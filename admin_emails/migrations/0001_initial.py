# Generated by Django 4.2.9 on 2025-01-13 21:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Template Name')),
                ('subject', models.CharField(help_text='Can include variables like {user_name}', max_length=200, verbose_name='Subject Template')),
                ('content', models.TextField(help_text='HTML content. Can include variables like {user_name}', verbose_name='Content Template')),
                ('description', models.TextField(blank=True, help_text='Describe when to use this template', verbose_name='Description')),
                ('is_active', models.<PERSON><PERSON>anField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Email Template',
                'verbose_name_plural': 'Email Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AdminEmail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custom_emails', models.TextField(blank=True, help_text='Enter email addresses separated by commas (e.g., <EMAIL>, <EMAIL>)', verbose_name='Custom Email Addresses')),
                ('subject', models.CharField(help_text='Email subject line', max_length=200, verbose_name='Subject')),
                ('template_choice', models.CharField(choices=[('custom', 'Custom Message'), ('announcement', 'Announcement'), ('maintenance', 'Maintenance Notice'), ('feature_update', 'Feature Update'), ('promotion', 'Promotion')], default='custom', help_text='Choose a template or select "Custom Message" to write your own', max_length=50, verbose_name='Template Type')),
                ('message', models.TextField(help_text='Email message content (HTML supported)', verbose_name='Message')),
                ('send_copy_to_admin', models.BooleanField(default=True, help_text='Send a copy of this email to the admin email address', verbose_name='Send Copy to Admin')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('send_to_users', models.ManyToManyField(blank=True, help_text='Select users from the database to send email to', to=settings.AUTH_USER_MODEL, verbose_name='Send to Users')),
                ('sent_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sent_admin_emails', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Admin Email',
                'verbose_name_plural': 'Send Admin Emails',
                'permissions': [('can_send_bulk_emails', 'Can send bulk emails to users')],
                'managed': False,
            },
        ),
    ]
