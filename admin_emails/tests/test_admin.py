from django.test import TestCase
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from admin_emails.admin import AdminEmailForm
from admin_emails.models import AdminEmail

User = get_user_model()


class AdminEmailFormTest(TestCase):
    """Test cases for AdminEmailForm"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()

        # Create test users with different scenarios
        self.user_with_full_name = User.objects.create_user(
            email='<EMAIL>',
            full_name='<PERSON>',
            password='testpass123'
        )

        self.user_without_full_name = User.objects.create_user(
            email='<EMAIL>',
            full_name='',
            password='testpass123'
        )

        self.user_with_none_full_name = User.objects.create_user(
            email='<EMAIL>',
            full_name=None,
            password='testpass123'
        )

    def test_form_initialization(self):
        """Test that the form initializes without errors"""
        form = AdminEmailForm()
        self.assertIsNotNone(form)
        self.assertIn('send_to_users', form.fields)

    def test_label_from_instance_with_full_name(self):
        """Test label display when user has full name"""
        form = AdminEmailForm()
        label = form.fields['send_to_users'].label_from_instance(self.user_with_full_name)
        self.assertEqual(label, "John Doe (<EMAIL>)")

    def test_label_from_instance_without_full_name(self):
        """Test label display when user has empty full name"""
        form = AdminEmailForm()
        label = form.fields['send_to_users'].label_from_instance(self.user_without_full_name)
        self.assertEqual(label, "<EMAIL> (<EMAIL>)")

    def test_label_from_instance_with_none_full_name(self):
        """Test label display when user has None as full name"""
        form = AdminEmailForm()
        label = form.fields['send_to_users'].label_from_instance(self.user_with_none_full_name)
        self.assertEqual(label, "<EMAIL> (<EMAIL>)")

    def test_send_to_users_queryset(self):
        """Test that the queryset only includes active users with email"""
        form = AdminEmailForm()
        queryset = form.fields['send_to_users'].queryset

        # All our test users should be in the queryset
        self.assertEqual(queryset.count(), 3)
        self.assertIn(self.user_with_full_name, queryset)
        self.assertIn(self.user_without_full_name, queryset)
        self.assertIn(self.user_with_none_full_name, queryset)

    def test_form_valid_data(self):
        """Test form with valid data"""
        form_data = {
            'send_to_users': [self.user_with_full_name.id],
            'custom_emails': '<EMAIL>',
            'subject': 'Test Email',
            'template_choice': 'custom',
            'message': '<p>Test message</p>',
            'send_copy_to_admin': True
        }
        form = AdminEmailForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_empty_recipients(self):
        """Test form with no recipients"""
        form_data = {
            'send_to_users': [],
            'custom_emails': '',
            'subject': 'Test Email',
            'template_choice': 'custom',
            'message': '<p>Test message</p>',
            'send_copy_to_admin': False
        }
        form = AdminEmailForm(data=form_data)
        # Form should still be valid as both recipient fields are optional
        self.assertTrue(form.is_valid())

    def test_template_choices(self):
        """Test that template choices are available"""
        form = AdminEmailForm()
        template_field = form.fields['template_choice']
        choices = [choice[0] for choice in template_field.choices]

        expected_choices = ['custom', 'announcement', 'maintenance', 'feature_update', 'promotion']
        for choice in expected_choices:
            self.assertIn(choice, choices)
