#!/usr/bin/env python
"""
Simple script to verify the AdminEmailForm fix for the username AttributeError.
This script creates mock users and tests the label_from_instance method.
"""

import os
import sys
import django
from django.conf import settings

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rentie.settings')
django.setup()

from django.contrib.auth import get_user_model
from admin_emails.admin import AdminEmailForm

User = get_user_model()


class MockUser:
    """Mock user object to test label_from_instance without database"""
    def __init__(self, email, full_name=None):
        self.email = email
        self.full_name = full_name
        self.id = 1
        self.is_active = True


def test_label_from_instance():
    """Test the label_from_instance method with different user scenarios"""
    print("Testing AdminEmailForm label_from_instance fix...")
    print("-" * 50)

    # Create form instance
    form = AdminEmailForm()

    # Test cases
    test_cases = [
        {
            "name": "User with full name",
            "user": MockUser(email="<EMAIL>", full_name="John Doe"),
            "expected": "John Doe (<EMAIL>)"
        },
        {
            "name": "User with empty full name",
            "user": MockUser(email="<EMAIL>", full_name=""),
            "expected": "<EMAIL> (<EMAIL>)"
        },
        {
            "name": "User with None full name",
            "user": MockUser(email="<EMAIL>", full_name=None),
            "expected": "<EMAIL> (<EMAIL>)"
        },
        {
            "name": "User with whitespace full name",
            "user": MockUser(email="<EMAIL>", full_name="   "),
            "expected": "    (<EMAIL>)"  # Will show whitespace as-is
        }
    ]

    all_passed = True

    for test in test_cases:
        try:
            label = form.fields['send_to_users'].label_from_instance(test['user'])
            passed = label == test['expected']

            print(f"\nTest: {test['name']}")
            print(f"  User email: {test['user'].email}")
            print(f"  User full_name: {repr(test['user'].full_name)}")
            print(f"  Expected: {test['expected']}")
            print(f"  Got: {label}")
            print(f"  Result: {'✓ PASSED' if passed else '✗ FAILED'}")

            if not passed:
                all_passed = False

        except Exception as e:
            print(f"\nTest: {test['name']}")
            print(f"  Result: ✗ FAILED with exception")
            print(f"  Error: {type(e).__name__}: {str(e)}")
            all_passed = False

    print("\n" + "-" * 50)
    if all_passed:
        print("✓ All tests passed! The fix is working correctly.")
        print("The AttributeError 'User' object has no attribute 'username' should be resolved.")
    else:
        print("✗ Some tests failed. Please check the implementation.")

    return all_passed


def test_form_initialization():
    """Test that the form can be initialized without errors"""
    print("\nTesting form initialization...")
    print("-" * 50)

    try:
        form = AdminEmailForm()
        print("✓ Form initialized successfully")
        print(f"  Available fields: {', '.join(form.fields.keys())}")

        # Check that label_from_instance is properly set
        send_to_users_field = form.fields.get('send_to_users')
        if send_to_users_field and hasattr(send_to_users_field, 'label_from_instance'):
            print("✓ label_from_instance method is properly configured")
        else:
            print("✗ label_from_instance method is not configured")

        return True
    except Exception as e:
        print(f"✗ Form initialization failed: {type(e).__name__}: {str(e)}")
        return False


if __name__ == "__main__":
    print("AdminEmailForm Username Fix Verification")
    print("=" * 50)

    # First test form initialization
    init_success = test_form_initialization()

    # Then test label_from_instance
    if init_success:
        label_success = test_label_from_instance()

        if init_success and label_success:
            print("\n✓ All verifications passed!")
            sys.exit(0)
        else:
            print("\n✗ Some verifications failed.")
            sys.exit(1)
    else:
        print("\n✗ Form initialization failed. Cannot proceed with other tests.")
        sys.exit(1)
