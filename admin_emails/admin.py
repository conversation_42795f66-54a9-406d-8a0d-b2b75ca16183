from django.contrib import admin
from django.contrib.auth import get_user_model
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.html import format_html_join
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import path
from django.http import HttpResponseRedirect
from django import forms
from django.db import models
from django.utils.translation import gettext_lazy as _

from .models import AdminEmail, EmailTemplate
from services.email_service import get_email_service

User = get_user_model()


class AdminEmailForm(forms.ModelForm):
    """Custom form for AdminEmail with enhanced widgets"""

    send_to_users = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(is_active=True).exclude(email='').exclude(email__isnull=True),
        required=False,
        widget=admin.widgets.FilteredSelectMultiple(
            verbose_name=_('Users with Email'),
            is_stacked=False
        ),
        help_text=_('Only active users with valid email addresses are shown')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Override the label_from_instance to show both name and email
        self.fields['send_to_users'].label_from_instance = lambda obj: f"{obj.full_name or obj.email} ({obj.email})"

    def clean(self):
        """Custom validation for the form"""
        cleaned_data = super().clean()
        send_to_users = cleaned_data.get('send_to_users')
        custom_emails = cleaned_data.get('custom_emails')

        print(f"DEBUG: send_to_users = {send_to_users}")
        print(f"DEBUG: custom_emails = {custom_emails}")
        print(f"DEBUG: send_to_users type = {type(send_to_users)}")

        # Check if at least one recipient is specified
        has_recipients = False

        if send_to_users and len(send_to_users) > 0:
            has_recipients = True
            print(f"DEBUG: Found {len(send_to_users)} selected users")

        if custom_emails and custom_emails.strip():
            has_recipients = True
            print(f"DEBUG: Found custom emails: {custom_emails}")

        if not has_recipients:
            print("DEBUG: No recipients found, raising validation error")
            raise forms.ValidationError(_('Please select at least one user or enter custom email addresses.'))

        # Validate custom emails if provided
        if custom_emails:
            from services.email_service import get_email_service
            email_service = get_email_service()

            emails = [email.strip() for email in custom_emails.split(',') if email.strip()]
            for email in emails:
                if not email_service.validate_email(email):
                    raise forms.ValidationError(_(f'Invalid email address: {email}'))

        print("DEBUG: Validation passed!")
        return cleaned_data

    class Meta:
        model = AdminEmail
        fields = ['send_to_users', 'custom_emails', 'subject', 'template_choice', 'message', 'send_copy_to_admin']
        widgets = {
            'custom_emails': forms.Textarea(attrs={'rows': 3, 'placeholder': '<EMAIL>, <EMAIL>'}),
            'subject': forms.TextInput(attrs={'class': 'vTextField', 'style': 'width: 100%'}),
            'custom_emails': forms.Textarea(attrs={'rows': 3, 'class': 'vLargeTextField', 'placeholder': '<EMAIL>, <EMAIL>'}),
            'message': forms.Textarea(attrs={'rows': 10, 'class': 'vLargeTextField'}),
            'template_choice': forms.Select(attrs={'class': 'vSelect'}),
        }

    # Remove custom JavaScript that's causing problems
    class Media:
        pass

    def get_template_content(self, template_type):
        """Get predefined template content"""
        templates = {
            'announcement': """
<h2>Important Announcement</h2>
<p>Dear User,</p>
<p>[Your announcement here]</p>
<p>Best regards,<br>The Rentie Team</p>
            """,
            'maintenance': """
<h2>Scheduled Maintenance Notice</h2>
<p>Dear User,</p>
<p>We will be performing scheduled maintenance on our platform:</p>
<ul>
    <li><strong>Date:</strong> [Date]</li>
    <li><strong>Time:</strong> [Time]</li>
    <li><strong>Duration:</strong> [Duration]</li>
</ul>
<p>During this time, the service may be temporarily unavailable. We apologize for any inconvenience.</p>
<p>Best regards,<br>The Rentie Team</p>
            """,
            'feature_update': """
<h2>New Features Available!</h2>
<p>Dear User,</p>
<p>We're excited to announce new features on Rentie:</p>
<ul>
    <li>[Feature 1]</li>
    <li>[Feature 2]</li>
    <li>[Feature 3]</li>
</ul>
<p>Log in to explore these new features!</p>
<p>Best regards,<br>The Rentie Team</p>
            """,
            'promotion': """
<h2>Special Promotion</h2>
<p>Dear User,</p>
<p>[Promotion details here]</p>
<p>Don't miss out on this limited-time offer!</p>
<p>Best regards,<br>The Rentie Team</p>
            """
        }
        return templates.get(template_type, '')


@admin.register(AdminEmail)
class AdminEmailAdmin(admin.ModelAdmin):
    """Admin interface for sending emails"""

    form = AdminEmailForm
    save_on_top = False
    save_as = False

    # Override to prevent actual database operations
    def has_add_permission(self, request):
        return request.user.has_perm('admin_emails.can_send_bulk_emails') or request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    fieldsets = (
        (_('Recipients'), {
            'fields': ('send_to_users', 'custom_emails'),
            'description': _('Select users from the database (only users with email addresses are shown) or enter custom email addresses.')
        }),
        (_('Email Content'), {
            'fields': ('subject', 'template_choice', 'message'),
            'description': _('Compose your email. You can use HTML formatting.')
        }),
        (_('Options'), {
            'fields': ('send_copy_to_admin',),
        }),
    )

    def get_urls(self):
        """Add custom URLs for preview and send actions"""
        urls = super().get_urls()
        custom_urls = [
            path('preview/', self.admin_site.admin_view(self.preview_email), name='admin_emails_adminemail_preview'),
            path('send/', self.admin_site.admin_view(self.send_email_view), name='admin_emails_adminemail_send'),
        ]
        return custom_urls + urls

    def preview_email(self, request):
        """Preview email before sending"""
        if request.method == 'POST':
            form = AdminEmailForm(request.POST)
            if form.is_valid():
                # For now, just send the email directly
                email = form.save(commit=False)
                email.sent_by = request.user
                success, message = email.send_email(request)

                if success:
                    messages.success(request, _('Email sent successfully!'))
                else:
                    messages.error(request, _(f'Failed to send email: {message}'))

                return HttpResponseRedirect('../add/')

        return HttpResponseRedirect('../add/')

    def send_email_view(self, request):
        """Handle email sending"""
        if request.method == 'POST':
            form = AdminEmailForm(request.POST)
            if form.is_valid():
                email = form.save(commit=False)
                email.sent_by = request.user

                # Send the email
                success, message = email.send_email(request)

                if success:
                    # Redirect to changelist with success message
                    return HttpResponseRedirect('../')
                else:
                    # Show error and stay on form
                    messages.error(request, _(f'Failed to send email: {message}'))
                    return HttpResponseRedirect('../')

        return HttpResponseRedirect('../')

    def has_add_permission(self, request):
        """Check if user can send emails"""
        return request.user.has_perm('admin_emails.can_send_bulk_emails') or request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        """No change permission since emails aren't saved"""
        return False

    def has_delete_permission(self, request, obj=None):
        """No delete permission since emails aren't saved"""
        return False

    def changelist_view(self, request, extra_context=None):
        """Redirect to add form since we don't have a list"""
        return HttpResponseRedirect('add/')

    def response_add(self, request, obj, post_url_continue=None):
        """Handle form submission"""
        if '_save' in request.POST:
            # Get form data directly from request instead of using the obj
            subject = request.POST.get('subject', '')
            message = request.POST.get('message', '')
            template_choice = request.POST.get('template_choice', 'custom')
            custom_emails = request.POST.get('custom_emails', '')
            send_copy_to_admin = request.POST.get('send_copy_to_admin') == 'on'

            # Get selected user IDs and their emails
            selected_user_ids = request.POST.getlist('send_to_users')
            all_emails = []

            # Add custom emails
            if custom_emails:
                emails = [email.strip() for email in custom_emails.split(',') if email.strip()]
                all_emails.extend(emails)

            # Add selected user emails
            if selected_user_ids:
                selected_users = User.objects.filter(id__in=selected_user_ids)
                user_emails = [user.email for user in selected_users if user.email]
                all_emails.extend(user_emails)

            # Send email
            if all_emails and subject and message:
                from services.email_service import get_email_service
                email_service = get_email_service()

                context = {
                    'subject': subject,
                    'message': message,
                    'template_type': template_choice,
                }

                success, result_message = email_service.send_templated_email(
                    to_emails=all_emails,
                    template_name='admin_email.html',
                    context=context
                )

                if success:
                    self.message_user(request, f'Email sent successfully to {len(all_emails)} recipients!', messages.SUCCESS)
                else:
                    self.message_user(request, f'Failed to send email: {result_message}', messages.ERROR)
            else:
                self.message_user(request, 'Please fill in subject, message and select recipients.', messages.ERROR)

        return HttpResponseRedirect(request.path)

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        """Override to add custom context"""
        extra_context = extra_context or {}
        extra_context['show_save'] = True
        extra_context['show_save_and_continue'] = False
        extra_context['show_save_and_add_another'] = False
        extra_context['show_delete'] = False
        extra_context['title'] = _('Send Email to Users')

        return super().changeform_view(request, object_id, form_url, extra_context)

    def changelist_view(self, request, extra_context=None):
        """Redirect to add form since we don't have a list"""
        return HttpResponseRedirect('add/')

    def save_model(self, request, obj, form, change):
        """Override to prevent saving to database"""
        # Don't save anything - handled in add_view
        pass

    def get_actions(self, request):
        """Remove default actions"""
        actions = super().get_actions(request)
        if 'delete_selected' in actions:
            del actions['delete_selected']
        return actions


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    """Admin interface for email templates"""

    list_display = ['name', 'subject', 'is_active', 'created_at', 'preview_button']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'subject', 'content']

    fieldsets = (
        (None, {
            'fields': ('name', 'subject', 'content', 'description', 'is_active')
        }),
    )

    readonly_fields = ['created_at', 'updated_at', 'preview_content']

    def preview_button(self, obj):
        """Show preview button in list"""
        return format_html(
            '<a class="button" href="#" onclick="alert(\'Preview: {}\\n\\n{}\'); return false;">Preview</a>',
            obj.subject,
            obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
        )
    preview_button.short_description = 'Preview'

    def preview_content(self, obj):
        """Show HTML preview of content"""
        if obj.content:
            return mark_safe(
                f'<div style="border: 1px solid #ddd; padding: 10px; max-width: 600px; '
                f'background: white; margin-top: 10px;">{obj.content}</div>'
            )
        return "No content"
    preview_content.short_description = "Content Preview"

    def get_fieldsets(self, request, obj=None):
        """Add preview field when editing existing template"""
        fieldsets = super().get_fieldsets(request, obj)
        if obj:  # Editing existing object
            fieldsets = list(fieldsets)
            fieldsets.append(
                (_('Preview'), {
                    'fields': ('preview_content', 'created_at', 'updated_at'),
                    'classes': ('collapse',),
                })
            )
        return fieldsets

    actions = ['duplicate_templates']

    def duplicate_templates(self, request, queryset):
        """Duplicate selected templates"""
        for template in queryset:
            template.pk = None
            template.name = f"{template.name} (Copy)"
            template.save()

        self.message_user(
            request,
            _(f'{queryset.count()} template(s) duplicated successfully.')
        )
    duplicate_templates.short_description = _('Duplicate selected templates')
