/* Admin Email Form Styling */

/* Ensure form inputs have proper borders */
.form-row input[type="text"],
.form-row input[type="email"],
.form-row textarea,
.form-row select {
    border: 1px solid #ddd !important;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.form-row input[type="text"]:focus,
.form-row textarea:focus,
.form-row select:focus {
    border-color: #417690 !important;
    outline: none;
    box-shadow: 0 0 0 2px rgba(65, 118, 144, 0.1);
}

/* Style the user selector widget */
.selector {
    width: 100% !important;
    max-width: 800px;
}

.selector select {
    min-height: 200px !important;
    border: 1px solid #ddd !important;
}

.selector-available h2,
.selector-chosen h2 {
    background: #417690 !important;
    color: white !important;
    padding: 10px !important;
    margin: 0 !important;
    font-size: 13px !important;
    border-radius: 4px 4px 0 0;
}

.selector-available h2 {
    background: #79aec8 !important;
}

/* Style the filter inputs */
.selector-filter {
    padding: 8px !important;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-top: none;
}

.selector-filter input {
    width: 100% !important;
    padding: 5px 8px !important;
    border: 1px solid #ccc !important;
    border-radius: 3px;
}

/* Style the submit row */
.submit-row {
    background: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    padding: 15px 20px !important;
    margin: 20px 0 !important;
    position: sticky;
    bottom: 20px;
    z-index: 10;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Style the send button */
.submit-row input[type="submit"][name="_save"] {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
    font-weight: bold !important;
    font-size: 16px !important;
    padding: 12px 40px !important;
    border-radius: 4px !important;
    cursor: pointer;
    transition: all 0.2s;
}

.submit-row input[type="submit"][name="_save"]:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Add recipient count display */
.submit-row::before {
    content: attr(data-recipient-count);
    font-size: 14px;
    font-weight: bold;
    color: #666;
    padding-right: 20px;
}

/* Style the help text */
.help, p.help {
    font-size: 12px !important;
    color: #666 !important;
    margin-top: 5px !important;
    line-height: 1.4;
}

/* Style fieldset descriptions */
.description {
    padding: 10px 15px;
    background: #f0f7ff;
    border-left: 4px solid #417690;
    margin: 10px 0 20px 0;
    font-size: 14px;
    color: #333;
}

/* Template choice styling */
#id_template_choice {
    max-width: 300px;
}

/* Message textarea styling */
#id_message {
    font-family: monospace;
    min-height: 300px;
    line-height: 1.5;
}

/* Custom emails textarea */
#id_custom_emails {
    min-height: 80px;
}

/* Improve label styling */
.form-row label {
    font-weight: bold !important;
    color: #333 !important;
    margin-bottom: 8px !important;
    display: inline-block;
}

.required label::after {
    content: " *";
    color: #dc3545;
}

/* Add visual feedback for recipient selection */
.form-row.field-send_to_users {
    background: #fafafa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* Style error messages */
.errorlist {
    margin: 5px 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.errorlist li {
    background: #fee;
    border: 1px solid #fcc;
    border-radius: 3px;
    padding: 8px 12px;
    margin: 5px 0;
    color: #c00;
    font-size: 13px;
}

/* Add recipient counter styling */
.recipient-counter {
    position: fixed;
    bottom: 100px;
    right: 30px;
    background: #417690;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 100;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .selector {
        display: block !important;
    }

    .selector-available,
    .selector-chosen {
        width: 100% !important;
        margin-bottom: 10px;
    }

    .submit-row {
        position: relative;
        flex-direction: column;
        gap: 10px;
    }
}

/* Override unfold theme if necessary */
.unfold .form-row input[type="text"],
.unfold .form-row textarea,
.unfold .form-row select {
    border: 1px solid #ddd !important;
}

/* Add loading state for send button */
.submit-row input[type="submit"][name="_save"]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Add animation for email sending */
@keyframes sendEmail {
    0% { transform: translateX(0); }
    50% { transform: translateX(10px); }
    100% { transform: translateX(0); }
}

.sending-email .submit-row input[type="submit"][name="_save"] {
    animation: sendEmail 0.5s ease-in-out;
}
