(function($) {
    'use strict';

    $(document).ready(function() {
        // Template definitions
        const emailTemplates = {
            'announcement': {
                subject: 'Important Announcement from <PERSON><PERSON>',
                message: '<h2>Important Announcement</h2>\n<p>Dear User,</p>\n<p>[Your announcement here]</p>\n<p>Best regards,<br>The Rentie Team</p>'
            },
            'maintenance': {
                subject: 'Scheduled Maintenance Notice',
                message: '<h2>Scheduled Maintenance Notice</h2>\n<p>Dear User,</p>\n<p>We will be performing scheduled maintenance on our platform:</p>\n<ul>\n    <li><strong>Date:</strong> [Date]</li>\n    <li><strong>Time:</strong> [Time]</li>\n    <li><strong>Duration:</strong> [Duration]</li>\n</ul>\n<p>During this time, the service may be temporarily unavailable. We apologize for any inconvenience.</p>\n<p>Best regards,<br>The Rentie Team</p>'
            },
            'feature_update': {
                subject: 'New Features Available on Ren<PERSON>!',
                message: '<h2>New Features Available!</h2>\n<p>Dear User,</p>\n<p>We\'re excited to announce new features on Rentie:</p>\n<ul>\n    <li>[Feature 1]</li>\n    <li>[Feature 2]</li>\n    <li>[Feature 3]</li>\n</ul>\n<p>Log in to explore these new features!</p>\n<p>Best regards,<br>The Rentie Team</p>'
            },
            'promotion': {
                subject: 'Special Promotion - Limited Time Offer!',
                message: '<h2>Special Promotion</h2>\n<p>Dear User,</p>\n<p>[Promotion details here]</p>\n<p>Don\'t miss out on this limited-time offer!</p>\n<p>Best regards,<br>The Rentie Team</p>'
            }
        };

        // Cache DOM elements
        const $templateSelect = $('#id_template_choice');
        const $messageField = $('#id_message');
        const $subjectField = $('#id_subject');
        const $customEmails = $('#id_custom_emails');
        const $submitRow = $('.submit-row');
        const $sendButton = $('input[type="submit"][name="_save"]');

        // Store custom message when switching templates
        let customMessage = '';
        let customSubject = '';

        // Handle template selection
        $templateSelect.on('change', function() {
            const selectedTemplate = $(this).val();

            if (selectedTemplate === 'custom') {
                // Restore custom message
                $messageField.val(customMessage);
                $subjectField.val(customSubject);
                return;
            }

            // Save current content if switching from custom
            if ($templateSelect.data('previous') === 'custom') {
                customMessage = $messageField.val();
                customSubject = $subjectField.val();
            }

            // Load template content
            if (emailTemplates[selectedTemplate]) {
                $subjectField.val(emailTemplates[selectedTemplate].subject);
                $messageField.val(emailTemplates[selectedTemplate].message);
            }

            // Store current selection
            $templateSelect.data('previous', selectedTemplate);
        });

        // Initialize previous value
        $templateSelect.data('previous', $templateSelect.val());

        // Function to count recipients
        function updateRecipientCount() {
            let count = 0;

            // Count selected users in the "chosen" select box
            const $chosenSelect = $('#id_send_to_users_to');
            if ($chosenSelect.length) {
                count += $chosenSelect.find('option').length;
            }

            // Count custom emails
            const customEmailsValue = $customEmails.val();
            if (customEmailsValue) {
                const emails = customEmailsValue.split(',')
                    .map(email => email.trim())
                    .filter(email => email.length > 0 && email.includes('@'));
                count += emails.length;
            }

            // Update display
            updateRecipientDisplay(count);

            // Enable/disable send button based on recipient count
            if (count === 0) {
                $sendButton.prop('disabled', true)
                    .attr('title', 'Please select at least one recipient');
            } else {
                $sendButton.prop('disabled', false)
                    .attr('title', `Send email to ${count} recipient${count !== 1 ? 's' : ''}`);
            }

            return count;
        }

        // Function to update recipient display
        function updateRecipientDisplay(count) {
            // Update submit row data attribute
            $submitRow.attr('data-recipient-count', `Total recipients: ${count}`);

            // Update any recipient counter element
            $('.recipient-counter').text(`${count} recipient${count !== 1 ? 's' : ''}`);

            // Add floating counter if it doesn't exist
            if (!$('#floating-recipient-counter').length && count > 0) {
                $('body').append(
                    `<div id="floating-recipient-counter" class="recipient-counter">
                        ${count} recipient${count !== 1 ? 's' : ''}
                    </div>`
                );
            } else if (count > 0) {
                $('#floating-recipient-counter').text(`${count} recipient${count !== 1 ? 's' : ''}`).show();
            } else {
                $('#floating-recipient-counter').hide();
            }
        }

        // Monitor changes to user selection
        // Watch for changes in the select boxes (for FilteredSelectMultiple widget)
        $(document).on('change', '#id_send_to_users_to, #id_send_to_users_from', function() {
            setTimeout(updateRecipientCount, 100);
        });

        // Monitor the "add" and "remove" button clicks
        $(document).on('click', '.selector-add, .selector-remove', function() {
            setTimeout(updateRecipientCount, 100);
        });

        // Monitor custom emails input
        $customEmails.on('input keyup paste', function() {
            updateRecipientCount();
        });

        // Add visual feedback when typing custom emails
        $customEmails.on('input', function() {
            const emails = $(this).val().split(',').map(e => e.trim()).filter(e => e);
            const validEmails = emails.filter(email => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email));
            const invalidEmails = emails.length - validEmails.length;

            if (invalidEmails > 0) {
                $(this).css('border-color', '#ff6b6b');
            } else if (emails.length > 0) {
                $(this).css('border-color', '#51cf66');
            } else {
                $(this).css('border-color', '');
            }
        });

        // Initial count
        setTimeout(updateRecipientCount, 500);

        // Add confirmation with recipient count before sending
        $sendButton.on('click', function(e) {
            const count = updateRecipientCount();

            if (count === 0) {
                e.preventDefault();
                alert('Please select at least one recipient before sending the email.');
                return false;
            }

            const message = `Are you sure you want to send this email to ${count} recipient${count !== 1 ? 's' : ''}?`;
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }

            // Add sending animation
            $(this).addClass('sending-email');
        });

        // Auto-save draft to localStorage
        let draftTimeout;
        function saveDraft() {
            const draft = {
                subject: $subjectField.val(),
                message: $messageField.val(),
                template: $templateSelect.val(),
                customEmails: $customEmails.val(),
                timestamp: new Date().toISOString()
            };
            localStorage.setItem('adminEmailDraft', JSON.stringify(draft));
        }

        // Restore draft if available
        function restoreDraft() {
            const draftStr = localStorage.getItem('adminEmailDraft');
            if (draftStr) {
                try {
                    const draft = JSON.parse(draftStr);
                    const draftAge = new Date() - new Date(draft.timestamp);

                    // Only restore if draft is less than 24 hours old
                    if (draftAge < 24 * 60 * 60 * 1000) {
                        if (confirm('A draft email was found. Would you like to restore it?')) {
                            $subjectField.val(draft.subject);
                            $messageField.val(draft.message);
                            $templateSelect.val(draft.template);
                            $customEmails.val(draft.customEmails);
                            updateRecipientCount();
                        }
                    } else {
                        // Clear old draft
                        localStorage.removeItem('adminEmailDraft');
                    }
                } catch (e) {
                    console.error('Error restoring draft:', e);
                }
            }
        }

        // Save draft on input
        $subjectField.add($messageField).add($customEmails).on('input', function() {
            clearTimeout(draftTimeout);
            draftTimeout = setTimeout(saveDraft, 1000);
        });

        // Clear draft after successful send
        $(document).on('submit', 'form', function() {
            if ($sendButton.prop('disabled') === false) {
                localStorage.removeItem('adminEmailDraft');
            }
        });

        // Restore draft on page load
        restoreDraft();

        // Add keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + Enter to send
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
                e.preventDefault();
                $sendButton.click();
            }
        });

        // Add character counter for subject
        $subjectField.after('<div class="char-counter" style="text-align: right; font-size: 12px; color: #666; margin-top: 2px;"></div>');
        $subjectField.on('input', function() {
            const length = $(this).val().length;
            const maxLength = 200;
            $(this).next('.char-counter').text(`${length}/${maxLength} characters`);

            if (length > maxLength) {
                $(this).next('.char-counter').css('color', '#dc3545');
            } else {
                $(this).next('.char-counter').css('color', '#666');
            }
        });

        // Initialize character counter
        $subjectField.trigger('input');
    });
})(django.jQuery || jQuery);
