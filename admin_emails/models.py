from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
import json

User = get_user_model()


class AdminEmailManager(models.Manager):
    """Custom manager for AdminEmail that doesn't use database"""

    def get_queryset(self):
        # Return empty queryset since we don't store emails in DB
        return super().get_queryset().none()


class AdminEmail(models.Model):
    """
    Proxy model for sending emails from admin panel.
    This model doesn't create a database table.
    """

    # Email composition fields
    send_to_users = models.ManyToManyField(
        User,
        blank=True,
        verbose_name=_('Send to Users'),
        help_text=_('Select users from the database to send email to')
    )

    custom_emails = models.TextField(
        blank=True,
        verbose_name=_('Custom Email Addresses'),
        help_text=_('Enter email addresses separated by commas (e.g., <EMAIL>, <EMAIL>)')
    )

    subject = models.CharField(
        max_length=200,
        verbose_name=_('Subject'),
        help_text=_('Email subject line')
    )

    template_choice = models.CharField(
        max_length=50,
        choices=[
            ('custom', 'Custom Message'),
            ('announcement', 'Announcement'),
            ('maintenance', 'Maintenance Notice'),
            ('feature_update', 'Feature Update'),
            ('promotion', 'Promotion'),
        ],
        default='custom',
        verbose_name=_('Template Type'),
        help_text=_('Choose a template or select "Custom Message" to write your own')
    )

    message = models.TextField(
        verbose_name=_('Message'),
        help_text=_('Email message content (HTML supported)')
    )

    send_copy_to_admin = models.BooleanField(
        default=True,
        verbose_name=_('Send Copy to Admin'),
        help_text=_('Send a copy of this email to the admin email address')
    )

    # Metadata fields (not saved to DB)
    created_at = models.DateTimeField(auto_now_add=True)
    sent_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sent_admin_emails'
    )

    objects = AdminEmailManager()

    class Meta:
        managed = False  # Don't create database table
        verbose_name = _('Admin Email')
        verbose_name_plural = _('Send Admin Emails')
        permissions = [
            ('can_send_bulk_emails', 'Can send bulk emails to users'),
        ]

    def __str__(self):
        return f"Email: {self.subject}"

    def clean(self):
        """Validate email data before sending"""
        # Skip recipient validation in model clean - it's handled in form clean
        pass

        # Validate custom emails
        if self.custom_emails:
            from services.email_service import get_email_service
            email_service = get_email_service()

            emails = [email.strip() for email in self.custom_emails.split(',') if email.strip()]
            for email in emails:
                if not email_service.validate_email(email):
                    raise ValidationError({
                        'custom_emails': _(f'Invalid email address: {email}')
                    })

        # Validate subject and message
        if not self.subject:
            raise ValidationError({'subject': _('Subject is required.')})

        if not self.message:
            raise ValidationError({'message': _('Message is required.')})

    def get_recipient_emails(self):
        """Get all recipient email addresses"""
        emails = []

        # Get emails from selected users (only if object has been saved and has an ID)
        try:
            if self.pk and self.send_to_users.exists():
                user_emails = list(self.send_to_users.values_list('email', flat=True))
                emails.extend([email for email in user_emails if email])
        except (ValueError, AttributeError):
            # Handle case where object doesn't have an ID yet
            pass

        # Get custom emails
        if self.custom_emails:
            custom_emails = [email.strip() for email in self.custom_emails.split(',') if email.strip()]
            emails.extend(custom_emails)

        # Remove duplicates while preserving order
        seen = set()
        unique_emails = []
        for email in emails:
            if email not in seen:
                seen.add(email)
                unique_emails.append(email)

        return unique_emails

    def get_template_context(self):
        """Get context for email template"""
        from django.conf import settings

        return {
            'subject': self.subject,
            'message': self.message,
            'template_type': self.template_choice,
            'site_name': getattr(settings, 'SITE_NAME', 'Rentie'),
            'site_url': getattr(settings, 'SITE_URL', 'https://rentie.com'),
            'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
        }

    def send_email(self, request=None):
        """Send the email to all recipients"""
        from services.email_service import get_email_service
        from django.conf import settings
        from django.contrib import messages

        email_service = get_email_service()
        recipient_emails = self.get_recipient_emails()

        if not recipient_emails:
            if request:
                messages.error(request, _('No valid email addresses found.'))
            return False, 'No valid email addresses found.'

        # Add admin email if requested
        if self.send_copy_to_admin:
            admin_email = getattr(settings, 'ADMIN_EMAIL', settings.DEFAULT_FROM_EMAIL)
            if admin_email and admin_email not in recipient_emails:
                recipient_emails.append(admin_email)

        # Send email using the template
        success, message = email_service.send_templated_email(
            to_emails=recipient_emails,
            template_name='admin_email.html',
            context=self.get_template_context()
        )

        if success and request:
            messages.success(
                request,
                _(f'Email sent successfully to {len(recipient_emails)} recipient(s).')
            )
        elif not success and request:
            messages.error(request, _(f'Failed to send email: {message}'))

        return success, message

    def save(self, *args, **kwargs):
        """Override save to prevent actual database save"""
        # This model doesn't save to database
        pass

    def delete(self, *args, **kwargs):
        """Override delete since there's nothing to delete"""
        pass


class EmailTemplate(models.Model):
    """
    Store reusable email templates for admin use.
    This is a simple model that actually saves to database.
    """

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Template Name')
    )

    subject = models.CharField(
        max_length=200,
        verbose_name=_('Subject Template'),
        help_text=_('Can include variables like {user_name}')
    )

    content = models.TextField(
        verbose_name=_('Content Template'),
        help_text=_('HTML content. Can include variables like {user_name}')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('Description'),
        help_text=_('Describe when to use this template')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Active')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Email Template')
        verbose_name_plural = _('Email Templates')
        ordering = ['name']

    def __str__(self):
        return self.name
