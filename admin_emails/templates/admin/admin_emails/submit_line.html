{% load i18n admin_urls %}
<div class="submit-row">
    <span class="user-count" style="float: left; line-height: 40px; font-size: 14px; font-weight: bold; color: #666;">
        Total recipients: <span id="recipient-count">0</span>
    </span>

    <input type="submit" class="default" name="_save" value="{% trans 'Send Email Now' %}"
           style="background-color: #28a745; border-color: #28a745; color: white; font-weight: bold; font-size: 16px; padding: 10px 30px; border: none; border-radius: 4px; cursor: pointer;" />
</div>

<script>
django.jQuery(document).ready(function($) {
    // Update recipient count
    function updateRecipientCount() {
        let count = 0;

        // Count selected users
        const selectedUsers = $('#id_send_to_users_to option').length;
        count += selectedUsers;

        // Count custom emails
        const customEmails = $('#id_custom_emails').val();
        if (customEmails) {
            const emails = customEmails.split(',').filter(email => email.trim().length > 0);
            count += emails.length;
        }

        $('#recipient-count').text(count);

        // Update button state
        const sendButton = $('.submit-row button[name="_save"]');
        if (count === 0) {
            sendButton.prop('disabled', true).css('opacity', '0.6');
            sendButton.attr('title', '{% trans "Please select at least one recipient" %}');
        } else {
            sendButton.prop('disabled', false).css('opacity', '1');
            sendButton.attr('title', '');
        }
    }

    // Monitor changes
    $(document).on('DOMNodeInserted DOMNodeRemoved', '#id_send_to_users_to', updateRecipientCount);
    $('#id_custom_emails').on('input keyup paste', updateRecipientCount);

    // Initial count
    setTimeout(updateRecipientCount, 100);
});
</script>

<style>
.submit-row {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px 20px;
    margin: 20px 0;
    position: sticky;
    bottom: 0;
    z-index: 10;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}

.submit-row button[name="_save"]:hover:not(:disabled) {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
}

.submit-row button[name="_save"]:disabled {
    cursor: not-allowed;
}
</style>
