from django.db import models
from django.utils import timezone
from core.models import AbstractModel
from api.interest.models import Interest
from django.utils.translation import gettext_lazy as _
from django.contrib.gis.db import models as gis_models
from django.core.validators import RegexValidator, MinLengthValidator
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager

from rentie import settings

# TODO: Add a custom user admin model that will override the default user admin model and
# from django.contrib import admin
# from .models import User

# class UserAdmin(admin.ModelAdmin):
#     def get_form(self, request, obj=None, **kwargs):
#         form = super().get_form(request, obj, **kwargs)
#         if obj:  # Only for existing objects
#             form.base_fields['mobile_number'].required = False
#         return form

# admin.site.register(User, UserAdmin)


class UserManager(BaseUserManager):
  def _create_user(self, password, **kwargs):
    user = self.model(**kwargs)
    user.set_password(password)
    user.save(using=self._db)
    return user

  def create_user(self, password, **kwargs):
    kwargs["is_admin"] = False
    return self._create_user(password, **kwargs)

  def create_superuser(self, password, **kwargs):
    kwargs["is_admin"] = True
    return self._create_user(password, **kwargs)


class UserProfileImage(AbstractModel):
  user = models.ForeignKey(
    "User",
    on_delete=models.CASCADE,
    related_name="profile_images",
  )

  image = models.ImageField(
    upload_to="profile_images/",
  )

  class Meta:
    verbose_name = _("User Profile Image")
    verbose_name_plural = _("User Profile Images")


class User(AbstractModel, AbstractBaseUser):
  GENDER_CHOICES = (
    ("Male", _("Male")),
    ("Female", _("Female")),
    ("Lesbian", _("Lesbian")),
    ("Gay", _("Gay")),
    ("Transgender", _("Transgender")),
    ("Queer", _("Queer")),
  )

  mobile_number = models.CharField(
    _("Mobile Number"),
    validators=[MinLengthValidator(7)],
    db_index=True,
    max_length=15,
    unique=True,
    blank=True,
  )

  email = models.EmailField(
    _("Email"),
    max_length=128,
    db_index=True,
    unique=True,
    blank=True,
    null=True,
  )

  full_name = models.CharField(
    _("Full Name"),
    max_length=128,
    blank=True,
    null=True,
  )

  bio = models.TextField(
    blank=True,
    null=True,
  )

  otp_number = models.CharField(
    _("OTP Number"),
    validators=[RegexValidator(r"^\d{4}$", "OTP must be exactly 4 digits")],
    max_length=4,
    blank=True,
    null=True,
  )

  is_ask_me_on = models.BooleanField(
    _("Is Ask Me On"),
    default=False,
    blank=True,
  )

  is_notification_on = models.BooleanField(
    _("Is Notification On"),
    default=False,
    blank=True,
  )

  is_live_location_on = models.BooleanField(
    _("Is Live Location On"),
    default=False,
    blank=True,
  )

  dob = models.DateField(
    _("Date of Birth"),
    blank=True,
    null=True,
  )

  gender = models.CharField(
    _("Gender"),
    choices=GENDER_CHOICES,
    max_length=20,
    blank=True,
    null=True,
  )

  address = models.CharField(
    _("Address"),
    max_length=255,
    blank=True,
    null=True,
  )

  sparks_number = models.IntegerField(
    _("Sparks Number"),
    default=settings.STARTER_SPARK_NUMBER,
  )

  last_sparks_added_date = models.DateTimeField(
    _("Last Sparks Added Date"),
    default=timezone.now,
    blank=True,
    null=True,
  )

  fcm_token = models.CharField(
    _("FCM Token"),
    max_length=255,
    blank=True,
    null=True,
  )

  location = gis_models.PointField(
    _("Location"),
    null=True,
    blank=True,
    geography=True,  # Use geography for more accurate distance calculations
    spatial_index=True,  # Add spatial index for better query performance
  )

  last_activity = models.DateTimeField(
    _("Last Activity"),
    blank=True,
    default=timezone.now,
  )

  live_location = models.CharField(
    _("Live Location"),
    max_length=255,
    blank=True,
    null=True,
  )

  is_online = models.BooleanField(
    _("Is Online"),
    default=False,
    blank=True,
  )

  interests = models.ManyToManyField(
    Interest,
    related_name="users",
    blank=True,
    help_text="Interests selected by the user",
  )

  # Fields from legacy code related to invite/chat
  # These might need to be added to the User model as well based on legacy usage
  rentedLimit = models.PositiveIntegerField(
    _("Rented Limit"),
    default=0,  # Assuming this tracks how many times they've been 'rented' if not premium
  )

  oneTimeChat = models.PositiveIntegerField(
    _("One Time Chat Used"),
    default=0,  # Assuming 0 means available, 1 means used
  )

  # overwrite from abstract model because actual users do not enter password in the app
  password = models.CharField(
    _("Password"),
    max_length=128,
    blank=True,
    null=True,
  )

  is_active = models.BooleanField(
    _("Is Active"),
    help_text="Designates whether this user can access their account",
    default=True,
  )

  is_admin = models.BooleanField(
    _("Is Admin"),
    help_text="Designates whether this user is an admin",
    default=False,
  )

  USERNAME_FIELD = "email"

  objects = UserManager()

  def __str__(self):
    return self.mobile_number

  @property
  def is_staff(self):
    return self.is_admin

  @property
  def is_superuser(self):
    return self.is_admin

  def has_perm(self, perm, obj=None):
    return self.is_active and self.is_admin

  def has_module_perms(self, app_label):
    return self.is_active and self.is_admin

  def get_all_permissions(self):
    return []

  class Meta(AbstractModel.Meta):
    verbose_name = _("User")
    verbose_name_plural = _("Users")
