from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.gis.admin import OSMGeoAdmin
from unfold.admin import ModelAdmin
from access.models import User, UserProfileImage


class UserProfileImageInline(admin.TabularInline):
  model = UserProfileImage
  extra = 1


@admin.register(User)
class UserAdmin(ModelAdmin, OSMGeoAdmin, BaseUserAdmin):
  search_fields = ("email", "full_name", "mobile_number", "fcm_token")

  readonly_fields = (
    "id",
    "created_at",
    "updated_at",
    "last_sparks_added_date",
    "fcm_token",
    "rentedLimit",
    "oneTimeChat",
  )

  inlines = [UserProfileImageInline]

  list_display = (
    "id",
    "mobile_number",
    "email",
    "full_name",
    "gender",
    "sparks_number",
    "last_sparks_added_date",
    "rentedLimit",
    "oneTimeChat",
    "last_activity",
    "is_online",
    "is_active",
    "is_admin",
    "created_at",
  )

  list_filter = (
    "is_active",
    "is_admin",
    "last_activity",
    "is_online",
    "is_ask_me_on",
    "is_notification_on",
    "is_live_location_on",
    "gender",
    "sparks_number",
    "rentedLimit",
    "oneTimeChat",
    "last_sparks_added_date",
    "created_at",
  )

  filter_horizontal = ["interests"]

  date_hierarchy = "created_at"
  ordering = ("-created_at",)

  # GeoDjango specific options
  gis_widget_kwargs = {
    "attrs": {
      "default_zoom": 12,
      "default_lon": 0,
      "default_lat": 0,
      "url": "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    },
  }

  add_fieldsets = (
    (
      _("Details"),
      {
        "fields": [
          "mobile_number",
          "full_name",
          "bio",
          "email",
          "otp_number",
          "gender",
          "dob",
        ]
      },
    ),
    (
      _("Location Information"),
      {
        "fields": [
          "address",
          "location",
          "live_location",
        ],
      },
    ),
    (
      _("User Settings"),
      {
        "fields": [
          "is_ask_me_on",
          "is_notification_on",
          "is_live_location_on",
          "is_online",
        ],
      },
    ),
    (
      _("Interests"),
      {
        "fields": [
          "interests",
        ],
      },
    ),
    (
      _("Sparks & App Usage"),
      {
        "fields": [
          # sparks_number can be set initially
          "sparks_number",
          # last_sparks_added_date set by app logic, not here
          # rentedLimit set by app logic, not here
          # oneTimeChat set by app logic, not here
        ],
      },
    ),
    (
      _("Access"),
      {
        "fields": [
          "is_active",
          "is_admin",
        ],
      },
    ),
  )

  fieldsets = (
    (None, {"fields": ("id",)}),
    (
      _("Details"),
      {
        "fields": [
          "mobile_number",
          "full_name",
          "bio",
          "email",
          "otp_number",
          "gender",
          "dob",
          "fcm_token",
        ]
      },
    ),
    (
      _("Location Information"),
      {
        "fields": [
          "address",
          "location",
          "live_location",
        ],
      },
    ),
    (
      _("User Settings"),
      {
        "fields": [
          "is_ask_me_on",
          "is_notification_on",
          "is_live_location_on",
          "is_online",
        ],
      },
    ),
    (
      _("Interests"),
      {
        "fields": [
          "interests",
        ],
      },
    ),
    (
      _("Sparks & App Usage"),
      {
        "fields": [
          "sparks_number",
          "last_sparks_added_date",
          "rentedLimit",
          "oneTimeChat",
        ],
      },
    ),
    (
      _("Access"),
      {
        "fields": [
          "is_active",
          "is_admin",
          "password",
        ],
      },
    ),
    (
      _("Dates"),
      {
        "fields": [
          "created_at",
          "updated_at",
          "last_activity",
        ]
      },
    ),
  )

  class Media:
    css = {"all": ("https://unpkg.com/leaflet@1.9.3/dist/leaflet.css",)}
    js = ("https://unpkg.com/leaflet@1.9.3/dist/leaflet.js",)


@admin.register(UserProfileImage)
class UserProfileImageAdmin(ModelAdmin):
  list_display = ("id", "user", "image")
  list_filter = ("user",)
  search_fields = ("user__email", "user__full_name")
