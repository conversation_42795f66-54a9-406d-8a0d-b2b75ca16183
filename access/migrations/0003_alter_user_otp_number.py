# Generated by Django 4.2.9 on 2025-05-03 12:10

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('access', '0002_alter_user_mobile_number'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='otp_number',
            field=models.CharField(blank=True, max_length=4, null=True, validators=[django.core.validators.RegexValidator('^\\d{4}$', 'OTP must be exactly 4 digits')], verbose_name='OTP Number'),
        ),
    ]
