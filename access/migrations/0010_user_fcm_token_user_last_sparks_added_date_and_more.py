# Generated by Django 4.2.9 on 2025-05-05 13:52

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('access', '0009_alter_user_last_activity'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='fcm_token',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='FCM Token'),
        ),
        migrations.AddField(
            model_name='user',
            name='last_sparks_added_date',
            field=models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='Last Sparks Added Date'),
        ),
        migrations.AddField(
            model_name='user',
            name='oneTimeChat',
            field=models.PositiveIntegerField(default=0, verbose_name='One Time Chat Used'),
        ),
        migrations.AddField(
            model_name='user',
            name='rentedLimit',
            field=models.PositiveIntegerField(default=0, verbose_name='Rented Limit'),
        ),
    ]
