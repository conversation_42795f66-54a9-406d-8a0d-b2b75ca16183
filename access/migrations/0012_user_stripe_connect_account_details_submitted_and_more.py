# Generated by Django 4.2.9 on 2025-05-12 15:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('access', '0011_alter_user_location'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='stripe_connect_account_details_submitted',
            field=models.BooleanField(default=False, verbose_name='Stripe Connect Account Details Submitted'),
        ),
        migrations.AddField(
            model_name='user',
            name='stripe_connect_account_id',
            field=models.CharField(blank=True, help_text='Stripe Connect Account ID for users receiving payments.', max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='user',
            name='stripe_connect_account_verified',
            field=models.BooleanField(default=False, verbose_name='Stripe Connect Account Verified Status'),
        ),
    ]
