# Generated by Django 4.2.9 on 2025-05-01 16:38

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('mobile_number', models.CharField(db_index=True, max_length=15, unique=True, validators=[django.core.validators.MinLengthValidator(7)], verbose_name='Mobile Number')),
                ('email', models.EmailField(db_index=True, max_length=128, unique=True, verbose_name='Email')),
                ('full_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='Full Name')),
                ('bio', models.TextField(blank=True, null=True)),
                ('otp_number', models.CharField(blank=True, max_length=4, null=True, verbose_name='OTP Number')),
                ('is_ask_me_on', models.BooleanField(blank=True, default=False, verbose_name='Is Ask Me On')),
                ('is_notification_on', models.BooleanField(blank=True, default=False, verbose_name='Is Notification On')),
                ('is_live_location_on', models.BooleanField(blank=True, default=False, verbose_name='Is Live Location On')),
                ('dob', models.DateField(blank=True, null=True, verbose_name='Date of Birth')),
                ('gender', models.CharField(blank=True, choices=[('Male', 'Male'), ('Female', 'Female'), ('Lesbian', 'Lesbian'), ('Gay', 'Gay'), ('Transgender', 'Transgender'), ('Queer', 'Queer')], max_length=20, null=True, verbose_name='Gender')),
                ('address', models.CharField(blank=True, max_length=255, null=True, verbose_name='Address')),
                ('location', models.CharField(blank=True, max_length=255, null=True, verbose_name='Location')),
                ('live_location', models.CharField(blank=True, max_length=255, null=True, verbose_name='Live Location')),
                ('is_online', models.BooleanField(blank=True, default=False, verbose_name='Is Online')),
                ('hour_price', models.FloatField(blank=True, null=True, verbose_name='Hour Price')),
                ('dinner_price', models.FloatField(blank=True, null=True, verbose_name='Dinner Price')),
                ('full_day_price', models.FloatField(blank=True, null=True, verbose_name='Full Day Price')),
                ('password', models.CharField(blank=True, max_length=128, null=True, verbose_name='Password')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user can access their account', verbose_name='Is Active')),
                ('is_admin', models.BooleanField(default=False, help_text='Designates whether this user is an admin', verbose_name='Is Admin')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'ordering': ['-created_at'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='UserProfileImage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('image', models.ImageField(upload_to='profile_images/')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='profile_images', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile Image',
                'verbose_name_plural': 'User Profile Images',
            },
        ),
    ]
