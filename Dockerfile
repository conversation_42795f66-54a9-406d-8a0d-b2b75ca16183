FROM python:3.11-slim

# Sets /app as the working directory for all subsequent commands.
WORKDIR /app

# Prevents .pyc files from being created (cleaner container).
ENV PYTHONDONTWRITEBYTECODE 1

# Ensures logs are output immediately (important for real-time logging in Docker).
ENV PYTHONUNBUFFERED 1

# Set Python path to include the app root directory
ENV PYTHONPATH=/app:$PYTHONPATH

# Install essential system tools
# postgresql-client: Allows running psql if needed.
# libpq-dev: Required for installing psycopg2 (PostgreSQL Python adapter).
# build-essential: Needed to compile any native Python packages.
# netcat-traditional: Used by nc command in entrypoint.sh to check service availability.
# --no-install-recommends: Prevents unnecessary packages.
# Cleanup at the end (apt-get clean, rm -rf) to keep the image small.
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
        netcat-traditional \
        gdal-bin \
        libgdal-dev \
        binutils \
        libproj-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copies requirements.txt and installs Python dependencies.
COPY requirements.txt /app/
RUN pip install --upgrade pip \
    && pip install -r requirements.txt

# Copies full Django project into the image.
COPY . /app/

# Makes your entrypoint.sh script executable.
RUN chmod +x entrypoint.sh

# Create static directory for Django
RUN mkdir -p /app/static

# Creates a non-root user (appuser)
RUN useradd -m appuser

# Set proper permissions
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser
