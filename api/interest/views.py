from rest_framework import mixins
from rest_framework.generics import GenericAPIView, ListAPIView, RetrieveAPIView
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from drf_spectacular.utils import extend_schema, OpenApiResponse
from rest_framework.permissions import AllowAny
from api.consts.permissions import IsAdmin
from api.interest.querysets import ALL_INTERESTS_QUERYSET
from api.interest.serializers import InterestSerializer
from core.utils.app_response import AppPagination


@extend_schema(tags=["Interest"])
class InterestListView(ListAPIView):
  """List all interests - public endpoint"""

  queryset = ALL_INTERESTS_QUERYSET
  serializer_class = InterestSerializer
  pagination_class = AppPagination
  permission_classes = [AllowAny]


@extend_schema(tags=["Interest"])
class InterestRetrieveView(RetrieveAPIView):
  """Retrieve a single interest by ID - public endpoint"""

  queryset = ALL_INTERESTS_QUERYSET
  serializer_class = InterestSerializer
  permission_classes = [AllowAny]


@extend_schema(
  tags=["Interest"],
  summary="Create a new interest",
  description="Upload a new interest. The image must be sent as multipart form data.",
  request={
    "multipart/form-data": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "Name of the interest",
        },
        "image": {
          "type": "string",
          "format": "binary",
          "description": "Image file (optional)",
        },
      },
      "required": ["name"],
    }
  },
  responses={201: InterestSerializer},
)
class InterestCreateView(mixins.CreateModelMixin, GenericAPIView):
  serializer_class = InterestSerializer
  queryset = ALL_INTERESTS_QUERYSET
  permission_classes = [IsAdmin]
  parser_classes = [MultiPartParser, FormParser]

  def post(self, request, *args, **kwargs):
    return self.create(request, *args, **kwargs)


@extend_schema(
  tags=["Interest"],
  summary="Update an existing interest",
  description="Update interest fields. Only name or image can be updated. Multipart/form-data required.",
  request={
    "multipart/form-data": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "New name of the interest (optional)",
        },
        "image": {
          "type": "string",
          "format": "binary",
          "description": "New image file (optional)",
        },
      },
    }
  },
  responses={200: InterestSerializer},
)
class InterestUpdateView(mixins.UpdateModelMixin, GenericAPIView):
  serializer_class = InterestSerializer
  queryset = ALL_INTERESTS_QUERYSET
  permission_classes = [IsAdmin]
  parser_classes = [MultiPartParser, FormParser]

  def patch(self, request, *args, **kwargs):
    return self.partial_update(request, *args, **kwargs)


@extend_schema(
  tags=["Interest"],
  summary="Delete an interest",
  responses={204: OpenApiResponse(description="Deleted successfully")},
)
class InterestDeleteView(mixins.DestroyModelMixin, GenericAPIView):
  """Delete an interest - admin only"""

  serializer_class = InterestSerializer
  queryset = ALL_INTERESTS_QUERYSET
  permission_classes = [IsAdmin]

  def delete(self, request, *args, **kwargs):
    return self.destroy(request, *args, **kwargs)
