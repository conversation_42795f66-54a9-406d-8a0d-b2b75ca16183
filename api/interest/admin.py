from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Interest


@admin.register(Interest)
class InterestAdmin(ModelAdmin):
  list_display = ("id", "name", "image", "created_at", "updated_at")
  search_fields = ("name",)
  list_filter = ("created_at",)
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "name",
          "image",
          "created_at",
          "updated_at",
        )
      },
    ),
  )
