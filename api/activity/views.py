from rest_framework.generics import GenericAP<PERSON><PERSON>iew, ListAPIView, RetrieveAPIView
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.exceptions import status
from rest_framework.views import APIView
from rest_framework import mixins

from api.activity.models import Activity
from api.activity.querysets import ALL_ACTIVITIES_QUERYSET
from api.activity.serializers import ActivityBatchUpdateSerializer, ActivitySerializer
from core.utils.app_response import AppPagination, AppResponse
from api.consts.permissions import IsAuthenticated


@extend_schema(tags=["Activity"])
class ActivityMeListView(ListAPIView):
  """List all activities owned by the authenticated user"""

  serializer_class = ActivitySerializer
  pagination_class = AppPagination
  permission_classes = [IsAuthenticated]

  def get_queryset(self):
    return ALL_ACTIVITIES_QUERYSET.filter(owner=self.request.user)

  def list(self, request, *args, **kwargs):
    """Override list method to use AppResponse format"""
    queryset = self.filter_queryset(self.get_queryset())

    page = self.paginate_queryset(queryset)
    if page is not None:
      serializer = self.get_serializer(page, many=True)
      return self.get_paginated_response(serializer.data)

    serializer = self.get_serializer(queryset, many=True)
    return AppResponse.success(data=serializer.data)


@extend_schema(tags=["Activity"])
class ActivityMeRetrieveView(RetrieveAPIView):
  """Retrieve a single activity by ID - only if owned by the authenticated user"""

  serializer_class = ActivitySerializer
  permission_classes = [IsAuthenticated]

  def get_queryset(self):
    return ALL_ACTIVITIES_QUERYSET.filter(owner=self.request.user)

  def retrieve(self, request, *args, **kwargs):
    """Override retrieve method to use AppResponse format"""
    instance = self.get_object()
    serializer = self.get_serializer(instance)
    return AppResponse.success(data=serializer.data)


@extend_schema(
  tags=["Activity"],
  summary="Create a new activity",
  description="Create a new activity. The authenticated user will be set as the owner.",
  responses={201: ActivitySerializer},
)
class ActivityMeCreateView(mixins.CreateModelMixin, GenericAPIView):
  serializer_class = ActivitySerializer
  permission_classes = [IsAuthenticated]

  def perform_create(self, serializer):
    serializer.save(owner=self.request.user)

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    if not serializer.is_valid():
      return AppResponse.error(message="Invalid data", errors=serializer.errors)

    self.perform_create(serializer)
    return AppResponse.success(
      data=serializer.data, message="Activity created successfully", status=status.HTTP_201_CREATED
    )


@extend_schema(
  tags=["Activity"],
  summary="Update an existing activity",
  description="Update activity fields. Only accessible if the user is the owner.",
  responses={200: ActivitySerializer},
)
class ActivityMeUpdateView(mixins.UpdateModelMixin, GenericAPIView):
  serializer_class = ActivitySerializer
  permission_classes = [IsAuthenticated]
  parser_classes = [MultiPartParser, FormParser]

  def get_queryset(self):
    return ALL_ACTIVITIES_QUERYSET.filter(owner=self.request.user)

  def patch(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance, data=request.data, partial=True)

    if not serializer.is_valid():
      return AppResponse.error(message="Invalid data", errors=serializer.errors)

    self.perform_update(serializer)
    return AppResponse.success(data=serializer.data, message="Activity updated successfully")


@extend_schema(
  tags=["Activity"],
  summary="Delete an activity",
  responses={204: OpenApiResponse(description="Deleted successfully")},
)
class ActivityMeDeleteView(mixins.DestroyModelMixin, GenericAPIView):
  """Delete an activity - only if owned by the authenticated user"""

  serializer_class = ActivitySerializer
  permission_classes = [IsAuthenticated]

  def get_queryset(self):
    return ALL_ACTIVITIES_QUERYSET.filter(owner=self.request.user)

  # TODO: For delete actions there is no app response returned
  def delete(self, request, *args, **kwargs):
    instance = self.get_object()
    self.perform_destroy(instance)
    return AppResponse.success(
      message="Activity deleted successfully", status=status.HTTP_204_NO_CONTENT
    )


class ActivityMeBatchUpdateView(APIView):
  """Update multiple activities at once - only activities owned by the authenticated user"""

  permission_classes = [IsAuthenticated]
  parser_classes = [JSONParser]

  @extend_schema(
    tags=["Activity"],
    summary="Batch update activities",
    description="Update multiple activities at once. Only activities owned by the user can be updated.",
    request=ActivityBatchUpdateSerializer,
    responses={
      200: OpenApiResponse(
        description="Activities updated successfully", response=ActivitySerializer(many=True)
      ),
      400: OpenApiResponse(description="Invalid data provided"),
      404: OpenApiResponse(description="One or more activities not found or not owned by user"),
    },
    examples=[
      OpenApiExample(
        "Example Batch Update",
        summary="Update multiple activities",
        value={
          "activities": [
            {"id": "uuid-here", "name": "Updated Name 1", "price": 29.99},
            {"id": "another-uuid", "price": 19.99},
          ]
        },
        request_only=True,
      )
    ],
    operation_id="activity_me_batch_update",
  )
  def patch(self, request, *args, **kwargs):
    serializer = ActivityBatchUpdateSerializer(data=request.data)
    if not serializer.is_valid():
      return AppResponse.error(message="Invalid data", errors=serializer.errors)

    activities_data = serializer.validated_data["activities"]
    updated_activities = []
    errors = []

    for activity_data in activities_data:
      activity_id = activity_data.pop("id")
      try:
        activity = ALL_ACTIVITIES_QUERYSET.get(id=activity_id, owner=request.user)
        activity_serializer = ActivitySerializer(activity, data=activity_data, partial=True)

        if activity_serializer.is_valid():
          activity_serializer.save()
          updated_activities.append(activity_serializer.data)
        else:
          errors.append({"id": activity_id, "errors": activity_serializer.errors})
      except Activity.DoesNotExist:
        errors.append(
          {
            "id": activity_id,
            "errors": {"detail": "Activity not found or not owned by current user"},
          }
        )

    if errors:
      return AppResponse.error(
        message="Some activities could not be updated",
        errors={"updated": updated_activities, "errors": errors},
      )

    return AppResponse.success(data=updated_activities, message="Activities updated successfully")
