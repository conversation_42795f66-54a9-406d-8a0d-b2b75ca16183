from django.utils.translation import gettext_lazy as _
from access.models import User
from core.models import AbstractModel
from django.db import models
from datetime import timedelta


class Activity(AbstractModel):
  name = models.CharField(
    _("Name"),
    max_length=128,
  )

  owner = models.ForeignKey(
    User,
    verbose_name=_("Owner"),
    related_name="activities",
    on_delete=models.CASCADE,
  )

  price = models.FloatField(
    _("Price"),
  )

  duration = models.DurationField(
    _("Duration"),
    # TODO: Remove default
    default=timedelta(minutes=60),
  )

  def __str__(self):
    return self.name
