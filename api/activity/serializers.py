from rest_framework import serializers
from django.core.exceptions import ObjectDoesNotExist

from api.activity.models import Activity


class ActivitySerializer(serializers.ModelSerializer):
  class Meta:
    model = Activity
    fields = ["id", "name", "price", "duration", "owner", "created_at", "updated_at"]
    read_only_fields = ["id", "owner", "created_at", "updated_at"]

class ActivityPublicSerializer(serializers.ModelSerializer):
  class Meta:
    model = Activity
    fields = ["id", "name", "price", "duration"]
    read_only_fields = ["id", "name", "price", "duration"]


class ActivityUpdateSerializer(serializers.Serializer):
  id = serializers.UUIDField(required=True)
  name = serializers.CharField(required=False, max_length=128)
  price = serializers.FloatField(required=False)
  duration = serializers.DurationField(required=False)

  def validate_id(self, value):
    try:
      Activity.objects.get(id=value)
      return value
    except ObjectDoesNotExist:
      raise serializers.ValidationError("Activity with this ID does not exist")


class ActivityBatchUpdateSerializer(serializers.Serializer):
  activities = ActivityUpdateSerializer(many=True)
