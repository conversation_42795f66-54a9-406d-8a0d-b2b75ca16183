from django.urls import path

from api.activity.views import (
  ActivityMeBatchUpdateView,
  ActivityMeRetrieveView,
  ActivityMeUpdateView,
  ActivityMeCreateView,
  ActivityMeDeleteView,
  ActivityMeListView,
)


urlpatterns = [
  path("me/", ActivityMeListView.as_view()),
  path("me/create/", ActivityMeCreateView.as_view()),
  path("me/update", ActivityMeBatchUpdateView.as_view()),
  path("me/<uuid:pk>/", ActivityMeRetrieveView.as_view()),
  path("me/<uuid:pk>/update/", ActivityMeUpdateView.as_view()),
  path("me/<uuid:pk>/delete/", ActivityMeDeleteView.as_view()),
]
