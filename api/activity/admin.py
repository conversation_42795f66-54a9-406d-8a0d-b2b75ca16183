from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Activity


@admin.register(Activity)
class ActivityAdmin(ModelAdmin):
  list_display = ("id", "name", "price", "duration", "owner", "created_at", "updated_at")
  search_fields = ("name",)
  list_filter = ("created_at",)
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "name",
          "price",
          "duration",
          "owner",
          "created_at",
          "updated_at",
        )
      },
    ),
  )
