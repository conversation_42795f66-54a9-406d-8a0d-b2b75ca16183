from rest_framework import serializers
from django.contrib.gis.db.models.functions import Distance
from access.models import User, UserProfileImage
from api.activity.serializers import ActivitySerializer
from api.interest.models import Interest
from api.interest.serializers import InterestSerializer
from api.utils import ApiUtils, SparkStatusEnum


# TODO: Add main photo serializer
class UserProfileImageSerializer(serializers.ModelSerializer):
  class Meta:
    model = UserProfileImage
    fields = ["id", "image"]


class UserProfilePublicSerializer(serializers.ModelSerializer):
  """
  Serializer for representing any user public profile info.
  """

  profile_images = UserProfileImageSerializer(many=True, read_only=True)
  interests = InterestSerializer(many=True, read_only=True)
  activities = ActivitySerializer(many=True, read_only=True)
  spark_status = serializers.SerializerMethodField()
  distance = serializers.SerializerMethodField()

  class Meta:
    model = User
    fields = [
      "id",
      "full_name",
      "bio",
      "dob",
      "gender",
      "location",
      "is_online",
      "activities",
      "profile_images",
      "interests",
      "spark_status",
      "distance",
    ]
    read_only_fields = [
      "id",
    ]

  def get_spark_status(self, obj):
    """
    Get the spark status between the authenticated user and the profile being viewed.

    Returns:
      - 'none': No spark has been sent between users
      - 'sent': Spark sent by auth user but not responded yet
      - 'received': Spark received by auth user from the viewed profile
      - 'matched': Spark sent and matched (both users sparked each other)
      - 'rejected': Spark was rejected
    """
    request = self.context.get("request")
    if not request:
      return SparkStatusEnum.NONE.value

    return ApiUtils.get_spark_status(request.user, obj)
    
  def get_distance(self, obj) -> int:
    """
    Get the distance between the authenticated user and the viewed profile.
    Returns distance in kilometers without decimals.
    """
    request = self.context.get("request")
    if not request or not request.user or not request.user.location or not obj.location:
      return 0
      
    # Calculate distance directly in the serializer
    # This gets the distance between the authenticated user and the viewed profile
    user_with_distance = (
      User.objects.filter(pk=obj.pk)
      .annotate(distance=Distance("location", request.user.location))
      .first()
    )
    
    if user_with_distance and hasattr(user_with_distance, "distance"):
      # Convert from meters to kilometers and round to integer
      return int(user_with_distance.distance.m / 1000)
    return 0


class UserProfileMeSerializer(serializers.ModelSerializer):
  """
  Serializer for representing authenticated user's profile with full details.
  """

  profile_images = UserProfileImageSerializer(many=True, read_only=True)
  interests = InterestSerializer(many=True, read_only=True)
  activities = ActivitySerializer(many=True, read_only=True)

  class Meta:
    model = User
    fields = [
      "id",
      "email",
      "mobile_number",
      "full_name",
      "bio",
      "is_ask_me_on",
      "is_notification_on",
      "is_live_location_on",
      "dob",
      "gender",
      "address",
      "location",
      "sparks_number",
      "live_location",
      "is_online",
      "activities",
      "profile_images",
      "interests",
      "created_at",
      "updated_at",
    ]
    read_only_fields = [
      "id",
      "email",
      "created_at",
      "updated_at",
    ]


class UserMeProfileImageUploadSerializer(serializers.Serializer):
  """
  Serializer for uploading user profile images.
  Handles both validation and business logic for replacing user profile images.
  """

  images = serializers.ListField(
    child=serializers.ImageField(max_length=None, allow_empty_file=False),
    required=True,
    write_only=True,
  )

  def validate_images(self, images):
    if len(images) > 6:
      raise serializers.ValidationError("Maximum 6 profile images allowed.")

    return images

  def create(self, validated_data):
    user = self.context["request"].user
    image_files = validated_data.get("images", [])

    # Delete all existing profile images for this user
    # TODO: Images are not actually deleted from the /media folder
    UserProfileImage.objects.filter(user=user).delete()

    # Create new profile images
    created_images = []
    for image_file in image_files:
      profile_image = UserProfileImage.objects.create(user=user, image=image_file)
      created_images.append(profile_image)

    return created_images


class UserMeProfileUpdateSerializer(serializers.ModelSerializer):
  """
  Serializer for updating authenticated user's profile fields
  (images excluded).
  """

  class Meta:
    model = User
    fields = [
      "full_name",
      "gender",
      "address",
      "location",
      "dob",
      "bio",
      "email",
      "gender",
    ]

  # TODO: Add validation to check if dob fits the minimum age

  def update(self, instance, validated_data):
    for attr, value in validated_data.items():
      setattr(instance, attr, value)
    instance.save()
    return instance


class UserMeInterestsUpdateSerializer(serializers.Serializer):
  """
  Serializer for updating authorized user's interests.
  Only accepts a list of interest IDs.
  """

  interest_ids = serializers.ListField(
    child=serializers.UUIDField(),
    required=True,
    write_only=True,
  )

  def validate_interest_ids(self, interest_ids):
    """
    Validate that all provided interest IDs exist in the database.
    """
    # Remove duplicates
    unique_ids = set(interest_ids)

    # Check if all IDs exist
    existing_interests = Interest.objects.filter(id__in=unique_ids)
    existing_ids = set(existing_interests.values_list("id", flat=True))

    # Find non-existent IDs
    non_existent_ids = unique_ids - existing_ids

    if non_existent_ids:
      raise serializers.ValidationError(
        f"Following interest IDs do not exist: {', '.join(map(str, non_existent_ids))}"
      )

    return list(unique_ids)

  def update(self, instance, validated_data):
    """
    Replace existing user interests with new ones.
    """
    user = instance
    interest_ids = validated_data.get("interest_ids", [])

    # Clear and set the interests directly using the M2M relationship
    user.interests.clear()
    user.interests.add(*interest_ids)

    return user
