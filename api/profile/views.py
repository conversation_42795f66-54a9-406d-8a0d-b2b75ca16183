from rest_framework.generics import <PERSON>ricAP<PERSON><PERSON>iew
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from drf_spectacular.utils import extend_schema
from drf_spectacular.types import OpenApiTypes

from access.models import User
from api.profile.serializers import (
  UserMeInterestsUpdateSerializer,
  UserMeProfileImageUploadSerializer,
  UserMeProfileUpdateSerializer,
  UserProfileMeSerializer,
  UserProfilePublicSerializer,
)
from api.consts.swagger_utils import SwaggerCommonExamples
from api.consts.permissions import IsAuthenticated
from core.utils.app_response import AppResponse
from core.utils.app_msg import AppMsg


@extend_schema(
  tags=["Profile"],
  responses={
    200: UserProfileMeSerializer,
    401: OpenApiTypes.OBJECT,
  },
  examples=[SwaggerCommonExamples.not_authenticated],
)
class UserProfileRetrieveView(GenericAPIView):
  """
  Retrieve a user's public profile by UUID.
  """

  queryset = User.objects.all()
  serializer_class = UserProfilePublicSerializer
  permission_classes = [IsAuthenticated]
  lookup_field = "pk"

  def get_object(self):
    return self.get_queryset().get(pk=self.kwargs[self.lookup_field])

  def get(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance)
    return AppResponse.success(message=AppMsg.USER_PROFILE_FOUND_SUCCESS, data=serializer.data)


@extend_schema(
  tags=["Profile"],
  responses={
    200: UserProfileMeSerializer,
    401: OpenApiTypes.OBJECT,
  },
  examples=[SwaggerCommonExamples.not_authenticated],
)
class UserProfileMeRetrieveView(GenericAPIView):
  """
  View to retrieve the currently authenticated user's profile information.
  """

  serializer_class = UserProfileMeSerializer
  permission_classes = [IsAuthenticated]

  def get_object(self):
    return self.request.user

  def get(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance)
    return AppResponse.success(message=AppMsg.PROFILE_GET_ME_SUCCESS, data=serializer.data)


@extend_schema(
  tags=["Profile"],
  responses={
    200: UserMeProfileImageUploadSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
  },
  examples=[SwaggerCommonExamples.not_authenticated],
  summary="Upload new profile images",
  description="Upload new profile images for the authenticated user. This will replace all existing profile images.",
  request={
    "multipart/form-data": {
      "type": "object",
      "properties": {
        "images": {
          "type": "array",
          "items": {"type": "string", "format": "binary"},
          "description": "Profile images to upload",
        },
      },
      "required": ["images"],
    },
  },
)
class UserMeProfileImagesUploadView(GenericAPIView):
  """
  View to upload new profile images for the currently authenticated user.
  Existing images will be deleted and replaced with the new uploads.
  """

  serializer_class = UserMeProfileImageUploadSerializer
  permission_classes = [IsAuthenticated]
  parser_classes = [MultiPartParser, FormParser]

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    serializer.save()

    return AppResponse.success(message=AppMsg.IMAGE_UPLOAD_SUCCESS)


@extend_schema(
  tags=["Profile"],
  responses={
    200: UserMeProfileUpdateSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
  },
  summary="Update basic user profile info",
  examples=[SwaggerCommonExamples.not_authenticated],
  description="Update user profile information for the authenticated user. Used for only basic fields, images are separate endpoints and rent prices are called activities in app",
)
class UserMeProfileUpdateView(GenericAPIView):
  """
  View to update basic profile fields for the currently authenticated user.
  Images are separate endpoints and rent prices are called activities in app
  """

  serializer_class = UserMeProfileUpdateSerializer
  permission_classes = [IsAuthenticated]

  def get_object(self):
    return self.request.user

  def patch(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance, data=request.data, partial=True)
    serializer.is_valid(raise_exception=True)
    serializer.save()

    return AppResponse.success(message=AppMsg.PROFILE_UPDATED)


@extend_schema(
  tags=["Profile"],
  responses={
    200: UserMeInterestsUpdateSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
  },
  summary="Update user interests",
  examples=[SwaggerCommonExamples.not_authenticated],
  description="Update interests for the authenticated user. Replaces all existing interests with the provided ones.",
  request={
    "application/json": {
      "type": "object",
      "properties": {
        "interest_ids": {
          "type": "array",
          "items": {"type": "uuid"},
          "description": "List of interest IDs to assign to the user",
        }
      },
      "required": ["interest_ids"],
    },
  },
)
class UserMeInterestsUpdateView(GenericAPIView):
  """
  View to update interests for the currently authenticated user.
  Existing interests will be replaced with the new ones.
  """

  serializer_class = UserMeInterestsUpdateSerializer
  permission_classes = [IsAuthenticated]

  def get_object(self):
    return self.request.user

  def patch(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance, data=request.data)
    serializer.is_valid(raise_exception=True)
    serializer.save()

    return AppResponse.success(message=AppMsg.INTERESTS_UPDATED)
