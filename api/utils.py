from typing import Optional
from enum import Enum


class SparkStatusEnum(Enum):
  """Enum representing all possible spark statuses between two users"""

  NONE = "none"  # No spark has been sent between users
  SENT = "sent"  # Spark sent by auth user but not responded yet
  RECEIVED = "received"  # Spark received by auth user from the viewed profile
  MATCHED = "matched"  # Spark sent and matched (both users sparked each other)
  REJECTED = "rejected"  # Spark was rejected


class ApiUtils:
  @staticmethod
  def get_first_profile_image(obj, request=None) -> Optional[str]:
    """Get only the first profile image if available, as absolute URL."""
    first_image = obj.profile_images.first()
    if first_image:
      url = first_image.image.url
      if request is not None:
        return request.build_absolute_uri(url)
      return url
    return None

  @staticmethod
  def get_spark_status(auth_user, viewed_profile, request=None) -> str:
    """
    Get the spark status between the authenticated user and the profile being viewed.
    
    Args:
      auth_user: The authenticated user
      viewed_profile: The profile being viewed
      request: Optional request object
      
    Returns:
      - 'none': No spark has been sent between users
      - 'sent': Spark sent by auth user but not responded yet
      - 'received': Spark received by auth user from the viewed profile
      - 'matched': Spark sent and matched (both users sparked each other)
      - 'rejected': Spark was rejected
    """
    # Import here to avoid circular imports
    from api.spark.models import Spark
    
    if not auth_user or not auth_user.is_authenticated:
      return SparkStatusEnum.NONE.value

    # If viewing own profile, return None
    if auth_user.id == viewed_profile.id:
      return SparkStatusEnum.NONE.value

    # Check if there's a spark from authenticated user to the viewed profile
    try:
      spark = Spark.objects.get(spark_send_from=auth_user, spark_send_to=viewed_profile)
      # Auth user sent a spark to viewed profile
      if spark.status == Spark.STATUS_PENDING:
        return SparkStatusEnum.SENT.value
      elif spark.status == Spark.STATUS_MATCHED:
        return SparkStatusEnum.MATCHED.value
      elif spark.status == Spark.STATUS_REJECTED:
        return SparkStatusEnum.REJECTED.value
    except Spark.DoesNotExist:
      # No spark from auth user to viewed profile, check the reverse
      pass

    # Check if there's a spark from viewed profile to authenticated user
    try:
      spark = Spark.objects.get(spark_send_from=viewed_profile, spark_send_to=auth_user)
      # Viewed profile sent a spark to auth user
      if spark.status == Spark.STATUS_PENDING:
        return SparkStatusEnum.RECEIVED.value
      elif spark.status == Spark.STATUS_MATCHED:
        return SparkStatusEnum.MATCHED.value
      elif spark.status == Spark.STATUS_REJECTED:
        return SparkStatusEnum.REJECTED.value
    except Spark.DoesNotExist:
      # No spark in either direction
      return SparkStatusEnum.NONE.value

    # Default fallback
    return SparkStatusEnum.NONE.value
