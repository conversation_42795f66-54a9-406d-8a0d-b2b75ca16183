from rest_framework import status
from rest_framework_simplejwt import views as jwt_views
from rest_framework_simplejwt.tokens import RefreshToken, TokenError
from rest_framework_simplejwt.settings import api_settings as jwt_settings
from drf_spectacular.utils import extend_schema, OpenApiExample, OpenApiTypes

from api.auth.otp.serializers import TokenResponseSerializer
from api.consts.serializers import ErrorResponseSerializer
from core.utils.app_response import BadRequest, AppResponse
from core.utils.app_msg import AppMsg
from api.auth.token.serializers import (
  TokenRefreshResponseSerializer,
  TokenRefreshSerializer,
)
from api.consts.permissions import AllowAny


class TokenRefreshView(jwt_views.TokenViewBase):
  permission_classes = [AllowAny]
  serializer_class = TokenRefreshSerializer

  @extend_schema(
    tags=["Authentication"],
    summary="Refresh JWT Token",
    operation_id="refreshToken",
    description="Refresh an expired access token using a valid refresh token. Returns new access and refresh tokens if successful.",
    request=TokenRefreshSerializer,
    responses={
      200: TokenRefreshResponseSerializer,
      400: ErrorResponseSerializer,
      401: ErrorResponseSerializer,
    },
    examples=[
      OpenApiExample(
        name="Valid Request",
        value={"refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."},
        request_only=True,
      ),
      OpenApiExample(
        name="Success Response",
        value={
          "status": True,
          "message": "Token refreshed successfully",
          "data": {
            "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          },
          "errors": None,
        },
        response_only=True,
        status_codes=["200"],
      ),
      OpenApiExample(
        name="Invalid Token Error",
        value={
          "status": False,
          "message": "Invalid Token",
          "data": None,
          "errors": {"detail": "Invalid token", "status_code": 401},
        },
        response_only=True,
        status_codes=["401"],
      ),
      OpenApiExample(
        name="Validation Error",
        value={
          "status": False,
          "message": "Validation error",
          "data": None,
          "errors": {"refresh": ["This field is required."]},
        },
        response_only=True,
        status_codes=["400"],
      ),
    ],
  )
  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    try:
      refresh = RefreshToken(serializer.validated_data["refresh"])
    except TokenError as _:
      raise BadRequest(
        {
          "message": "Invalid Token",
          "status_code": status.HTTP_401_UNAUTHORIZED,
        }
      )

    data = {
      "access": str(refresh.access_token),
      "refresh": str(refresh),
    }

    if jwt_settings.ROTATE_REFRESH_TOKENS:
      refresh.set_jti()
      refresh.set_exp()
      
      data = {
        "access": str(refresh.access_token),
        "refresh": str(refresh),
      }

    return AppResponse.success(
      data=data,
      status=status.HTTP_200_OK,
      message=AppMsg.TOKEN_REFRESH_SUCCESS,
    )
