# TODO: Uncomment for prod
# from rest_framework_simplejwt.tokens import RefreshToken, TokenError
# from rest_framework import serializers
# from access.models import User

# from django.core.validators import RegexValidator

# from api.consts.serializers import BaseResponseSerializer
# from services.sms_service import TwilioSMSService


# class MobileNumberSerializer(serializers.Serializer):
#   mobile_number = serializers.CharField(
#     min_length=7,
#     max_length=15,
#     error_messages={
#       "min_length": "Ensure this field has at least 7 characters.",
#       "max_length": "Ensure this field has no more than 15 characters.",
#     },
#   )


# class SendOTPSerializer(MobileNumberSerializer):
#   def create(self, validated_data):
#     mobile = validated_data.get("mobile_number")

#     # Check if user already exists with this mobile number
#     user, created = User.objects.get_or_create(
#       mobile_number=mobile,
#     )

#     # Send OTP using Twilio service
#     sms_service = TwilioSMSService()
#     success, message = sms_service.send_otp(mobile)

#     if not success:
#       raise serializers.ValidationError({"mobile_number": message})

#     return created


# class VerifyOTPSerializer(MobileNumberSerializer):
#   otp_number = serializers.CharField(
#     validators=[RegexValidator(r"^\d{4}$", "OTP must be exactly 4 digits")],
#   )


# class VerifyOTPSerializerSuccess(VerifyOTPSerializer):
#   """
#   Serializer that handles successful OTP verification and generates JWT tokens
#   """

#   def validate(self, attrs):
#     attrs = super().validate(attrs)

#     try:
#       # Find the user with the given mobile number
#       user = User.objects.get(mobile_number=attrs.get("mobile_number"))

#       sms_service = TwilioSMSService()
#       is_valid, message = sms_service.verify_otp(
#         attrs.get("mobile_number"),
#         attrs.get("otp_number")
#       )

#       if not is_valid:
#         raise serializers.ValidationError({"otp_number": message})

#       # Generate tokens
#       refresh = RefreshToken.for_user(user)

#       # Add tokens to validated data
#       attrs["tokens"] = {
#         "access": str(refresh.access_token),
#         "refresh": str(refresh),
#       }

#       # Store user for later use if needed
#       attrs["user"] = user

#       return attrs

#     except User.DoesNotExist:
#       raise serializers.ValidationError(
#         {"mobile_number": "User with this mobile number does not exist"}
#       )
#     except TokenError:
#       raise serializers.ValidationError({"detail": "Failed to generate token"})


# class TokenDataSerializer(serializers.Serializer):
#   access = serializers.CharField()
#   refresh = serializers.CharField()


# class TokenResponseSerializer(BaseResponseSerializer):
#   data = TokenDataSerializer()


from rest_framework_simplejwt.tokens import RefreshToken, TokenError
from rest_framework import serializers
from access.models import User

from django.core.validators import RegexValidator

from api.consts.serializers import BaseResponseSerializer


class MobileNumberSerializer(serializers.Serializer):
  mobile_number = serializers.CharField(
    min_length=7,
    max_length=15,
    error_messages={
      "min_length": "Ensure this field has at least 7 characters.",
      "max_length": "Ensure this field has no more than 15 characters.",
    },
  )


class SendOTPSerializer(MobileNumberSerializer):
  def create(self, validated_data):
    mobile = validated_data.get("mobile_number")

    # Check if user already exists with this mobile number
    user, created = User.objects.get_or_create(
      mobile_number=mobile,
    )

    # TODO: Create a service for actually sending OTP

    return created


class VerifyOTPSerializer(MobileNumberSerializer):
  otp_number = serializers.CharField(
    validators=[RegexValidator(r"^\d{4}$", "OTP must be exactly 4 digits")],
  )


class VerifyOTPSerializerSuccess(VerifyOTPSerializer):
  """
  Serializer that handles successful OTP verification and generates JWT tokens
  """

  def validate(self, attrs):
    attrs = super().validate(attrs)

    try:
      # Find the user with the given mobile number
      user = User.objects.get(mobile_number=attrs.get("mobile_number"))

      # Validate OTP
      # TODO: Implement actual OTP validation
      if attrs.get("otp_number") != "1234":
        raise serializers.ValidationError({"otp_number": "Invalid OTP"})

      # Generate tokens
      refresh = RefreshToken.for_user(user)

      # Add tokens to validated data
      attrs["tokens"] = {
        "access": str(refresh.access_token),
        "refresh": str(refresh),
      }

      # Store user for later use if needed
      attrs["user"] = user

      return attrs

    except User.DoesNotExist:
      raise serializers.ValidationError(
        {"mobile_number": "User with this mobile number does not exist"}
      )
    except TokenError:
      raise serializers.ValidationError({"detail": "Failed to generate token"})


class TokenDataSerializer(serializers.Serializer):
  access = serializers.CharField()
  refresh = serializers.CharField()


class TokenResponseSerializer(BaseResponseSerializer):
  data = TokenDataSerializer()
