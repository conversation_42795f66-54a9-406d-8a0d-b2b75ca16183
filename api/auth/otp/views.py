from drf_spectacular.utils import extend_schema, OpenApiExample
from rest_framework.generics import GenericAPIView
from rest_framework import status


from api.auth.otp.serializers import (
  SendOTPSerializer,
  TokenResponseSerializer,
  VerifyOTPSerializer,
  VerifyOTPSerializerSuccess,
)
from api.consts.serializers import BaseResponseSerializer, ErrorResponseSerializer
from core.utils.app_response import AppResponse
from api.consts.permissions import AllowAny
from core.utils.app_msg import AppMsg


class SendOTPView(GenericAPIView):
  permission_classes = [AllowAny]
  serializer_class = SendOTPSerializer

  @extend_schema(
    tags=["Authentication"],
    summary="Send OTP to mobile number",
    operation_id="sendOTP",
    description="Sends a one-time password to the provided mobile number for authentication. If the mobile number is provided first time it will create a new User instance with phone number in the database.",
    request=SendOTPSerializer,
    responses={
      201: BaseResponseSerializer,
      400: ErrorResponseSerializer,
    },
    examples=[
      OpenApiExample(
        name="Valid Request",
        value={"mobile_number": "000111222"},
        request_only=True,
      ),
      OpenApiExample(
        name="Success Response",
        value={"status": True, "message": "OTP sent successfully", "data": None, "errors": None},
        response_only=True,
        status_codes=["201"],
      ),
      OpenApiExample(
        name="Validation Error - Mobile Number",
        value={
          "status": False,
          "message": "Validation error",
          "data": None,
          "errors": {"mobile_number": ["Ensure this field has at least 7 characters."]},
        },
        response_only=True,
        status_codes=["400"],
      ),
    ],
  )
  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    created = serializer.save()
    if created:
      return AppResponse.success(
        status=status.HTTP_201_CREATED,
        message=AppMsg.OTP_SEND,
      )

    return AppResponse.success(
      status=status.HTTP_200_OK,
      message=AppMsg.OTP_SEND,
    )


class VerifyOTPView(GenericAPIView):
  permission_classes = [AllowAny]
  serializer_class = VerifyOTPSerializerSuccess

  @extend_schema(
    tags=["Authentication"],
    summary="Verify OTP and get tokens",
    operation_id="verifyOTP",
    description="Verifies the one-time password provided for the given mobile number and returns JWT access and refresh tokens upon successful verification.",
    request=VerifyOTPSerializer,
    responses={
      200: TokenResponseSerializer,
      400: ErrorResponseSerializer,
      404: ErrorResponseSerializer,
    },
    examples=[
      OpenApiExample(
        name="Valid Request",
        value={"mobile_number": "000111222", "otp_number": "1234"},
        request_only=True,
      ),
      OpenApiExample(
        name="Success Response",
        value={
          "status": True,
          "message": "OTP verified successfully.",
          "data": {
            "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          },
          "errors": None,
        },
        response_only=True,
        status_codes=["200"],
      ),
      OpenApiExample(
        name="Validation Error - OTP Format",
        value={
          "status": False,
          "message": "Validation error",
          "data": None,
          "errors": {"otp_number": ["OTP must be exactly 4 digits"]},
        },
        response_only=True,
        status_codes=["400"],
      ),
      OpenApiExample(
        name="Invalid OTP Error",
        value={
          "status": False,
          "message": "Validation error",
          "data": None,
          "errors": {"otp_number": ["Invalid OTP"]},
        },
        response_only=True,
        status_codes=["400"],
      ),
      OpenApiExample(
        name="User with mobile number does not exist",
        value={
          "status": False,
          "message": "Validation error",
          "data": None,
          "errors": {"mobile_number": ["User with this mobile number does not exist"]},
        },
        response_only=True,
        status_codes=["400"],
      ),
    ],
  )
  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    return AppResponse.success(
      message=AppMsg.OTP_VERIFIED,
      data=serializer.validated_data.get("tokens"),
      status=status.HTTP_200_OK,
    )
