from django.shortcuts import get_object_or_404
from rest_framework.generics import ListAPIView, CreateAPIView
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiTypes


from access.models import User
from api.chat.models import ChatRoom, Message
from core.utils.app_msg import AppMsg
from core.utils.app_response import AppChatPagination, AppPagination, AppResponse
from .serializers import ChatRoomCreateSerializer, ChatRoomSerializer, MessageSerializer


@extend_schema(
  tags=["Chat"],
  responses={
    200: ChatRoomSerializer(many=True),
    401: OpenApiTypes.OBJECT,  # Not authenticated
  },
)
class ChatRoomListView(ListAPIView):
  """
  Retrieve a list of chat rooms for the authenticated user.
  Results are paginated and ordered by last activity (most recent first).
  """

  serializer_class = ChatRoomSerializer
  permission_classes = [IsAuthenticated]
  pagination_class = AppPagination

  def get_queryset(self):
    """
    Filters the queryset to only include chat rooms where the authenticated user
    is a participant.
    """
    user = self.request.user
    queryset = ChatRoom.objects.filter(participants=user).order_by("-updated_at")
    return queryset

  def get_serializer_context(self):
    context = super().get_serializer_context()
    context["request"] = self.request
    return context


@extend_schema(
  tags=["Chat"],
  description="Create a new chat room or retrieve an existing one between the authenticated user and another user.",
)
class ChatRoomCreateView(CreateAPIView):
  serializer_class = ChatRoomCreateSerializer
  permission_classes = [IsAuthenticated]

  def create(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)
    other_user_id = serializer.validated_data["other_user_id"]
    current_user = request.user

    # Get the other user instance
    try:
      other_user = User.objects.get(id=other_user_id)
    except User.DoesNotExist:
      return AppResponse.error(message=AppMsg.USER_NOT_FOUND)

    if current_user == other_user:
      return AppResponse.error(message=AppMsg.CHAT_ROOM_SELF)

    # Check if a chat room already exists between these two users
    chat_room = (
      ChatRoom.objects.filter(participants=current_user)
      .filter(participants=other_user)
      .distinct()
      .first()
    )

    if not chat_room:
      # Create a new chat room
      chat_room = ChatRoom.objects.create()
      chat_room.participants.add(current_user, other_user)
      chat_room.save()

    # Serialize the chat room for response
    chat_room_serializer = ChatRoomSerializer(chat_room, context={"request": request})

    # TODO: add create flag to choose either create/retrieve message
    return AppResponse.success(message=AppMsg.CHAT_ROOM_CREATED, data=chat_room_serializer.data)


@extend_schema(
  tags=["Chat"],
  parameters=[
    OpenApiParameter(
      name="other_user_id",
      type=OpenApiTypes.UUID,
      location=OpenApiParameter.PATH,
      description="UUID of the other user in the conversation",
      required=True,
    ),
    OpenApiParameter(  # Docs for pagination parameter
      name="page",
      type=OpenApiTypes.INT,
      location=OpenApiParameter.QUERY,
      description="A page number within the paginated result set.",
      required=False,
    ),
    OpenApiParameter(  # Docs for page_size parameter (if enabled in pagination)
      name="page_size",
      type=OpenApiTypes.INT,
      location=OpenApiParameter.QUERY,
      description="Number of results to return per page.",
      required=False,
    ),
  ],
  responses={
    200: MessageSerializer(many=True),  # Response is a list of messages
    401: OpenApiTypes.OBJECT,  # Not authenticated
    404: OpenApiTypes.OBJECT,  # Other user not found
    403: OpenApiTypes.OBJECT,  # Permission denied (e.g., trying to access chat not involving you)
  },
  # You might add examples here similar to your other views
)
class MessageListView(ListAPIView):
  serializer_class = MessageSerializer
  permission_classes = [IsAuthenticated]
  pagination_class = AppChatPagination

  def get_queryset(self):
    other_user_id = self.kwargs["other_user_id"]
    current_user = self.request.user

    # TODO: change it to try catch because we returning custom app response
    other_user = get_object_or_404(User, id=other_user_id)

    # Find the chat room between current user and other user
    chat_room = (
      ChatRoom.objects.filter(participants=current_user).filter(participants=other_user).first()
    )

    print(f"chat_room: {chat_room}")

    if not chat_room:
      # Return empty queryset if no chat room exists
      return Message.objects.none()

    # Get messages from this chat room in reverse chronological order (newest first)
    queryset = Message.objects.filter(chat_room=chat_room).order_by("-created_at")

    return queryset


@extend_schema(tags=["Chat"])
class MessageReadView(APIView):
  """
  Mark all messages from the other user to the authenticated user as read.
  """

  permission_classes = [IsAuthenticated]

  def post(self, request, other_user_id):
    current_user = request.user

    # Get the other user
    try:
      other_user = User.objects.get(id=other_user_id)
    except User.DoesNotExist:
      return AppResponse.error(message=AppMsg.USER_NOT_FOUND, status_code=status.HTTP_404_NOT_FOUND)

    # Find the chat room between current user and other user
    chat_room = (
      ChatRoom.objects.filter(participants=current_user).filter(participants=other_user).first()
    )

    if not chat_room:
      return AppResponse.success(message="No chat room found, no messages to mark as read")

    # Get unread messages from other user to current user in this chat room
    unread_messages = Message.objects.filter(
      chat_room=chat_room, sender=other_user, receiver=current_user, read_by_receiver=False
    )

    # Mark all unread messages as read
    count = unread_messages.count()
    unread_messages.update(read_by_receiver=True)

    return AppResponse.success(
      message=f"Marked {count} messages as read", data={"marked_count": count}
    )
