from django.db import models
from django.utils.translation import gettext_lazy as _


from access.models import User
from core.models import AbstractModel


class ChatRoom(AbstractModel):
  participants = models.ManyToManyField(
    User,
    related_name="chat_rooms",
    verbose_name=_("Participants"),
  )

  def __str__(self):
    return f"Chat {self.id} - {', '.join([str(user) for user in self.participants.all()])}"


class Message(AbstractModel):
  chat_room = models.ForeignKey(
    ChatRoom,
    related_name="messages",
    on_delete=models.CASCADE,
    verbose_name=_("Chat Room"),
  )
  sender = models.ForeignKey(
    User,
    verbose_name=_("Sender"),
    related_name="sent_messages",
    on_delete=models.CASCADE,
  )
  receiver = models.ForeignKey(
    User,
    verbose_name=_("Receiver"),
    related_name="received_messages",
    on_delete=models.CASCADE,
  )
  content = models.TextField(_("Content"))
  read_by_receiver = models.BooleanField(_("Read by Receiver"), default=False)
