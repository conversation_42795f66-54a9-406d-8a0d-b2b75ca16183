from typing import Optional
from rest_framework import serializers

from access.models import User
from api.chat.models import Chat<PERSON>oom, Message
from api.utils import ApiUtils


class UserBriefSerializer(serializers.ModelSerializer):
  profile_image = serializers.SerializerMethodField()

  class Meta:
    model = User
    fields = ["id", "full_name", "profile_image"]

  def get_profile_image(self, obj) -> Optional[str]:
    request = self.context.get("request")
    return ApiUtils.get_first_profile_image(obj, request)


class ChatRoomSerializer(serializers.ModelSerializer):
  other_user = serializers.SerializerMethodField()
  last_message = serializers.SerializerMethodField()
  unread_count = serializers.SerializerMethodField()

  class Meta:
    model = ChatRoom
    fields = ["id", "other_user", "last_message", "created_at", "updated_at", "unread_count"]

  def get_other_user(self, obj) -> dict:
    request = self.context.get("request")
    if request and hasattr(request, "user"):
      current_user = request.user
      other_user = obj.participants.exclude(id=current_user.id).first()
      if other_user:
        serializer = UserBriefSerializer(other_user, context={"request": request})
        return serializer.data
    return None

  def get_last_message(self, obj) -> dict:
    last_message = Message.objects.filter(chat_room=obj).order_by("-created_at").first()

    if last_message:
      return {
        "id": last_message.id,
        "content": last_message.content,
        "created_at": last_message.created_at,
        "sender_id": last_message.sender.id,
        "read_by_receiver": last_message.read_by_receiver,
      }
    return None

  def get_unread_count(self, obj) -> int:
    request = self.context.get("request")
    if request and hasattr(request, "user"):
      current_user = request.user
      # TODO: This will only work properly for 1:1 chats as it is now
      other_user = obj.participants.exclude(id=current_user.id).first()

      if other_user:
        # Count messages sent by other_user to current_user that are unread
        return Message.objects.filter(
          chat_room=obj, sender=other_user, receiver=current_user, read_by_receiver=False
        ).count()

    return 0


class ChatRoomCreateSerializer(serializers.Serializer):
  other_user_id = serializers.UUIDField(write_only=True)


class MessageSerializer(serializers.ModelSerializer):
  """
  Serializer for the Message model for REST API.
  """

  # We include sender_id and receiver_id directly
  # as the client likely already knows who these users are
  # based on the conversation context.

  sender_id = serializers.ReadOnlyField(source="sender.id")
  receiver_id = serializers.ReadOnlyField(source="receiver.id")

  class Meta:
    model = Message
    fields = ["id", "sender_id", "receiver_id", "content", "created_at", "read_by_receiver"]
    read_only_fields = ["id", "sender_id", "receiver_id", "content", "created_at"]
