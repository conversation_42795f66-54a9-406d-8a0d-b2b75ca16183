from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Message, ChatRoom


@admin.register(Message)
class MessageAdmin(ModelAdmin):
  list_display = (
    "id",
    "chat_room",
    "sender",
    "receiver",
    "content",
    "created_at",
    "updated_at",
  )
  search_fields = (
    "sender__username",
    "receiver__username",
    "chat_room__id",
    "chat_room__participants__username",
  )
  list_filter = ("created_at", "chat_room")
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "chat_room",
          "sender",
          "receiver",
          "content",
          "created_at",
          "updated_at",
        )
      },
    ),
  )


@admin.register(ChatRoom)
class ChatRoomAdmin(ModelAdmin):
  list_display = ("id", "__str__", "created_at", "updated_at")
  filter_horizontal = ("participants",)
  search_fields = ("id", "participants__username", "participants__email")
  list_filter = ("created_at",)
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "participants",
          "created_at",
          "updated_at",
        )
      },
    ),
  )
