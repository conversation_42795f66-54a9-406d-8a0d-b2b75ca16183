from rest_framework import serializers

from core.utils.app_msg import AppMsg
from .models import Spark
from access.models import User


class SendSparkSerializer(serializers.Serializer):
  recipient_id = serializers.UUIDField(help_text="UUID of the user to send spark to")

  def validate_recipient_id(self, value):
    """
    Validate the recipient exists and no pending spark already exists.
    """
    request = self.context.get("request")
    sender = request.user

    try:
      recipient = User.objects.get(id=value)
    except User.DoesNotExist:
      raise serializers.ValidationError(AppMsg.USER_NOT_FOUND)

    # Check that sender isn't sending to themselves
    if sender.id == recipient.id:
      raise serializers.ValidationError(AppMsg.SPARK_NOT_YOURSELF)

    # Check for existing pending spark from sender to recipient
    existing_spark = Spark.objects.filter(
      spark_send_from=sender,
      spark_send_to=recipient,
      status=Spark.STATUS_PENDING,
    ).exists()

    if existing_spark:
      raise serializers.ValidationError(AppMsg.SPARK_ALREADY_SENT)

    return recipient  # Return the actual user object instead of just the ID


class RespondSparkSerializer(serializers.Serializer):
  sender_id = serializers.UUIDField(help_text="UUID of the user who sent the spark")
  status = serializers.ChoiceField(
    choices=[
      (Spark.STATUS_MATCHED, "Match"),
      (Spark.STATUS_REJECTED, "Reject"),
    ],
    help_text="Response status: 'matched' to accept/match, 'rejected' to reject"
  )

  def validate(self, data):
    """
    Validate that:
    1. The sender exists
    2. There is an existing pending spark from sender to responder
    3. Status is either 0 or 1
    """
    request = self.context.get("request")
    responder = request.user
    sender_id = data["sender_id"]
    status = data["status"]

    # Validate sender exists
    try:
      sender = User.objects.get(id=sender_id)
    except User.DoesNotExist:
      raise serializers.ValidationError({"sender_id": AppMsg.USER_NOT_FOUND})

    # Validate status value
    if status not in [Spark.STATUS_MATCHED, Spark.STATUS_REJECTED]:
      raise serializers.ValidationError(
        {"status": f"Status must be either '{Spark.STATUS_MATCHED}' (accept) or '{Spark.STATUS_REJECTED}' (reject)"}
      )

    # Find the pending spark
    spark = Spark.objects.filter(
      spark_send_from=sender,
      spark_send_to=responder,
      status=Spark.STATUS_PENDING,
    ).first()

    if not spark:
      raise serializers.ValidationError(AppMsg.SPARK_NO_PENDING)

    data["spark_instance"] = spark

    return data
