# spark/models.py

from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import AbstractModel

from access.models import User


class Spark(AbstractModel):
  # Status constants
  STATUS_PENDING = 'pending'
  STATUS_MATCHED = 'matched'
  STATUS_REJECTED = 'rejected'
  
  SPARK_STATUS_CHOICES = [
    (STATUS_PENDING, _("Pending")),  # Spark sent, recipient hasn't responded with a spark back
    (STATUS_MATCHED, _("Matched")),  # Spark sent, recipient sent one back
    (STATUS_REJECTED, _("Rejected")),  # Spark sent, recipient rejected it
  ]

  # The user who initiated the spark
  spark_send_from = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="sent_sparks",
    verbose_name=_("Spark Sender"),
  )

  # The user who received the spark
  spark_send_to = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="received_sparks",
    verbose_name=_("Spark Receiver"),
  )

  status = models.CharField(_("Status"), max_length=20, choices=SPARK_STATUS_CHOICES, default=STATUS_PENDING)

  spark_matched_timing = models.DateTimeField(
    _("Spark Matched Timing"),
    null=True,
    blank=True,
    help_text=_("Timestamp when status changed to Matched"),
  )

  class Meta:
    verbose_name = _("Spark")
    verbose_name_plural = _("Sparks")

    """
    This is the best choice because:
    It enforces that only one spark record can exist between any two users in a specific direction (A→B, regardless of status).
    Example:

    User A sends spark to B: Create A→B (PENDING)
    If B already sparked A: Update B→A to MATCHED
    If B responds to A's spark: Update A→B to MATCHED or delete it if ignored

    The directional unique constraint allows both A→B and B→A records to exist simultaneously (which might be needed during the "pending" phase), while preventing multiple records in the same direction.
    """
    unique_together = ("spark_send_from", "spark_send_to")

  def __str__(self):
    # Use string reference to avoid import issues at model definition time
    sender_name = getattr(self.spark_send_from, "full_name", f"User {self.spark_send_from_id}")
    receiver_name = getattr(self.spark_send_to, "full_name", f"User {self.spark_send_to_id}")
    return f"Spark from {sender_name} to {receiver_name} ({self.get_status_display()})"
