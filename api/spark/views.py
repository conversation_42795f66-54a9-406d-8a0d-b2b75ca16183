from rest_framework.generics import GenericAPIView
from drf_spectacular.utils import extend_schema, OpenApiExample
from drf_spectacular.types import OpenApiTypes

from api.consts.permissions import IsAuthenticated
from core.utils.app_response import AppResponse
from api.consts.swagger_utils import SwaggerCommonExamples
from core.utils.app_logger import <PERSON><PERSON><PERSON>ogger

from .serializers import SendSparkSerializer, RespondSparkSerializer
from .services import SparkService

logger = AppLogger()


@extend_schema(
  tags=["Spark"],
  responses={
    200: OpenApiTypes.OBJECT,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
    OpenApiExample(
      name="not_enough_sparks",
      value={"status": False, "message": "Not enough sparks"},
      response_only=True,
      status_codes=["400"],
    ),
  ],
  summary="Send a spark to another user",
  description="Send a spark from the authenticated user to another user. Decrements sender's sparks. "
  "Checks for reverse pending spark to create a match.",
)
class SendSparkView(GenericAPIView):
  """
  Handles sending a spark from the authenticated user to another user.
  Decrements sender's sparks. Checks for reverse pending spark to create a match.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = SendSparkSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data, context={"request": request})
    serializer.is_valid(raise_exception=True)

    sender = request.user
    recipient = serializer.validated_data["recipient_id"]

    result = SparkService.send_spark(
      sender=sender,
      recipient=recipient,
    )

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Spark"],
  responses={
    200: OpenApiTypes.OBJECT,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
    OpenApiExample(
      name="not_enough_sparks",
      value={"status": False, "message": "You don't have enough sparks to send one back."},
      response_only=True,
      status_codes=["400"],
    ),
  ],
  summary="Respond to a received spark",
  description="Respond to a received spark (send back/accept or ignore/delete).",
)
class RespondSparkView(GenericAPIView):
  """
  Handles responding to a received spark (send back/accept or ignore/delete).
  """

  permission_classes = [IsAuthenticated]
  serializer_class = RespondSparkSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data, context={"request": request})
    serializer.is_valid(raise_exception=True)

    responder = request.user
    spark_instance = serializer.validated_data["spark_instance"]
    response_status = serializer.validated_data["status"]

    result = SparkService.respond_spark(
      responder=responder, spark_instance=spark_instance, response_status=response_status
    )

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)
