from django.db import transaction
from django.utils import timezone
from typing import Tuple

from api.consts.service_result import ServiceResult
from core.utils.app_logger import AppLogger
from core.utils.app_msg import AppMsg
from core.utils.notification_helpers import NotificationHelpers
from api.notification.services import NotificationService
from api.notification.utils import NotificationType

from .models import Spark
from access.models import User

logger = AppLogger()


class SparkService:
  """
  Service class for Spark-related business logic.
  Separates domain logic from view/presentation
  """

  @staticmethod
  def send_spark(sender: User, recipient: User) -> ServiceResult:
    """
    Send a spark from one user to another.

    Args:
      sender: The user sending the spark
      recipient: The user receiving the spark

    Returns:
      Tuple of (success status, message, remaining sparks)

    Raises:
      ValueError: If sender doesn't have enough sparks
    """

    try:
      if sender.sparks_number <= 0:
        return ServiceResult(
          success=False,
          message=AppMsg.SPARK_NOT_ENOUGH,
        )

      notification_service = NotificationService.get_instance()

      sender_name = sender.full_name
      recipient_name = recipient.full_name

      with transaction.atomic():
        sender.sparks_number -= 1
        sender.save(update_fields=["sparks_number"])

        # Check for a pending spark from recipient to sender (reverse spark)
        reverse_spark = Spark.objects.filter(
          spark_send_from=recipient,
          spark_send_to=sender,
          status=Spark.STATUS_PENDING,
        ).first()

        if reverse_spark:
          # Found a pending spark from recipient -> Match
          reverse_spark.status = Spark.STATUS_MATCHED
          reverse_spark.spark_matched_timing = timezone.now()
          reverse_spark.save(update_fields=["status", "spark_matched_timing", "updated_at"])

          # In case of a match, we need to notify both users
          # First, prepare notification data for both users
          sender_notification_data = {
            "spark_id": str(reverse_spark.id),
            "match_user_id": str(recipient.id),
            "match_user_name": recipient_name,
          }
          
          recipient_notification_data = {
            "spark_id": str(reverse_spark.id),
            "match_user_id": str(sender.id),
            "match_user_name": sender_name,
          }

          # For matches, we always notify both users
          notification_service.notify(
            recipient=sender,
            title="Spark Match!",
            body=f"You matched with {recipient_name}!",
            notification_type=NotificationType.SPARK_MATCHED,
            sender=recipient,
            data=sender_notification_data,
          )

          notification_service.notify(
            recipient=recipient,
            title="Spark Match!",
            body=f"You matched with {sender_name}!",
            notification_type=NotificationType.SPARK_MATCHED,
            sender=sender,
            data=recipient_notification_data,
          )

        else:
          # No pending spark from recipient -> Create a new pending spark
          new_spark = Spark.objects.create(
            spark_send_from=sender,
            spark_send_to=recipient,
            status=Spark.STATUS_PENDING,
          )

          # For a new spark, the sender is the action initiator
          notification_recipient, notification_sender_name = NotificationHelpers.determine_spark_participants(
            action_initiator=sender,
            spark_sender=sender,
            spark_recipient=recipient
          )
          
          # Data for additional context
          notification_data = {
            "spark_id": str(new_spark.id),
            "sender_id": str(sender.id),
            "sender_name": sender_name,
          }

          # Notify recipient about the new spark
          # In this case, notification_recipient should always be the recipient
          # since the sender initiated the action
          notification_service.notify(
            recipient=notification_recipient,
            title="New Spark",
            body=f"{notification_sender_name} sent you a spark!",
            notification_type=NotificationType.SPARK_SENT,
            sender=sender,
            data=notification_data,
          )

        return ServiceResult(
          success=True,
          message=AppMsg.SPARK_SENT,
          data={"sparks_number": sender.sparks_number},
        )

    except Exception as e:
      logger.log_error("SparService - send_spark", e)

  @staticmethod
  def respond_spark(
    responder: User, spark_instance: Spark, response_status: int
  ) -> Tuple[bool, str, int]:
    """
    Respond to a received spark (accept/match or ignore).

    Args:
      responder: The user responding to the spark
      spark_instance: The pending spark object
      response_status: 1 for accept, 0 for ignore

    Returns:
      Tuple of (success status, message, remaining sparks)

    Raises:
      ValueError: If responder doesn't have enough sparks (when accepting)
    """

    try:
      sender = spark_instance.spark_send_from
      notification_service = NotificationService.get_instance()

      # Define name variables once to use throughout the method
      sender_name = sender.full_name
      responder_name = responder.full_name

      with transaction.atomic():
        if response_status == Spark.STATUS_MATCHED:  # Accept/Match:
          if responder.sparks_number <= 0:
            return ServiceResult(
              success=False,
              message=AppMsg.SPARK_NOT_ENOUGH_BACK,
            )

          responder.sparks_number -= 1
          responder.save(update_fields=["sparks_number"])

          spark_instance.status = Spark.STATUS_MATCHED
          spark_instance.spark_matched_timing = timezone.now()
          spark_instance.save(update_fields=["status", "spark_matched_timing", "updated_at"])

          # Determine notification participants using our helper
          # For spark acceptance, we use the responder as the action initiator
          notification_recipient, notification_sender_name = NotificationHelpers.determine_spark_participants(
            action_initiator=responder,
            spark_sender=sender,
            spark_recipient=responder
          )
          
          # In case of a match, we need to notify both users
          # First, prepare notification data for both users
          sender_data = {
            "spark_id": str(spark_instance.id),
            "match_user_id": str(responder.id),
            "match_user_name": responder_name,
          }
          
          responder_data = {
            "spark_id": str(spark_instance.id),
            "match_user_id": str(sender.id),
            "match_user_name": sender_name,
          }

          # For matches, we always notify both users
          notification_service.notify(
            recipient=sender,
            title="Spark Matched!",
            body=f"{responder_name} accepted your spark!",
            notification_type=NotificationType.SPARK_MATCHED,
            sender=responder,
            data=sender_data,
          )

          notification_service.notify(
            recipient=responder,
            title="Spark Matched!",
            body=f"You accepted {sender_name}'s spark!",
            notification_type=NotificationType.SPARK_MATCHED,
            sender=sender,
            data=responder_data,
          )

        elif response_status == Spark.STATUS_REJECTED:  # Reject
          spark_instance.status = Spark.STATUS_REJECTED
          spark_instance.save(update_fields=["status", "updated_at"])

        return ServiceResult(
          success=True,
          message=AppMsg.SPARK_RESPONDED,
          data={"sparks_number": sender.sparks_number},
        )

    except Exception as e:
      logger.log_error("SparService - send_spark", e)

      return ServiceResult(
        success=False,
        message=AppMsg.SPARK_RESPONDED_FAILURE,
      )
