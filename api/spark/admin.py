from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Spark


@admin.register(Spark)
class SparkAdmin(ModelAdmin):
  list_display = (
    "id",
    "spark_send_from",
    "spark_send_to",
    "formatted_status",
    "spark_matched_timing",
    "created_at",
  )
  list_filter = ("status", "created_at", "spark_matched_timing")
  search_fields = (
    "spark_send_from__email",
    "spark_send_from__first_name",
    "spark_send_from__last_name",
    "spark_send_to__email",
    "spark_send_to__first_name",
    "spark_send_to__last_name",
  )
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"

  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "spark_send_from",
          "spark_send_to",
          "status",
        )
      },
    ),
    (
      "Timing Information",
      {
        "fields": (
          "spark_matched_timing",
          "created_at",
          "updated_at",
        ),
        "classes": ("collapse",),
      },
    ),
  )

  def formatted_status(self, obj):
    status_map = {
      obj.STATUS_PENDING: "Pending",
      obj.STATUS_MATCHED: "Matched",
      obj.STATUS_REJECTED: "Rejected",
    }
    return status_map.get(obj.status, obj.status)

  formatted_status.short_description = "Status"

  # Optional: Add actions to bulk update sparks
  actions = ["mark_as_matched", "mark_as_rejected"]

  def mark_as_matched(self, request, queryset):
    from django.utils import timezone
    from .models import Spark

    updated = queryset.filter(status=Spark.STATUS_PENDING).update(
      status=Spark.STATUS_MATCHED, spark_matched_timing=timezone.now()
    )
    self.message_user(request, f"{updated} spark(s) were successfully marked as matched.")

  mark_as_matched.short_description = "Mark selected sparks as matched"

  def mark_as_rejected(self, request, queryset):
    from .models import Spark

    updated = queryset.filter(status=Spark.STATUS_PENDING).update(status=Spark.STATUS_REJECTED)
    self.message_user(request, f"{updated} spark(s) were successfully marked as rejected.")

  mark_as_rejected.short_description = "Mark selected sparks as rejected"
