# Generated by Django 4.2.9 on 2025-05-05 14:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Spark',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('status', models.IntegerField(choices=[(0, 'Pending'), (1, 'Matched')], default=0, verbose_name='Status')),
                ('spark_matched_timing', models.DateTimeField(blank=True, help_text='Timestamp when status changed to Matched (status=1)', null=True, verbose_name='Spark Matched Timing')),
                ('spark_send_from', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_sparks', to=settings.AUTH_USER_MODEL, verbose_name='Spark Sender')),
                ('spark_send_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_sparks', to=settings.AUTH_USER_MODEL, verbose_name='Spark Receiver')),
            ],
            options={
                'verbose_name': 'Spark',
                'verbose_name_plural': 'Sparks',
                'unique_together': {('spark_send_from', 'spark_send_to')},
            },
        ),
    ]
