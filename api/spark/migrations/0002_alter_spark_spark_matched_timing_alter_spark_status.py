# Generated by Django 4.2.9 on 2025-06-02 13:35

from django.db import migrations, models


class Migration(migrations.Migration):
  dependencies = [
    ("spark", "0001_initial"),
  ]

  operations = [
    migrations.AlterField(
      model_name="spark",
      name="spark_matched_timing",
      field=models.DateTimeField(
        blank=True,
        help_text="Timestamp when status changed to Matched",
        null=True,
        verbose_name="Spark Matched Timing",
      ),
    ),
    migrations.AlterField(
      model_name="spark",
      name="status",
      field=models.CharField(
        choices=[("pending", "Pending"), ("matched", "Matched"), ("rejected", "Rejected")],
        default="pending",
        max_length=20,
        verbose_name="Status",
      ),
    ),
  ]
