from django.db import models
from django.utils.translation import gettext_lazy as _
from access.models import User
from core.models import AbstractModel


class SubscriptionPlan(AbstractModel):
  """Model to store available subscription plans"""

  CURRENCY_CHOICES = [
    ("EUR", _("€ (Euro)")),
  ]

  name = models.CharField(max_length=100)
  duration_months = models.PositiveIntegerField()
  price = models.DecimalField(max_digits=6, decimal_places=2)
  currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default="EUR")

  stripe_product_id = models.CharField(max_length=100)
  stripe_price_id = models.CharField(max_length=100)

  def __str__(self):
    currency_symbols = {"EUR": "€"}
    symbol = currency_symbols.get(self.currency, self.currency)
    return f"{self.name} ({symbol}{self.price})"


class Subscription(AbstractModel):
  """Model to store user subscriptions"""

  STATUS_CHOICES = [
    ("active", _("Active")),
    ("past_due", _("Past Due")),
    ("canceled", _("Canceled")),
    ("incomplete", _("Incomplete")),
    ("incomplete_expired", _("Incomplete Expired")),
    ("trialing", _("Trialing")),
    ("unpaid", _("Unpaid")),
  ]

  user = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="subscriptions",
  )

  plan = models.ForeignKey(
    SubscriptionPlan,
    on_delete=models.PROTECT,
    related_name="subscriptions",
  )

  stripe_subscription_id = models.CharField(max_length=100)
  stripe_customer_id = models.CharField(max_length=100)

  status = models.CharField(
    max_length=20,
    choices=STATUS_CHOICES,
    default="incomplete",
  )

  current_period_start = models.DateTimeField(null=True, blank=True)
  current_period_end = models.DateTimeField(null=True, blank=True)
  cancel_at_period_end = models.BooleanField(default=False)

  def __str__(self):
    return f"{self.user.email} - {self.plan.name}"
