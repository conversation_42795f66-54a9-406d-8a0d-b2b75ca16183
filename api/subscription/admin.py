from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Subscription, SubscriptionPlan


@admin.register(Subscription)
class SubscriptionAdmin(ModelAdmin):
  list_display = (
    "id",
    "user",
    "plan",
    "status",
    "stripe_subscription_id",
    "stripe_customer_id",
    "current_period_start",
    "current_period_end",
    "cancel_at_period_end",
    "created_at",
    "updated_at",
  )
  search_fields = ("user__email", "stripe_subscription_id", "stripe_customer_id")
  list_filter = ("status", "cancel_at_period_end", "created_at")
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "user",
          "plan",
          "status",
          "stripe_subscription_id",
          "stripe_customer_id",
          "current_period_start",
          "current_period_end",
          "cancel_at_period_end",
          "created_at",
          "updated_at",
        )
      },
    ),
  )


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(ModelAdmin):
  list_display = (
    "id",
    "name",
    "duration_months",
    "price",
    "currency",
    "stripe_product_id",
    "stripe_price_id",
    "created_at",
    "updated_at",
  )
  search_fields = ("name", "stripe_product_id", "stripe_price_id")
  list_filter = ("currency", "created_at")
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "name",
          "duration_months",
          "price",
          "currency",
          "stripe_product_id",
          "stripe_price_id",
          "created_at",
          "updated_at",
        )
      },
    ),
  )
