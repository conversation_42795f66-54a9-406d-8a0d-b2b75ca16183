import os
import stripe
from datetime import datetime
from django.utils import timezone
from typing import Dict, Any
from django.db import transaction

from access.models import User
from core.utils.app_msg import AppMsg
from api.consts.service_result import ServiceResult
from api.subscription.models import Subscription, SubscriptionPlan
from core.utils.app_logger import AppLogger
from api.notification.services import NotificationService
from api.notification.utils import NotificationType

logger = AppLogger()

# For backend site must be secret key
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")


class SubscriptionService:
  @staticmethod
  def create_payment_intent(user: User, plan_id: str) -> ServiceResult:
    """
    Create a payment intent for a subscription plan
    Returns client_secret for the mobile app to confirm payment
    Supports card, Apple Pay, and Google Pay
    """
    try:
      # Check if user already has an active subscription
      active_subscription_check = SubscriptionService._check_for_active_subscription(user)
      if active_subscription_check:
        return active_subscription_check

      plan = SubscriptionPlan.objects.get(id=plan_id)
      customer_id = SubscriptionService._get_or_create_stripe_customer(user)

      # Default payment methods if not specified (apply pay & google pay included in card set)
      payment_method_types = ["card"]

      # Create a SetupIntent instead of PaymentIntent
      # For subscriptions, we need to save the payment method first
      setup_intent = stripe.SetupIntent.create(
        customer=customer_id,
        payment_method_types=payment_method_types,
        metadata={
          "user_id": user.id,
          "plan_id": plan.id,
        },
      )

      result_data = {
        "client_secret": setup_intent.client_secret,
        "customer_id": customer_id,
        "price_id": plan.stripe_price_id,
      }

      return ServiceResult(
        success=True,
        data=result_data,
        message=AppMsg.SUBSCRIPTION_PREPARE_SUCCESS,
      )

    except SubscriptionPlan.DoesNotExist:
      return ServiceResult(success=False, message="Subscription plan not found")

    except Exception as e:
      logger.log_error("SubscriptionService - create_payment_intent", e)
      return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_PREPARE_FAILURE)

  @staticmethod
  @transaction.atomic
  def create_subscription(user: User, plan_id: str, payment_method_id: str) -> ServiceResult:
    """
    Create a subscription with the given payment method
    Returns the subscription object
    """
    try:
      # Check if user already has an active subscription
      active_subscription_check = SubscriptionService._check_for_active_subscription(user)
      if active_subscription_check:
        return active_subscription_check

      # Get the subscription plan
      try:
        plan = SubscriptionPlan.objects.get(id=plan_id)
      except SubscriptionPlan.DoesNotExist:
        logger.log_error("subscription", f"Subscription plan {plan_id} not found")
        return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_PLAN_NOT_FOUND)

      # Get or create Stripe customer
      customer_id = SubscriptionService._get_or_create_stripe_customer(user)
      logger.log_info("subscription", f"Using Stripe customer: {customer_id}")

      # Step 1: Verify the payment method exists
      try:
        payment_method = stripe.PaymentMethod.retrieve(payment_method_id)
        logger.log_info("subscription", f"Retrieved payment method: {payment_method_id}")
      except stripe.error.InvalidRequestError as e:
        logger.log_error("subscription", f"Invalid payment method: {payment_method_id}", e)
        return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)

      # Step 2: Handle payment method attachment
      payment_attached = False
      try:
        # If payment method is already attached to this customer, no action needed
        if hasattr(payment_method, "customer") and payment_method.customer == customer_id:
          logger.log_info(
            "subscription",
            f"Payment method {payment_method_id} already attached to customer {customer_id}",
          )
          payment_attached = True
        # If payment method is not attached to any customer, attach it
        elif not hasattr(payment_method, "customer") or not payment_method.customer:
          logger.log_info(
            "subscription",
            f"Attaching payment method {payment_method_id} to customer {customer_id}",
          )
          stripe.PaymentMethod.attach(payment_method_id, customer=customer_id)
          payment_attached = True
        # If payment method is attached to a different customer, return error
        else:
          other_customer = payment_method.customer
          logger.log_info(
            "subscription",
            f"Payment method {payment_method_id} is attached to another customer: {other_customer}",
          )
          return ServiceResult(
            success=False,
            message=AppMsg.SUBSCRIPTION_CREATE_FAILURE,
          )
      except stripe.error.InvalidRequestError as e:
        logger.log_error("subscription", f"Error attaching payment method: {str(e)}", e)
        return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)
      except Exception as e:
        logger.log_error("subscription", f"Unexpected error handling payment method: {str(e)}", e)
        return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)

      # Step 3: Set as default payment method (only if we successfully attached it)
      if payment_attached:
        try:
          stripe.Customer.modify(
            customer_id, invoice_settings={"default_payment_method": payment_method_id}
          )
          logger.log_info(
            "subscription", f"Set {payment_method_id} as default payment method for {customer_id}"
          )
        except stripe.error.InvalidRequestError as e:
          logger.log_error("subscription", f"Error setting default payment method: {str(e)}", e)
          return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)
        except Exception as e:
          logger.log_error(
            "subscription", f"Unexpected error setting default payment method: {str(e)}", e
          )
          return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)

      # Step 4: Create the subscription
      try:
        stripe_subscription = stripe.Subscription.create(
          customer=customer_id,
          items=[{"price": plan.stripe_price_id}],
          metadata={
            "user_id": str(user.id),
            "plan_id": str(plan.id),
          },
        )
        logger.log_info("subscription", f"Created subscription: {stripe_subscription.id}")
      except stripe.error.InvalidRequestError as e:
        logger.log_error("subscription", f"Error creating subscription: {str(e)}", e)
        return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)
      except Exception as e:
        logger.log_error("subscription", f"Unexpected error creating subscription: {str(e)}", e)
        return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)

      # Create a Subscription model instance matching the serializer fields
      new_subscription = Subscription()
      new_subscription.user = user
      new_subscription.plan = plan  # This matches your serializer's plan field
      new_subscription.status = stripe_subscription.status
      new_subscription.stripe_subscription_id = stripe_subscription.id
      new_subscription.stripe_customer_id = customer_id
      new_subscription.cancel_at_period_end = False  # Default value

      # Handle timestamps
      if hasattr(stripe_subscription, "current_period_start"):
        new_subscription.current_period_start = datetime.fromtimestamp(
          stripe_subscription.current_period_start, tz=timezone.utc
        )

      if hasattr(stripe_subscription, "current_period_end"):
        new_subscription.current_period_end = datetime.fromtimestamp(
          stripe_subscription.current_period_end, tz=timezone.utc
        )

      # Save to database
      new_subscription.save()

      # Get notification service instance
      notification_service = NotificationService.get_instance()

      # Prepare notification data
      notification_data = {
        "subscription_id": str(new_subscription.id),
        "plan_name": plan.name,
        "plan_price": float(plan.price),
        "current_period_end": new_subscription.current_period_end.isoformat()
        if new_subscription.current_period_end
        else None,
      }

      # Notify the user about subscription creation
      notification_service.notify(
        recipient=user,
        title="Subscription Activated",
        body=f"Your {plan.name} subscription has been activated",
        notification_type=NotificationType.SUBSCRIPTION_CREATED,
        data=notification_data,
      )

      # Return the model instance directly
      return ServiceResult(
        success=True,
        data=new_subscription,
        message=AppMsg.SUBSCRIPTION_CREATE_SUCCESS,
      )

    except Exception as e:
      logger.log_error("SubscriptionService - create_subscription", e)
      return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CREATE_FAILURE)

  @staticmethod
  @transaction.atomic
  def cancel_subscription(user: User, subscription_id: str) -> ServiceResult:
    """
    Cancel a subscription (at the end of the billing period)
    """
    try:
      subscription = Subscription.objects.get(id=subscription_id, user=user)

      stripe.Subscription.modify(
        subscription.stripe_subscription_id,
        cancel_at_period_end=True,
      )

      # Update our subscription_record
      subscription.cancel_at_period_end = True
      subscription.save()

      # Get notification service instance
      notification_service = NotificationService.get_instance()

      # Prepare notification data
      notification_data = {
        "subscription_id": str(subscription.id),
        "plan_name": subscription.plan.name,
        "current_period_end": subscription.current_period_end.isoformat()
        if subscription.current_period_end
        else None,
      }

      # Notify the user about subscription cancellation
      notification_service.notify(
        recipient=user,
        title="Subscription Cancellation",
        body=f"Your {subscription.plan.name} subscription has been scheduled for cancellation at the end of the current billing period",
        notification_type=NotificationType.SUBSCRIPTION_CANCELLED,
        data=notification_data,
      )

      return ServiceResult(
        success=True,
        data=subscription,
        message=AppMsg.SUBSCRIPTION_CANCEL_SCHEDULE_SUCCESS,
      )

    except Subscription.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.NO_ACTIVE_SUBSCRIPTION)
    except Exception as e:
      logger.log_error("SubscriptionService - cancel_subscription", e)
      return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CANCEL_FAILURE)

  @staticmethod
  @transaction.atomic
  def cancel_subscription_immediately(user: User, subscription_id: str) -> ServiceResult:
    """
    Cancel a subscription immediately
    """
    try:
      subscription = Subscription.objects.get(id=subscription_id, user=user)

      stripe.Subscription.delete(subscription.stripe_subscription_id)

      # Update our subscription record
      subscription.status = "canceled"
      subscription.save()

      # Get notification service instance
      notification_service = NotificationService.get_instance()

      # Prepare notification data
      notification_data = {
        "subscription_id": str(subscription.id),
        "plan_name": subscription.plan.name,
        "canceled_at": timezone.now().isoformat(),
      }

      # Notify the user about immediate subscription cancellation
      notification_service.notify(
        recipient=user,
        title="Subscription Cancelled",
        body=f"Your {subscription.plan.name} subscription has been cancelled immediately",
        notification_type=NotificationType.SUBSCRIPTION_CANCELLED,
        data=notification_data,
      )

      return ServiceResult(
        success=True,
        data=subscription,
        message=AppMsg.SUBSCRIPTION_CANCEL_IMMEDIATE_SUCCESS,
      )

    except Subscription.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.NO_ACTIVE_SUBSCRIPTION)
    except Exception as e:
      logger.log_error("SubscriptionService - cancel_subscription_immediately", e)
      return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_CANCEL_FAILURE)

  @staticmethod
  def update_subscription_from_webhook(
    subscription_id: str, event_data: Dict[str, Any]
  ) -> ServiceResult:
    """
    Update subscription details from webhook
    """
    try:
      subscription = Subscription.objects.get(stripe_subscription_id=subscription_id)

      # Update subscription status
      subscription.status = event_data.get("status", subscription.status)

      # Update period dates if provided
      current_period_start = event_data.get("current_period_start")
      if current_period_start:
        subscription.current_period_start = datetime.fromtimestamp(current_period_start)

      current_period_end = event_data.get("current_period_end")
      if current_period_end:
        subscription.current_period_end = datetime.fromtimestamp(current_period_end)

      # Update cancel_at_period_end if provided
      cancel_at_period_end = event_data.get("cancel_at_period_end")
      if cancel_at_period_end is not None:
        subscription.cancel_at_period_end = cancel_at_period_end

      subscription.save()

      return ServiceResult(
        success=True,
        message=AppMsg.SUBSCRIPTION_WEBHOOK_UPDATE_SUCCESS,
        data=subscription,
      )
    except Subscription.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.NO_SUBSCRIPTION)
    except Exception as e:
      logger.log_error("SubscriptionService - update_subscription_from_webhook", e)
      return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_WEBHOOK_UPDATE_FAILURE)

  @staticmethod
  def get_active_subscription(user: User) -> ServiceResult:
    """
    Get the user's active subscription if any
    """
    try:
      now = timezone.now()
      subscription = (
        # Added current_period_end__gt to exclude expired subscriptions
        Subscription.objects.filter(user=user, status="active", current_period_end__gt=now)
        .select_related("plan")
        .first()
      )

      if subscription:
        return ServiceResult(
          success=True,
          data=subscription,
          message=AppMsg.SUBSCRIPTION_GET_ACTIVE_SUCCESS,
        )
      else:
        return ServiceResult(
          success=False,
          data=None,
          message=AppMsg.NO_ACTIVE_SUBSCRIPTION,
        )

    except Exception as e:
      logger.log_error("SubscriptionService - get_active_subscription", e)
      return ServiceResult(success=False, message=AppMsg.SUBSCRIPTION_GET_ACTIVE_FAILURE)

  @staticmethod
  def _get_or_create_stripe_customer(user: User) -> str:
    """
    Get or create a Stripe customer for the given user
    Returns the stripe customer ID
    """
    # Step 1: Check if user already has a subscription with a customer ID
    subscription = Subscription.objects.filter(user=user).first()
    if subscription and subscription.stripe_customer_id:
      logger.log_info(
        "subscription",
        f"Found existing Stripe customer ID from subscription: {subscription.stripe_customer_id}",
      )
      return subscription.stripe_customer_id

    # Step 2: Check if a customer already exists in Stripe with this email
    try:
      existing_customers = stripe.Customer.list(email=user.email, limit=1)
      if existing_customers and existing_customers.data:
        customer_id = existing_customers.data[0].id
        logger.log_info("subscription", f"Found existing Stripe customer by email: {customer_id}")
        return customer_id
    except Exception as e:
      logger.log_error("subscription", f"Error searching for existing Stripe customer: {str(e)}", e)
      # Continue with customer creation if search fails

    # Step 3: Create a new customer if no existing customer found
    try:
      logger.log_info("subscription", f"Creating new Stripe customer for user: {user.id}")
      customer = stripe.Customer.create(
        email=user.email,
        name=user.full_name,
        metadata={"user_id": str(user.id)},
      )

      # Optional: Store the customer ID somewhere for future reference
      # This could be on the user model or in a separate table

      return customer.id
    except Exception as e:
      logger.log_error("subscription", f"Error creating Stripe customer: {str(e)}", e)
      raise e

  @staticmethod
  def _check_for_active_subscription(user: User) -> ServiceResult:
    """
    Helper method to check if a user already has an active subscription
    Returns a ServiceResult with success=False if an active subscription exists,
    otherwise returns None
    """
    # Step 1: Check in our database
    now = timezone.now()
    existing_subscription = Subscription.objects.filter(
      user=user, status="active", cancel_at_period_end=False, current_period_end__gt=now
    ).first()

    if existing_subscription:
      logger.log_info(
        "subscription",
        f"User {user.id} already has an active subscription in DB: {existing_subscription.id}",
      )
      return ServiceResult(
        success=False,
        message=AppMsg.SUBSCRIPTION_ALREADY_ACTIVE,
      )

    # Step 2: Double-check directly with Stripe
    try:
      # Get the customer ID if it exists
      customer_id = None
      try:
        existing_customers = stripe.Customer.list(email=user.email, limit=1)
        if existing_customers and existing_customers.data:
          customer_id = existing_customers.data[0].id
      except Exception as e:
        logger.log_error(
          "subscription", f"Error checking for existing Stripe customer: {str(e)}", e
        )
        # Continue with the flow if we can't check Stripe
        return None

      # If we found a customer, check for active subscriptions
      if customer_id:
        try:
          active_subscriptions = stripe.Subscription.list(
            customer=customer_id, status="active", limit=1
          )

          if active_subscriptions and active_subscriptions.data:
            logger.log_info(
              "subscription",
              f"User {user.id} has active subscription in Stripe: {active_subscriptions.data[0].id}",
            )
            return ServiceResult(
              success=False,
              message="You have an active subscription in our payment system. Please contact support if you're having issues accessing your subscription.",
            )
        except Exception as e:
          logger.log_error(
            "subscription", f"Error checking for active Stripe subscriptions: {str(e)}", e
          )
          # Continue with the flow if we can't check Stripe
    except Exception as e:
      logger.log_error("subscription", f"Unexpected error in subscription check: {str(e)}", e)

    # No active subscription found
    return None
