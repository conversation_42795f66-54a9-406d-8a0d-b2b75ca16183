from django.utils import timezone
from rest_framework import serializers
from .models import SubscriptionPlan, Subscription


class SubscriptionPlanSerializer(serializers.ModelSerializer):
  class Meta:
    model = SubscriptionPlan
    fields = ["id", "name", "duration_months", "price", "currency"]


class SubscriptionSerializer(serializers.ModelSerializer):
  plan = SubscriptionPlanSerializer(read_only=True)
  days_remaining = serializers.SerializerMethodField()

  class Meta:
    model = Subscription
    fields = [
      "id",
      "plan",
      "status",
      "current_period_start",
      "current_period_end",
      "cancel_at_period_end",
      "days_remaining",
    ]

  def get_days_remaining(self, obj) -> int:
    """Calculate days remaining in subscription"""

    if not obj.current_period_end:
      return 0

    now = timezone.now()
    if now > obj.current_period_end:
      return 0

    return (obj.current_period_end - now).days


class CreateSubscriptionSerializer(serializers.Serializer):
  plan_id = serializers.CharField()


class CompleteSubscriptionSerializer(serializers.Serializer):
  payment_method_id = serializers.CharField()
  plan_id = serializers.CharField()


class CancelSubscriptionSerializer(serializers.Serializer):
  subscription_id = serializers.CharField()
  immediate = serializers.BooleanField(default=False)


class SetupIntentResponseSerializer(serializers.Serializer):
  client_secret = serializers.CharField()
  customer_id = serializers.CharField()
  price_id = serializers.CharField()
