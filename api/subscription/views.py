from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiTypes

from core.utils.app_response import AppPagination, AppResponse

from .models import SubscriptionPlan, Subscription
from .serializers import (
  SetupIntentResponseSerializer,
  SubscriptionPlanSerializer,
  SubscriptionSerializer,
  CreateSubscriptionSerializer,
  CompleteSubscriptionSerializer,
  CancelSubscriptionSerializer,
)
from .services import SubscriptionService


@extend_schema(tags=["Subscription"])
class SubscriptionPlanViewSet(viewsets.ReadOnlyModelViewSet):
  """
  A viewset for viewing subscription plans
  """

  queryset = SubscriptionPlan.objects.all()
  serializer_class = SubscriptionPlanSerializer
  permission_classes = [permissions.IsAuthenticated]
  pagination_class = AppPagination


@extend_schema(tags=["Subscription"])
class SubscriptionViewSet(viewsets.ViewSet):
  """
  A viewset for managing user subscriptions
  """

  permission_classes = [permissions.IsAuthenticated]

  @extend_schema(
    summary="List subscriptions for current user",
    responses=SubscriptionSerializer(many=True),
  )
  def list(self, request):
    """List all subscriptions for the current user"""
    subscriptions = Subscription.objects.filter(user=request.user).select_related("plan")
    serializer = SubscriptionSerializer(subscriptions, many=True)
    return AppResponse.success(data=serializer.data)

  @extend_schema(
    summary="Retrieve a specific subscription by ID",
    responses=SubscriptionSerializer,
    parameters=[
      OpenApiParameter(
        name="id",
        type=OpenApiTypes.UUID,
        location=OpenApiParameter.PATH,
        description="Subscription ID",
      )
    ],
  )
  def retrieve(self, request, pk=None):
    """Get a specific subscription"""
    subscription = get_object_or_404(Subscription, id=pk, user=request.user)
    serializer = SubscriptionSerializer(subscription)
    return AppResponse.success(data=serializer.data)

  @extend_schema(
    summary="Get the active subscription of current user",
    responses=SubscriptionSerializer,
  )
  @action(detail=False, methods=["get"])
  def active(self, request):
    """Get the user's active subscription if any"""
    result = SubscriptionService.get_active_subscription(request.user)

    if not result.success:
      return AppResponse.error(message=result.message)

    serializer = SubscriptionSerializer(result.data)
    return AppResponse.success(message=result.message, data=serializer.data)

  @extend_schema(
    summary="Create Stripe SetupIntent to start subscription payment flow",
    request=CreateSubscriptionSerializer,
    responses={200: SetupIntentResponseSerializer},
  )
  @action(detail=False, methods=["post"])
  def create_payment_intent(self, request):
    """
    Create a payment intent for subscribing to a plan
    Returns client_secret needed for Flutter Stripe SDK
    """
    serializer = CreateSubscriptionSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    result = SubscriptionService.create_payment_intent(
      request.user, serializer.validated_data["plan_id"]
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    return AppResponse.success(message=result.message, data=result.data)

  @extend_schema(
    summary="Complete the subscription setup with payment method",
    request=CompleteSubscriptionSerializer,
    responses=SubscriptionSerializer,
  )
  @action(detail=False, methods=["post"])
  def complete_subscription(self, request):
    """
    Complete the subscription process after payment method is confirmed
    """
    serializer = CompleteSubscriptionSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    result = SubscriptionService.create_subscription(
      request.user,
      serializer.validated_data["plan_id"],
      serializer.validated_data["payment_method_id"],
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    # Return the subscription details
    subscription_serializer = SubscriptionSerializer(result.data)
    response_data = subscription_serializer.data

    # If there's a client_secret (for confirming the payment), include it
    # Note: Since result.data is now the subscription object directly, we don't need to access it
    # The client_secret would need to be returned separately if needed

    return AppResponse.success(message=result.message, data=response_data)

  @extend_schema(
    request=CancelSubscriptionSerializer,
    responses={200: SubscriptionSerializer},
    summary="Cancel a subscription at period end",
  )
  @action(detail=False, methods=["post"])
  def cancel(self, request):
    """
    Cancel a subscription
    """
    serializer = CancelSubscriptionSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    if serializer.validated_data.get("immediate", False):
      result = SubscriptionService.cancel_subscription_immediately(
        request.user, serializer.validated_data["subscription_id"]
      )
    else:
      result = SubscriptionService.cancel_subscription(
        request.user, serializer.validated_data["subscription_id"]
      )

    if not result.success:
      return AppResponse.error(message=result.message)

    subscription_serializer = SubscriptionSerializer(result.data)
    return AppResponse.success(message=result.message, data=subscription_serializer.data)
