from django.urls import include, path

from drf_spectacular.views import (
  SpectacularAPIView,
  SpectacularRedocView,
  SpectacularSwaggerView,
)


urlpatterns = [
  path("user/", include("api.user.urls")),
  path("auth/", include("api.auth.urls")),
  path("chat/", include("api.chat.urls")),
  path("spark/", include("api.spark.urls")),
  path("review/", include("api.review.urls")),
  path("session/", include("api.session.urls")),
  path("payment/", include("api.payment.urls")),
  path("profile/", include("api.profile.urls")),
  path("activity/", include("api.activity.urls")),
  path("interest/", include("api.interest.urls")),
  path("discovery/", include("api.discovery.urls")),
  path("invitation/", include("api.invitation.urls")),
  path("blocked-user/", include("api.blocked_user.urls")),
  path("subscription/", include("api.subscription.urls")),
  path("notification/", include("api.notification.urls")),
  path("stripe-connect/", include("api.stripe_connect.urls")),
  path("schema/", SpectacularAPIView.as_view(), name="schema"),
  path("schema/docs/", SpectacularSwaggerView.as_view(url_name="schema")),
  path("schema/redoc/", SpectacularRedocView.as_view(url_name="schema")),
]
