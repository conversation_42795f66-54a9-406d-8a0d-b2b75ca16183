from django.db import models
from core.models import AbstractModel
from django.utils.translation import gettext_lazy as _
import random
import string
import logging

from access.models import User
from api.invitation.models import Invitation
from core.utils.qr_code import QRCodeGenerator

logger = logging.getLogger(__name__)


def generate_session_code():
  """Generate a random 8-character alphanumeric session code"""
  chars = string.ascii_uppercase + string.digits
  return "".join(random.choice(chars) for _ in range(8))


class SessionStatus(models.TextChoices):
  """Enum for session statuses"""

  PENDING = "pending", _("Pending")  # Created but not started
  ACTIVE = "active", _("Active")  # Started and ongoing
  PAUSED = "paused", _("Paused")  # Temporarily paused
  COMPLETED = "completed", _("Completed")  # Normally ended
  TERMINATED_EARLY = "terminated_early", _("Terminated Early")  # Deliberately ended early
  EMERGENCY = "emergency", _("Emergency")  # Emergency situation
  EXPIRED = "expired", _("Expired")  # Not started and time passed


class Session(AbstractModel):
  """
  Model to store session data between users.

  A session represents an actual meeting that happens as a result
  of an accepted invitation.
  """

  invitation = models.OneToOneField(
    Invitation,
    on_delete=models.CASCADE,
    related_name="session",
    verbose_name=_("Invitation"),
    help_text=_("The invitation this session belongs to"),
  )

  session_code = models.CharField(
    max_length=8,
    unique=True,
    blank=True,
    verbose_name=_("Session Code"),
    help_text=_("Unique code for QR scanning and session identification"),
  )

  qr_code = models.TextField(
    blank=True,
    verbose_name=_("QR Code"),
    help_text=_("Base64 encoded QR code image generated from the session code"),
  )

  host = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="hosted_sessions",
    verbose_name=_("Host"),
    help_text=_("User who hosts the session (the invitation recipient)"),
  )

  guest = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="guest_sessions",
    verbose_name=_("Guest"),
    help_text=_("User who is invited to the session (the invitation sender)"),
  )

  status = models.CharField(
    max_length=20,
    choices=SessionStatus.choices,
    default=SessionStatus.PENDING,
    verbose_name=_("Status"),
    help_text=_("Current status of the session"),
  )

  started_at = models.DateTimeField(
    null=True,
    blank=True,
    verbose_name=_("Started At"),
    help_text=_("When the session was started"),
  )

  ended_at = models.DateTimeField(
    null=True,
    blank=True,
    verbose_name=_("Ended At"),
    help_text=_("When the session ended"),
  )

  paused_at = models.DateTimeField(
    null=True,
    blank=True,
    verbose_name=_("Paused At"),
    help_text=_("When the session was last paused"),
  )

  total_pause_duration = models.DurationField(
    null=True,
    blank=True,
    default=None,
    verbose_name=_("Total Pause Duration"),
    help_text=_("Total duration of all pauses during the session"),
  )

  emergency_reported_at = models.DateTimeField(
    null=True,
    blank=True,
    verbose_name=_("Emergency Reported At"),
    help_text=_("When an emergency was reported during this session"),
  )

  emergency_details = models.TextField(
    blank=True,
    verbose_name=_("Emergency Details"),
    help_text=_("Additional details about the reported emergency"),
  )

  paused_by = models.ForeignKey(
    User,
    on_delete=models.SET_NULL,
    related_name="paused_sessions",
    null=True,
    blank=True,
    verbose_name=_("Paused By"),
    help_text=_("The user who last paused the session."),
  )

  early_terminated_by = models.ForeignKey(
    User,
    on_delete=models.SET_NULL,
    related_name="terminated_sessions_by",
    null=True,
    blank=True,
    verbose_name=_("Early Terminated By"),
    help_text=_("The user who terminated the session early."),
  )

  emergency_reported_by = models.ForeignKey(
    User,
    on_delete=models.SET_NULL,
    related_name="emergency_reported_sessions_by",
    null=True,
    blank=True,
    verbose_name=_("Emergency Reported By"),
    help_text=_("The user who reported the emergency."),
  )

  early_termination_reason = models.TextField(
    blank=True,
    verbose_name=_("Early Termination Reason"),
    help_text=_("Reason for ending the session early if applicable"),
  )

  class Meta:
    verbose_name = _("Session")
    verbose_name_plural = _("Sessions")
    ordering = ["-created_at"]

  def __str__(self):
    session_code = self.session_code or "[no code]"
    return f"Session {session_code} between {self.host.full_name} and {self.guest.full_name}"

  def save(self, *args, **kwargs):
    logger.info(f"Session save called - pk: {self.pk}, session_code: {self.session_code}")

    # Always generate a session code if it's empty (regardless of whether it's a new or existing session)
    if not self.session_code:
      logger.info("No session code found, generating one")
      max_attempts = 10
      attempt = 0

      while attempt < max_attempts:
        try:
          # Generate a new session code
          session_code = generate_session_code()
          logger.info(f"Generated session code: {session_code}")

          # Check if this code already exists in the database
          if self.__class__.objects.filter(session_code=session_code).exists():
            logger.warning(f"Session code {session_code} already exists, trying again")
            attempt += 1
            continue

          # Set the session code
          self.session_code = session_code

          # Generate QR code
          try:
            self.qr_code = QRCodeGenerator.generate_qr_code_base64(session_code)
            logger.info("QR code generated successfully")
            break  # Exit the loop if successful
          except Exception as e:
            logger.error(f"Error generating QR code: {str(e)}")
            attempt += 1
            if attempt >= max_attempts:
              raise
        except Exception as e:
          logger.error(f"Error in session code generation: {str(e)}")
          attempt += 1
          if attempt >= max_attempts:
            raise Exception("Failed to generate a unique session code after multiple attempts")

    # Now save the model
    logger.info(f"Saving session with code: {self.session_code}")
    result = super().save(*args, **kwargs)
    logger.info("Session saved successfully")
    return result
