from rest_framework import serializers
from django.utils import timezone
from core.utils.app_msg import AppMsg

from .models import Session, SessionStatus
from api.invitation.models import InvitationStatus, Invitation


class SessionSerializer(serializers.ModelSerializer):
  """Serializer for representing session details"""

  host_name = serializers.SerializerMethodField()
  guest_name = serializers.SerializerMethodField()
  invitation_activity_name = serializers.SerializerMethodField()
  duration_minutes = serializers.SerializerMethodField()

  def get_host_name(self, obj) -> str:
    return obj.host.full_name

  def get_guest_name(self, obj) -> str:
    return obj.guest.full_name

  def get_invitation_activity_name(self, obj) -> str:
    return obj.invitation.activity.name

  def get_duration_minutes(self, obj) -> int:
    """Calculate the duration of the session in minutes"""
    if not obj.started_at:
      return 0

    end_time = obj.ended_at or timezone.now()

    # Base duration
    duration = end_time - obj.started_at

    # Subtract pause time if any
    if obj.total_pause_duration:
      duration -= obj.total_pause_duration

    return round(duration.total_seconds() / 60)

  class Meta:
    model = Session
    fields = [
      "id",
      "session_code",
      "invitation",
      "host",
      "host_name",
      "guest",
      "guest_name",
      "qr_code",
      "status",
      "started_at",
      "ended_at",
      "paused_at",
      "total_pause_duration",
      "emergency_reported_at",
      "emergency_details",
      "early_termination_reason",
      "invitation_activity_name",
      "duration_minutes",
      "created_at",
      "updated_at",
    ]
    read_only_fields = [
      "id",
      "session_code",
      "host",
      "guest",
      "status",
      "started_at",
      "ended_at",
      "paused_at",
      "total_pause_duration",
      "emergency_reported_at",
      "created_at",
      "updated_at",
    ]


class StartSessionSerializer(serializers.Serializer):
  """Serializer for starting a session by scanning QR code"""

  session_code = serializers.CharField(max_length=8)

  def validate_session_code(self, value):
    """Validate that the session exists and is pending"""
    try:
      session = Session.objects.get(session_code=value)

      # Check if session is pending
      if session.status != SessionStatus.PENDING:
        if session.status == SessionStatus.ACTIVE:
          raise serializers.ValidationError(AppMsg.SESSION_ALREADY_ACTIVE)
        elif session.status == SessionStatus.COMPLETED:
          raise serializers.ValidationError(AppMsg.SESSION_ALREADY_COMPLETED)
        elif session.status == SessionStatus.EXPIRED:
          raise serializers.ValidationError(AppMsg.SESSION_CODE_EXPIRED)
        elif session.status == SessionStatus.PAUSED:
          raise serializers.ValidationError(AppMsg.SESSION_IS_PAUSED)
        else:
          raise serializers.ValidationError(AppMsg.SESSION_CANNOT_START)

      return session
    except Session.DoesNotExist:
      raise serializers.ValidationError(AppMsg.SESSION_CODE_INVALID)


class SessionActionSerializer(serializers.Serializer):
  """Base serializer for session actions (pause, resume, end, etc.)"""

  session_code = serializers.CharField(max_length=8)

  def validate_session_code(self, value):
    """Validate that the session exists"""
    try:
      session = Session.objects.get(session_code=value)
      return session
    except Session.DoesNotExist:
      raise serializers.ValidationError(AppMsg.SESSION_CODE_INVALID)


class PauseSessionSerializer(SessionActionSerializer):
  """Serializer for pausing an active session"""

  def validate_session_code(self, value):
    """Validate that the session is active"""
    session = super().validate_session_code(value)

    if session.status != SessionStatus.ACTIVE:
      raise serializers.ValidationError(AppMsg.SESSION_CANNOT_PAUSE)

    return session


class ResumeSessionSerializer(SessionActionSerializer):
  """Serializer for resuming a paused session"""

  def validate_session_code(self, value):
    """Validate that the session is paused"""
    session = super().validate_session_code(value)

    if session.status != SessionStatus.PAUSED:
      raise serializers.ValidationError(AppMsg.SESSION_CANNOT_RESUME)

    return session


class EndSessionSerializer(SessionActionSerializer):
  """Serializer for ending an active session"""

  def validate_session_code(self, value):
    """Validate that the session is active or paused"""
    session = super().validate_session_code(value)

    if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
      raise serializers.ValidationError(AppMsg.SESSION_CANNOT_END)

    return session


class LeaveEarlySerializer(SessionActionSerializer):
  """Serializer for leaving a session early"""

  reason = serializers.CharField(required=False, allow_blank=True)

  def validate_session_code(self, value):
    """Validate that the session is active or paused"""
    session = super().validate_session_code(value)

    if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
      raise serializers.ValidationError(AppMsg.SESSION_CANNOT_LEAVE)

    return session


class EmergencySerializer(SessionActionSerializer):
  """Serializer for reporting an emergency during a session"""

  details = serializers.CharField(required=False, allow_blank=True)

  def validate_session_code(self, value):
    """Validate that the session is active or paused"""
    session = super().validate_session_code(value)

    if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
      raise serializers.ValidationError(AppMsg.SESSION_CANNOT_REPORT_EMERGENCY)

    return session


class CreateSessionSerializer(serializers.Serializer):
  """Serializer for creating a session from an accepted invitation"""

  invitation_id = serializers.UUIDField()

  def validate_invitation_id(self, value):
    """Validate that the invitation exists, is accepted, and doesn't already have a session"""
    try:
      invitation = Invitation.objects.get(id=value)

      if invitation.status != InvitationStatus.ACCEPTED:
        raise serializers.ValidationError(AppMsg.SESSION_INVITATION_NOT_ACCEPTED)

      # Check if session already exists
      if hasattr(invitation, "session"):
        raise serializers.ValidationError(AppMsg.SESSION_ALREADY_EXISTS)

      return invitation
    except Invitation.DoesNotExist:
      raise serializers.ValidationError(AppMsg.INVITATION_NOT_FOUND)
