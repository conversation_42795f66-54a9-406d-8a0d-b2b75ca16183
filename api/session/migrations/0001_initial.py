# Generated by Django 4.2.9 on 2025-05-17 10:25

import api.session.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('invitation', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Session',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('session_code', models.CharField(default=api.session.models.generate_session_code, help_text='Unique code for QR scanning and session identification', max_length=8, unique=True, verbose_name='Session Code')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('active', 'Active'), ('paused', 'Paused'), ('completed', 'Completed'), ('terminated_early', 'Terminated Early'), ('emergency', 'Emergency'), ('expired', 'Expired')], default='pending', help_text='Current status of the session', max_length=20, verbose_name='Status')),
                ('started_at', models.DateTimeField(blank=True, help_text='When the session was started', null=True, verbose_name='Started At')),
                ('ended_at', models.DateTimeField(blank=True, help_text='When the session ended', null=True, verbose_name='Ended At')),
                ('paused_at', models.DateTimeField(blank=True, help_text='When the session was last paused', null=True, verbose_name='Paused At')),
                ('total_pause_duration', models.DurationField(blank=True, default=None, help_text='Total duration of all pauses during the session', null=True, verbose_name='Total Pause Duration')),
                ('emergency_reported_at', models.DateTimeField(blank=True, help_text='When an emergency was reported during this session', null=True, verbose_name='Emergency Reported At')),
                ('emergency_details', models.TextField(blank=True, help_text='Additional details about the reported emergency', verbose_name='Emergency Details')),
                ('early_termination_reason', models.TextField(blank=True, help_text='Reason for ending the session early if applicable', verbose_name='Early Termination Reason')),
                ('guest', models.ForeignKey(help_text='User who is invited to the session (the invitation sender)', on_delete=django.db.models.deletion.CASCADE, related_name='guest_sessions', to=settings.AUTH_USER_MODEL, verbose_name='Guest')),
                ('host', models.ForeignKey(help_text='User who hosts the session (the invitation recipient)', on_delete=django.db.models.deletion.CASCADE, related_name='hosted_sessions', to=settings.AUTH_USER_MODEL, verbose_name='Host')),
                ('invitation', models.OneToOneField(help_text='The invitation this session belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='session', to='invitation.invitation', verbose_name='Invitation')),
            ],
            options={
                'verbose_name': 'Session',
                'verbose_name_plural': 'Sessions',
                'ordering': ['-created_at'],
            },
        ),
    ]
