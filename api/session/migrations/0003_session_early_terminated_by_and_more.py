# Generated by Django 4.2.9 on 2025-06-11 17:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('session', '0002_session_qr_code_alter_session_session_code'),
    ]

    operations = [
        migrations.AddField(
            model_name='session',
            name='early_terminated_by',
            field=models.ForeignKey(blank=True, help_text='The user who terminated the session early.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='terminated_sessions_by', to=settings.AUTH_USER_MODEL, verbose_name='Early Terminated By'),
        ),
        migrations.AddField(
            model_name='session',
            name='emergency_reported_by',
            field=models.ForeignKey(blank=True, help_text='The user who reported the emergency.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='emergency_reported_sessions_by', to=settings.AUTH_USER_MODEL, verbose_name='Emergency Reported By'),
        ),
        migrations.AddField(
            model_name='session',
            name='paused_by',
            field=models.ForeignKey(blank=True, help_text='The user who last paused the session.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paused_sessions', to=settings.AUTH_USER_MODEL, verbose_name='Paused By'),
        ),
    ]
