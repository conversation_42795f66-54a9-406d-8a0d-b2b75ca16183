from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from unfold.admin import ModelAdmin
from .models import Session


@admin.register(Session)
class SessionAdmin(ModelAdmin):
  """Admin interface for Session model"""

  def display_qr_code(self, obj):
    """Display QR code as an image in the admin"""
    if obj.qr_code:
      return mark_safe(f'<img src="{obj.qr_code}" width="100" height="100" />')
    return "No QR code"

  display_qr_code.short_description = "QR Code"

  list_display = (
    "session_code",
    "display_qr_code",
    "host",
    "guest",
    "status",
    "started_at",
    "ended_at",
    "duration_display",
    "created_at",
  )
  list_filter = ("status", "created_at", "started_at", "ended_at")
  search_fields = (
    "session_code",
    "host__email",
    "guest__email",
    "host__full_name",
    "guest__full_name",
    "invitation__id",
  )
  readonly_fields = (
    "created_at",
    "updated_at",
    "duration_display",
    "session_code",
    "display_qr_code",
  )
  date_hierarchy = "created_at"
  fieldsets = (
    ("Session Identification", {"fields": ("session_code", "display_qr_code", "invitation")}),
    ("Participants", {"fields": ("host", "guest")}),
    (
      "Session Timeline",
      {
        "fields": (
          "status",
          "started_at",
          "ended_at",
          "paused_at",
          "paused_by",
          "early_terminated_by",
          "emergency_reported_by",
          "total_pause_duration",
          "duration_display",
        )
      },
    ),
    (
      "Early Termination",
      {"fields": ("early_termination_reason",)},
    ),
    (
      "Emergency Information",
      {"fields": ("emergency_reported_at", "emergency_details")},
    ),
    ("Metadata", {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
  )

  def get_queryset(self, request):
    """Optimize queryset with select_related to reduce database queries"""
    return super().get_queryset(request).select_related("host", "guest", "invitation")

  autocomplete_fields = ["host", "guest", "invitation"]

  def duration_display(self, obj):
    """Calculate and display the session duration"""
    if not obj.started_at:
      return "Not started"

    if not obj.ended_at:
      return "In progress"

    # Base duration
    duration = obj.ended_at - obj.started_at

    # Subtract pause time if any
    if obj.total_pause_duration:
      duration -= obj.total_pause_duration

    # Convert to minutes
    minutes = round(duration.total_seconds() / 60)

    if minutes < 60:
      return f"{minutes} minutes"
    else:
      hours = minutes // 60
      remaining_minutes = minutes % 60
      return f"{hours} hours, {remaining_minutes} minutes"

  duration_display.short_description = "Duration"

  def status_display(self, obj):
    """Display status with color coding"""
    status_colors = {
      "pending": "blue",
      "active": "green",
      "paused": "orange",
      "completed": "purple",
      "terminated_early": "red",
      "emergency": "red",
      "expired": "gray",
    }

    color = status_colors.get(obj.status, "black")
    return format_html('<span style="color: {};">{}</span>', color, obj.get_status_display())

  status_display.short_description = "Status"
  status_display.admin_order_field = "status"
