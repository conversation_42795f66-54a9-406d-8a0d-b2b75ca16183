# api/session/urls.py
from django.urls import path
from .views import (
  CreateSessionView,
  StartSessionView,
  PauseSessionView,
  ResumeSessionView,
  EndSessionView,
  LeaveEarlyView,
  ReportEmergencyView,
  HostedSessionsView,
  GuestSessionsView,
  SessionDetailView,
)

urlpatterns = [
  path("create/", CreateSessionView.as_view(), name="create-session"),
  path("start/", StartSessionView.as_view(), name="start-session"),
  path("pause/", PauseSessionView.as_view(), name="pause-session"),
  path("resume/", ResumeSessionView.as_view(), name="resume-session"),
  path("end/", EndSessionView.as_view(), name="end-session"),
  path("leave-early/", LeaveEarlyView.as_view(), name="leave-early"),
  path("emergency/", ReportEmergencyView.as_view(), name="report-emergency"),
  path("hosted/", HostedSessionsView.as_view(), name="hosted-sessions"),
  path("guest/", GuestSessionsView.as_view(), name="guest-sessions"),
  path("<uuid:pk>/", SessionDetailView.as_view(), name="session-detail"),
]
