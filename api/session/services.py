from django.db import transaction
from django.utils import timezone
from datetime import timedelta

from api.consts.service_result import ServiceResult
from core.utils.app_logger import AppLogger
from core.utils.app_msg import AppMsg
from core.utils.notification_helpers import NotificationHelpers
from api.notification.services import NotificationService
from api.notification.utils import NotificationType

from .models import Session, SessionStatus
from api.invitation.models import Invitation, InvitationStatus
from access.models import User

logger = AppLogger()


class SessionService:
  """
  Service class for Session-related business logic.
  Handles session lifecycle operations including start, pause, resume, end,
  early termination, and emergency reporting.
  """

  @staticmethod
  def create_session(invitation: Invitation) -> ServiceResult:
    """
    Create a new session for an accepted invitation.

    Args:
        invitation: The accepted invitation to create a session for

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if invitation.status != InvitationStatus.ACCEPTED:
        return ServiceResult(success=False, message=AppMsg.SESSION_INVITATION_NOT_ACCEPTED)

      # Check if session already exists
      if hasattr(invitation, "session"):
        return ServiceResult(
          success=False,
          message=AppMsg.SESSION_ALREADY_EXISTS,
          data={"session_code": invitation.session.session_code},
        )

      # The recipient of the invitation becomes the host of the session
      host = invitation.recipient
      guest = invitation.sender

      with transaction.atomic():
        session = Session.objects.create(
          invitation=invitation, host=host, guest=guest, status=SessionStatus.PENDING
        )

        # Prepare notification data
        notification_data = {
          "session_id": str(session.id),
          "session_code": session.session_code,
          "host_id": str(host.id),
          "host_name": host.full_name,
          "activity_name": invitation.activity.name,
        }

        # Get notification service instance
        notification_service = NotificationService.get_instance()
        
        # Notify both parties about the session creation
        # For session creation, we always notify both parties
        # Host notification
        notification_service.notify(
          recipient=host,
          title="Session Created",
          body=f"You're hosting a session with {guest.full_name}. Your session code: {session.session_code}",
          notification_type=NotificationType.SESSION_CREATED,
          data=notification_data,
        )

        # Guest notification
        notification_service.notify(
          recipient=guest,
          title="Session Created",
          body=f"{host.full_name} has created a session for your meetup",
          notification_type=NotificationType.SESSION_CREATED,
          sender=host,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.SESSION_CREATED,
          data={"session_id": str(session.id), "session_code": session.session_code},
        )

    except Exception as e:
      logger.log_error("SessionService - create_session", e)
      return ServiceResult(success=False, message=AppMsg.SESSION_CREATE_FAILURE)

  @staticmethod
  def start_session(session: Session) -> ServiceResult:
    """
    Start a pending session (when QR code is scanned).

    Args:
        session: The session to start

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if session.status != SessionStatus.PENDING:
        return ServiceResult(
          success=False, message=AppMsg.SESSION_CANNOT_START, data={"status": session.status}
        )

      notification_service = NotificationService.get_instance()

      host = session.host
      guest = session.guest

      with transaction.atomic():
        # Update session status and start time
        session.status = SessionStatus.ACTIVE
        session.started_at = timezone.now()
        session.save(update_fields=["status", "started_at", "updated_at"])

        # Prepare notification data
        notification_data = {
          "session_id": str(session.id),
          "session_code": session.session_code,
          "started_at": session.started_at.isoformat(),
        }

        # Notify both users that the session has started
        notification_service.notify(
          recipient=host,
          title="Session Started",
          body=f"Your session with {guest.full_name} has started",
          notification_type=NotificationType.SESSION_STARTED,
          data=notification_data,
        )

        notification_service.notify(
          recipient=guest,
          title="Session Started",
          body=f"Your session with {host.full_name} has started",
          notification_type=NotificationType.SESSION_STARTED,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.SESSION_STARTED,
          data={"session_id": str(session.id), "session_code": session.session_code},
        )

    except Exception as e:
      logger.log_error("SessionService - start_session", e)
      return ServiceResult(success=False, message=AppMsg.SESSION_START_FAILURE)

  @staticmethod
  def pause_session(session: Session) -> ServiceResult:
    """
    Pause an active session.

    Args:
        session: The session to pause

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if session.status != SessionStatus.ACTIVE:
        return ServiceResult(
          success=False, message=AppMsg.SESSION_CANNOT_PAUSE, data={"status": session.status}
        )

      notification_service = NotificationService.get_instance()

      host = session.host
      guest = session.guest

      with transaction.atomic():
        # Update session status and pause time
        session.status = SessionStatus.PAUSED
        session.paused_at = timezone.now()
        session.save(update_fields=["status", "paused_at", "updated_at"])

        # Prepare notification data
        notification_data = {
          "session_id": str(session.id),
          "session_code": session.session_code,
          "paused_at": session.paused_at.isoformat(),
        }

        # Notify both users that the session has been paused
        notification_service.notify(
          recipient=host,
          title="Session Paused",
          body=f"Your session with {guest.full_name} has been paused",
          notification_type=NotificationType.SESSION_PAUSED,
          data=notification_data,
        )

        notification_service.notify(
          recipient=guest,
          title="Session Paused",
          body=f"Your session with {host.full_name} has been paused",
          notification_type=NotificationType.SESSION_PAUSED,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.SESSION_PAUSED,
          data={"session_id": str(session.id), "session_code": session.session_code},
        )

    except Exception as e:
      logger.log_error("SessionService - pause_session", e)
      return ServiceResult(success=False, message=AppMsg.SESSION_PAUSE_FAILURE)

  @staticmethod
  def resume_session(session: Session) -> ServiceResult:
    """
    Resume a paused session.

    Args:
        session: The session to resume

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if session.status != SessionStatus.PAUSED:
        return ServiceResult(
          success=False, message=AppMsg.SESSION_CANNOT_RESUME, data={"status": session.status}
        )

      notification_service = NotificationService.get_instance()

      host = session.host
      guest = session.guest

      now = timezone.now()

      with transaction.atomic():
        # Calculate pause duration
        pause_duration = now - session.paused_at

        # Update total pause duration
        if session.total_pause_duration:
          session.total_pause_duration += pause_duration
        else:
          session.total_pause_duration = pause_duration

        # Reset pause time and update status
        session.status = SessionStatus.ACTIVE
        session.paused_at = None
        session.save(update_fields=["status", "paused_at", "total_pause_duration", "updated_at"])

        # Prepare notification data
        notification_data = {
          "session_id": str(session.id),
          "session_code": session.session_code,
          "resumed_at": now.isoformat(),
        }

        # Notify both users that the session has been resumed
        notification_service.notify(
          recipient=host,
          title="Session Resumed",
          body=f"Your session with {guest.full_name} has been resumed",
          notification_type=NotificationType.SESSION_RESUMED,
          data=notification_data,
        )

        notification_service.notify(
          recipient=guest,
          title="Session Resumed",
          body=f"Your session with {host.full_name} has been resumed",
          notification_type=NotificationType.SESSION_RESUMED,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.SESSION_RESUMED,
          data={"session_id": str(session.id), "session_code": session.session_code},
        )

    except Exception as e:
      logger.log_error("SessionService - resume_session", e)
      return ServiceResult(success=False, message=AppMsg.SESSION_RESUME_FAILURE)

  @staticmethod
  def end_session(session: Session, action_initiator: User = None) -> ServiceResult:
    """
    End an active or paused session normally.

    Args:
        session: The session to end
        action_initiator: The user who initiated the end action (optional)

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
        return ServiceResult(
          success=False, message=AppMsg.SESSION_CANNOT_END, data={"status": session.status}
        )

      notification_service = NotificationService.get_instance()

      host = session.host
      guest = session.guest
      
      # Determine notification recipients based on who initiated the action
      # If no initiator specified, notify both parties
      notify_host = True
      notify_guest = True
      
      if action_initiator:
        notification_recipient, notification_sender_name = NotificationHelpers.determine_session_participants(
          action_initiator=action_initiator,
          session_host=host,
          session_guest=guest
        )
        notify_host = notification_recipient.id == host.id
        notify_guest = notification_recipient.id == guest.id

      now = timezone.now()

      with transaction.atomic():
        # If session was paused, add the final pause duration
        if session.status == SessionStatus.PAUSED:
          pause_duration = now - session.paused_at
          if session.total_pause_duration:
            session.total_pause_duration += pause_duration
          else:
            session.total_pause_duration = pause_duration

        # Update session status and end time
        session.status = SessionStatus.COMPLETED
        session.ended_at = now
        session.paused_at = None
        session.save(
          update_fields=["status", "ended_at", "paused_at", "total_pause_duration", "updated_at"]
        )

        # Calculate session duration in minutes
        if session.started_at:
          duration = session.ended_at - session.started_at
          if session.total_pause_duration:
            duration -= session.total_pause_duration
          duration_minutes = round(duration.total_seconds() / 60)
        else:
          duration_minutes = 0

        # Prepare notification data
        notification_data = {
          "session_id": str(session.id),
          "session_code": session.session_code,
          "ended_at": session.ended_at.isoformat(),
          "duration_minutes": duration_minutes,
        }

        # Notify users about the session ending based on who initiated the action
        if notify_host:
          notification_service.notify(
            recipient=host,
            title="Session Ended",
            body=f"Your session with {guest.full_name} has ended",
            notification_type=NotificationType.SESSION_ENDED,
            data=notification_data,
          )

        if notify_guest:
          notification_service.notify(
            recipient=guest,
            title="Session Ended",
            body=f"Your session with {host.full_name} has ended",
            notification_type=NotificationType.SESSION_ENDED,
            data=notification_data,
          )

        return ServiceResult(
          success=True,
          message=AppMsg.SESSION_ENDED,
          data={"session_id": str(session.id), "session_code": session.session_code},
        )

    except Exception as e:
      logger.log_error("SessionService - end_session", e)
      return ServiceResult(success=False, message=AppMsg.SESSION_END_FAILURE)

  @staticmethod
  def leave_early(session: Session, action_initiator: User, reason: str = "") -> ServiceResult:
    """
    Leave a session early before normal completion.

    Args:
        session: The session to leave early
        action_initiator: The user who initiated the early termination
        reason: Optional reason for leaving early

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
        return ServiceResult(
          success=False, message=AppMsg.SESSION_CANNOT_LEAVE, data={"status": session.status}
        )

      notification_service = NotificationService.get_instance()

      host = session.host
      guest = session.guest
      
      # Determine who should receive the notification based on who initiated the action
      notification_recipient, notification_sender_name = NotificationHelpers.determine_session_participants(
        action_initiator=action_initiator,
        session_host=host,
        session_guest=guest
      )

      now = timezone.now()

      with transaction.atomic():
        # If session was paused, add the final pause duration
        if session.status == SessionStatus.PAUSED:
          pause_duration = now - session.paused_at
          if session.total_pause_duration:
            session.total_pause_duration += pause_duration
          else:
            session.total_pause_duration = pause_duration

        # Update session status, end time, and early termination reason
        session.status = SessionStatus.TERMINATED_EARLY
        session.ended_at = now
        session.paused_at = None
        session.early_termination_reason = reason
        session.save(
          update_fields=[
            "status",
            "ended_at",
            "paused_at",
            "early_termination_reason",
            "total_pause_duration",
            "updated_at",
          ]
        )

        # Calculate session duration in minutes
        if session.started_at:
          duration = session.ended_at - session.started_at
          if session.total_pause_duration:
            duration -= session.total_pause_duration
          duration_minutes = round(duration.total_seconds() / 60)
        else:
          duration_minutes = 0

        # Prepare notification data
        notification_data = {
          "session_id": str(session.id),
          "session_code": session.session_code,
          "ended_at": session.ended_at.isoformat(),
          "duration_minutes": duration_minutes,
          "reason": reason if reason else "No reason provided",
        }

        # Notify the appropriate recipient that the session has been terminated early
        notification_service.notify(
          recipient=notification_recipient,
          title="Session Ended Early",
          body=f"{notification_sender_name} has terminated the session early",
          notification_type=NotificationType.SESSION_ENDED_EARLY,
          sender=action_initiator,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.SESSION_LEFT_EARLY,
          data={"session_id": str(session.id), "session_code": session.session_code},
        )

    except Exception as e:
      logger.log_error("SessionService - leave_early", e)
      return ServiceResult(success=False, message=AppMsg.SESSION_LEAVE_FAILURE)

  @staticmethod
  def report_emergency(session: Session, details: str = "") -> ServiceResult:
    """
    Report an emergency during a session.

    Args:
        session: The session where emergency is being reported
        details: Optional details about the emergency

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
        return ServiceResult(
          success=False,
          message=AppMsg.SESSION_CANNOT_REPORT_EMERGENCY,
          data={"status": session.status},
        )

      notification_service = NotificationService.get_instance()

      host = session.host
      guest = session.guest

      now = timezone.now()

      with transaction.atomic():
        # If session was paused, add the final pause duration
        if session.status == SessionStatus.PAUSED:
          pause_duration = now - session.paused_at
          if session.total_pause_duration:
            session.total_pause_duration += pause_duration
          else:
            session.total_pause_duration = pause_duration

        # Update session status and emergency details
        session.status = SessionStatus.EMERGENCY
        session.ended_at = now
        session.emergency_reported_at = now
        session.paused_at = None
        session.emergency_details = details
        session.save(
          update_fields=[
            "status",
            "ended_at",
            "paused_at",
            "emergency_reported_at",
            "emergency_details",
            "total_pause_duration",
            "updated_at",
          ]
        )

        # Prepare notification data
        notification_data = {
          "session_id": str(session.id),
          "session_code": session.session_code,
          "emergency_reported_at": session.emergency_reported_at.isoformat(),
          "details": details if details else "No details provided",
        }

        # TODO: Call the actual emergency

        # Notify both users about the emergency
        notification_service.notify(
          recipient=host,
          title="Emergency Reported",
          body="An emergency has been reported during your session. Authorities have been notified.",
          notification_type=NotificationType.SESSION_EMERGENCY,
          data=notification_data,
        )

        notification_service.notify(
          recipient=guest,
          title="Emergency Reported",
          body="An emergency has been reported during your session. Authorities have been notified.",
          notification_type=NotificationType.SESSION_EMERGENCY,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.SESSION_EMERGENCY_REPORTED,
          data={"session_id": str(session.id), "session_code": session.session_code},
        )

    except Exception as e:
      logger.log_error("SessionService - report_emergency", e)
      return ServiceResult(success=False, message=AppMsg.SESSION_EMERGENCY_REPORT_FAILURE)

  # TODO: Add marking mechanism
  @staticmethod
  def mark_expired_sessions():
    """
    Utility method to mark sessions as expired if they were never started.
    This could be run as a scheduled task.

    Returns:
        Number of sessions marked as expired
    """
    try:
      # Sessions created more than 24 hours ago that are still pending are considered expired
      cutoff_time = timezone.now() - timedelta(hours=24)

      expired_count = Session.objects.filter(
        status=SessionStatus.PENDING, created_at__lt=cutoff_time
      ).update(status=SessionStatus.EXPIRED)

      if expired_count > 0:
        logger.log_info(
          "SessionService - mark_expired_sessions", f"Marked {expired_count} sessions as expired"
        )

      return expired_count

    except Exception as e:
      logger.log_error("SessionService - mark_expired_sessions", e)
      return 0
