from django.db import models
from rest_framework.generics import GenericAPIView, ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema
from drf_spectacular.types import OpenApiTypes

from core.utils.app_response import AppResponse
from core.utils.app_logger import AppLogger
from api.consts.swagger_utils import Swagger<PERSON>ommonExamples

from .models import Session
from .serializers import (
  SessionSerializer,
  CreateSessionSerializer,
  StartSessionSerializer,
  PauseSessionSerializer,
  ResumeSessionSerializer,
  EndSessionSerializer,
  LeaveEarlySerializer,
  EmergencySerializer,
)
from .services import SessionService


logger = AppLogger()


@extend_schema(
  tags=["Session"],
  responses={
    200: CreateSessionSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Create a session from an accepted invitation",
  description="Creates a new session based on an accepted invitation. The invitation recipient becomes the host.",
)
class CreateSessionView(GenericAPIView):
  """
  Creates a new session from an accepted invitation.
  The invitation recipient becomes the host of the session.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = CreateSessionSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    invitation = serializer.validated_data["invitation_id"]
    result = SessionService.create_session(invitation=invitation)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Session"],
  responses={
    200: StartSessionSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Start a session by scanning QR code",
  description="Starts a pending session by providing the session code from the QR scan.",
)
class StartSessionView(GenericAPIView):
  """
  Starts a pending session by scanning the QR code.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = StartSessionSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    session = serializer.validated_data["session_code"]
    result = SessionService.start_session(session=session)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Session"],
  responses={
    200: PauseSessionSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Pause an active session",
  description="Temporarily pauses an active session.",
)
class PauseSessionView(GenericAPIView):
  """
  Pauses an active session.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = PauseSessionSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    session = serializer.validated_data["session_code"]
    result = SessionService.pause_session(session=session)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Session"],
  responses={
    200: ResumeSessionSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Resume a paused session",
  description="Resumes a previously paused session.",
)
class ResumeSessionView(GenericAPIView):
  """
  Resumes a paused session.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = ResumeSessionSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    session = serializer.validated_data["session_code"]
    result = SessionService.resume_session(session=session)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Session"],
  responses={
    200: EndSessionSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="End a session normally",
  description="Ends an active or paused session normally.",
)
class EndSessionView(GenericAPIView):
  """
  Ends an active or paused session normally.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = EndSessionSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    session = serializer.validated_data["session_code"]
    result = SessionService.end_session(session=session)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Session"],
  responses={
    200: LeaveEarlySerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Leave a session early",
  description="Terminates a session early with an optional reason.",
)
class LeaveEarlyView(GenericAPIView):
  """
  Leaves a session early before normal completion.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = LeaveEarlySerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    session = serializer.validated_data["session_code"]
    reason = serializer.validated_data.get("reason", "")
    result = SessionService.leave_early(session=session, reason=reason)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Session"],
  responses={
    200: EmergencySerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Report an emergency during a session",
  description="Reports an emergency situation during a session with optional details.",
)
class ReportEmergencyView(GenericAPIView):
  """
  Reports an emergency during a session.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = EmergencySerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    session = serializer.validated_data["session_code"]
    details = serializer.validated_data.get("details", "")
    result = SessionService.report_emergency(session=session, details=details)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Session"],
  responses={
    200: SessionSerializer,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="List user's hosted sessions",
  description="Get a list of sessions where the authenticated user is the host.",
)
class HostedSessionsView(ListAPIView):
  """
  Returns a list of sessions where the authenticated user is the host.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = SessionSerializer

  def get_queryset(self):
    return Session.objects.filter(host=self.request.user)

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())

    # Apply status filter if provided
    status = request.query_params.get("status")
    if status:
      queryset = queryset.filter(status=status)

    serializer = self.get_serializer(queryset, many=True)
    return AppResponse.success(data=serializer.data)


@extend_schema(
  tags=["Session"],
  responses={
    200: SessionSerializer,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="List user's guest sessions",
  description="Get a list of sessions where the authenticated user is the guest.",
)
class GuestSessionsView(ListAPIView):
  """
  Returns a list of sessions where the authenticated user is the guest.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = SessionSerializer

  def get_queryset(self):
    return Session.objects.filter(guest=self.request.user)

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())

    # Apply status filter if provided
    status = request.query_params.get("status")
    if status:
      queryset = queryset.filter(status=status)

    serializer = self.get_serializer(queryset, many=True)
    return AppResponse.success(data=serializer.data)


@extend_schema(
  tags=["Session"],
  responses={
    200: SessionSerializer,
    401: OpenApiTypes.OBJECT,
    404: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Get session details",
  description="Get detailed information about a specific session.",
)
class SessionDetailView(RetrieveAPIView):
  """
  Returns detailed information about a specific session.
  User must be either the host or guest.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = SessionSerializer

  def get_queryset(self):
    user = self.request.user
    return Session.objects.filter(models.Q(host=user) | models.Q(guest=user))

  def retrieve(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance)
    return AppResponse.success(data=serializer.data)
