from api.user.utils import UserToggleFieldView, get_toggle_field_schema


@get_toggle_field_schema("is_online", "Online")
class UserMeOnlineStatusUpdateView(UserToggleFieldView):
  """
  View to update the online status of the currently authenticated user.
  """

  field_name = "is_online"


@get_toggle_field_schema("is_live_location_on", "Live Location")
class UserMeLiveLocationUpdateView(UserToggleFieldView):
  """
  View to update the live location status of the currently authenticated user.
  """

  field_name = "is_live_location_on"


@get_toggle_field_schema("is_ask_me_on", "Ask Me Status")
class UserMeAskMeUpdateView(UserToggleFieldView):
  """
  View to update the ask me status of the currently authenticated user.
  """

  field_name = "is_ask_me_on"


@get_toggle_field_schema("is_notification_on", "Allow Notifications")
class UserMeNotificationPermissionUpdateView(UserToggleFieldView):
  """
  View to update notifications permission of the currently authenticated user.
  """

  field_name = "is_notification_on"
