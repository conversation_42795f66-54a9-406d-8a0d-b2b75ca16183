from drf_spectacular.utils import extend_schema, OpenApiExample
from rest_framework.generics import GenericAPIView
from api.consts.permissions import IsAuthenticated
from drf_spectacular.types import OpenApiTypes
from rest_framework import serializers
from core.utils.app_response import App<PERSON><PERSON>ponse


def get_toggle_field_schema(field_name, human_readable_name=None):
  """
  Generate an extend_schema decorator for toggle field views.

  Args:
      field_name (str): The name of the field in the model (e.g., "is_online")
      human_readable_name (str, optional): A human-readable name for the field.
          If not provided, it will be generated from the field_name.

  Returns:
      function: A properly configured extend_schema decorator
  """
  if not human_readable_name:
    # Convert snake_case to Title Case (e.g., "is_online" to "Online")
    human_readable_name = field_name.replace("is_", "").replace("_", " ").title()

  return extend_schema(
    tags=["User"],
    summary=f"Update user {human_readable_name.lower()} status",
    description=f"Updates the {human_readable_name.lower()} status of the currently authenticated user.",
    responses={
      200: OpenApiTypes.OBJECT,
      401: OpenApiTypes.OBJECT,
    },
    examples=[
      OpenApiExample(
        name=f"Enable {human_readable_name}",
        value={field_name: True},
        request_only=True,
      ),
      OpenApiExample(
        name=f"Disable {human_readable_name}",
        value={field_name: False},
        request_only=True,
      ),
      OpenApiExample(
        name="Success Response",
        value={field_name: True},
        response_only=True,
        status_codes=["200"],
      ),
      OpenApiExample(
        name="Invalid Token Error",
        value={
          "detail": "Authentication credentials were not provided.",
          # "status_code": 401,
        },
        response_only=True,
        status_codes=["401"],
      ),
    ],
  )


class UserToggleFieldView(GenericAPIView):
  """
  Base view for toggling boolean fields on the user model.
  """

  permission_classes = [IsAuthenticated]
  field_name = None  # Should be defined by subclasses

  def get_serializer_class(self):
    """
    Dynamically create a serializer class for the specific field.
    """
    field_name = self.field_name

    # Create a unique name for each serializer based on the field name
    serializer_name = f"{field_name.title().replace('_', '')}ToggleSerializer"

    # Create the serializer with a unique class name
    class_dict = {field_name: serializers.BooleanField(required=True)}

    # Create the serializer class with a unique name
    DynamicSerializer = type(serializer_name, (serializers.Serializer,), class_dict)

    return DynamicSerializer

  def patch(self, request, *args, **kwargs):
    if not self.field_name:
      raise ValueError("field_name must be defined by subclasses")

    serializer = self.get_serializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    user = request.user
    setattr(user, self.field_name, serializer.validated_data[self.field_name])
    user.save(update_fields=[self.field_name])

    return AppResponse.success(
      data={self.field_name: getattr(user, self.field_name)},
      message=f"User {self.field_name} updated successfully",
    )
