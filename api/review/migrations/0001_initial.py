# Generated by Django 4.2.9 on 2025-05-17 11:02

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('session', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('score', models.IntegerField(help_text='Rating score from 1 to 5', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Score')),
                ('comment', models.TextField(blank=True, help_text='Optional comments about the experience', verbose_name='Comment')),
                ('reviewee', models.ForeignKey(help_text='User who received the review', on_delete=django.db.models.deletion.CASCADE, related_name='reviews_received', to=settings.AUTH_USER_MODEL, verbose_name='Reviewee')),
                ('reviewer', models.ForeignKey(help_text='User who left the review', on_delete=django.db.models.deletion.CASCADE, related_name='reviews_given', to=settings.AUTH_USER_MODEL, verbose_name='Reviewer')),
                ('session', models.ForeignKey(help_text='The session being reviewed', on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='session.session', verbose_name='Session')),
            ],
            options={
                'verbose_name': 'Review',
                'verbose_name_plural': 'Reviews',
                'ordering': ['-created_at'],
                'unique_together': {('session', 'reviewer')},
            },
        ),
    ]
