# api/review/services.py
from django.db import transaction
from django.db.models import Avg

from api.consts.service_result import ServiceResult
from core.utils.app_logger import AppLogger
from core.utils.app_msg import AppMsg
from core.utils.notification_helpers import NotificationHelpers
from api.notification.services import NotificationService
from api.notification.utils import NotificationType

from access.models import User
from .models import Review

logger = AppLogger()


class ReviewService:
  """
  Service class for Review-related business logic.
  Handles creating reviews and retrieving review summaries.
  """

  @staticmethod
  def create_review(reviewer, session, score, comment="") -> ServiceResult:
    """
    Create a new review for a completed session.

    Args:
        reviewer: The user creating the review
        session: The completed session being reviewed
        score: Rating score (1-5)
        comment: Optional comment about the experience

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      # Determine who is being reviewed (the other person in the session)
      if reviewer == session.host:
        reviewee = session.guest
      elif reviewer == session.guest:
        reviewee = session.host
      else:
        return ServiceResult(success=False, message=AppMsg.REVIEW_NOT_SESSION_PARTICIPANT)

      # Check if user already reviewed this session
      if Review.objects.filter(session=session, reviewer=reviewer).exists():
        return ServiceResult(success=False, message=AppMsg.REVIEW_ALREADY_SUBMITTED)

      with transaction.atomic():
        # Create the review
        review = Review.objects.create(
          session=session, reviewer=reviewer, reviewee=reviewee, score=score, comment=comment
        )

        # Notify the reviewee using our helper
        notification_service = NotificationService.get_instance()

        # Determine notification participants using our helper
        # For reviews, the reviewer is the action initiator
        notification_recipient, notification_sender_name = (
          NotificationHelpers.determine_review_participants(
            action_initiator=reviewer,
            reviewer=reviewer,
            reviewed_user=reviewee,
          )
        )

        notification_service.notify(
          recipient=notification_recipient,
          title="New Review Received",
          body=f"{notification_sender_name} has left you a review",
          notification_type=NotificationType.REVIEW_RECEIVED,
          sender=reviewer,
          data={"review_id": str(review.id), "session_id": str(session.id), "score": score},
        )

        return ServiceResult(
          success=True, message=AppMsg.REVIEW_CREATED, data={"review_id": str(review.id)}
        )

    except Exception as e:
      logger.log_error("ReviewService - create_review", e)
      return ServiceResult(success=False, message=AppMsg.REVIEW_CREATE_FAILURE)

  @staticmethod
  def get_user_review_summary(user_id) -> ServiceResult:
    """
    Get a summary of reviews received by a user.

    Args:
        user_id: The ID of the user to get reviews for

    Returns:
        ServiceResult with success status, message, and data containing review summary
    """
    try:
      try:
        user = User.objects.get(id=user_id)
      except User.DoesNotExist:
        return ServiceResult(success=False, message=AppMsg.USER_NOT_FOUND)

      # Get reviews received by the user
      reviews = Review.objects.filter(reviewee=user).order_by("-created_at")

      # Calculate statistics
      reviews_count = reviews.count()

      if reviews_count > 0:
        average_score = reviews.aggregate(avg=Avg("score"))["avg"]
        # Round to 1 decimal place
        average_score = round(average_score, 1)
      else:
        average_score = 0.0

      # Get recent reviews (limit to 5)
      recent_reviews = reviews[:5]

      return ServiceResult(
        success=True,
        message=AppMsg.REVIEW_SUMMARY_RETRIEVED,
        data={
          "user_id": str(user.id),
          "full_name": user.full_name,
          "average_score": average_score,
          "reviews_count": reviews_count,
          "recent_reviews": recent_reviews,
        },
      )

    except Exception as e:
      logger.log_error("ReviewService - get_user_review_summary", e)
      return ServiceResult(success=False, message=AppMsg.REVIEW_SUMMARY_RETRIEVAL_FAILURE)
