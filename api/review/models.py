# api/review/models.py
from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _

from core.models import AbstractModel
from access.models import User
from api.session.models import Session


class Review(AbstractModel):
  """
  Model to store reviews left by users after completed sessions.

  Each review includes a score (1-5) and optional comments.
  A session can only have one review from each participant.
  """

  session = models.ForeignKey(
    Session,
    on_delete=models.CASCADE,
    related_name="reviews",
    verbose_name=_("Session"),
    help_text=_("The session being reviewed"),
  )

  reviewer = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="reviews_given",
    verbose_name=_("Reviewer"),
    help_text=_("User who left the review"),
  )

  reviewee = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="reviews_received",
    verbose_name=_("Reviewee"),
    help_text=_("User who received the review"),
  )

  score = models.IntegerField(
    validators=[<PERSON>V<PERSON>ueValidator(1), <PERSON>V<PERSON>ueValidator(5)],
    verbose_name=_("Score"),
    help_text=_("Rating score from 1 to 5"),
  )

  comment = models.TextField(
    blank=True,
    verbose_name=_("Comment"),
    help_text=_("Optional comments about the experience"),
  )

  class Meta:
    verbose_name = _("Review")
    verbose_name_plural = _("Reviews")
    ordering = ["-created_at"]
    # Ensure a user can only review a session once
    unique_together = [["session", "reviewer"]]

  def __str__(self):
    return f"Review by {self.reviewer.full_name} for {self.reviewee.full_name} ({self.score}/5)"
