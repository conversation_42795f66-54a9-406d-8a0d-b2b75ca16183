from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Review


@admin.register(Review)
class ReviewAdmin(ModelAdmin):
  """Admin interface for Review model"""

  list_display = (
    "id",
    "reviewer",
    "reviewee",
    "score",
    "session",
    "created_at",
  )
  list_filter = ("score", "created_at")
  search_fields = (
    "reviewer__email",
    "reviewee__email",
    "reviewer__full_name",
    "reviewee__full_name",
    "comment",
  )
  readonly_fields = ("created_at", "updated_at")
  fieldsets = (
    ("Review Information", {"fields": ("reviewer", "reviewee", "session", "score")}),
    ("Content", {"fields": ("comment",)}),
    ("Metadata", {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
  )

  def get_queryset(self, request):
    """Optimize queryset with select_related to reduce database queries"""
    return super().get_queryset(request).select_related("reviewer", "reviewee", "session")

  autocomplete_fields = ["reviewer", "reviewee", "session"]
