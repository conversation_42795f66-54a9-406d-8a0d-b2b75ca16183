# api/review/serializers.py
from rest_framework import serializers

from core.utils.app_msg import AppMsg
from .models import Review
from api.session.models import Session, SessionStatus


class ReviewSerializer(serializers.ModelSerializer):
  """Serializer for representing review details"""

  reviewer_name = serializers.SerializerMethodField()
  reviewee_name = serializers.SerializerMethodField()

  def get_reviewer_name(self, obj) -> str:
    return obj.reviewer.full_name

  def get_reviewee_name(self, obj) -> str:
    return obj.reviewee.full_name

  class Meta:
    model = Review
    fields = [
      "id",
      "session",
      "reviewer",
      "reviewer_name",
      "reviewee",
      "reviewee_name",
      "score",
      "comment",
      "created_at",
      "updated_at",
    ]
    read_only_fields = [
      "id",
      "reviewer",
      "reviewer_name",
      "reviewee",
      "reviewee_name",
      "created_at",
      "updated_at",
    ]


class CreateReviewSerializer(serializers.Serializer):
  """Serializer for creating a new review"""

  session_id = serializers.UUIDField()
  score = serializers.IntegerField(min_value=1, max_value=5)
  comment = serializers.CharField(required=False, allow_blank=True)

  def validate_session_id(self, value):
    """Validate that the session exists and is completed"""
    try:
      session = Session.objects.get(id=value)

      # Check if session is completed or terminated
      completed_statuses = [
        SessionStatus.COMPLETED,
        SessionStatus.TERMINATED_EARLY,
        SessionStatus.EMERGENCY,
      ]

      if session.status not in completed_statuses:
        raise serializers.ValidationError(AppMsg.REVIEW_SESSION_NOT_COMPLETED)

      # Check if the user has already reviewed this session
      user = self.context["request"].user
      if Review.objects.filter(session=session, reviewer=user).exists():
        raise serializers.ValidationError(AppMsg.REVIEW_ALREADY_SUBMITTED)

      return session
    except Session.DoesNotExist:
      raise serializers.ValidationError(AppMsg.SESSION_NOT_FOUND)


class UserReviewSummarySerializer(serializers.Serializer):
  """Serializer for user review summary"""

  user_id = serializers.UUIDField()
  full_name = serializers.CharField(read_only=True)
  average_score = serializers.FloatField(read_only=True)
  reviews_count = serializers.IntegerField(read_only=True)
  recent_reviews = ReviewSerializer(many=True, read_only=True)
