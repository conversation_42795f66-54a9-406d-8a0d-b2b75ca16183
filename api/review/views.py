# api/review/views.py
from rest_framework.generics import GenericAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema
from drf_spectacular.types import OpenApiTypes

from core.utils.app_response import AppResponse
from core.utils.app_logger import A<PERSON><PERSON>ogger
from api.consts.swagger_utils import Swa<PERSON><PERSON><PERSON><PERSON>Examples

from .models import Review
from .serializers import (
  ReviewSerializer,
  CreateReviewSerializer,
  UserReviewSummarySerializer,
)
from .services import ReviewService


logger = AppLogger()


@extend_schema(
  tags=["Review"],
  responses={
    200: CreateReviewSerializer,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Create a review for a completed session",
  description="Submit a review for another user after a completed session.",
)
class CreateReviewView(GenericAPIView):
  """
  <PERSON>les creating a review for a completed session.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = CreateReviewSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data, context={"request": request})
    serializer.is_valid(raise_exception=True)

    reviewer = request.user
    session = serializer.validated_data["session_id"]
    score = serializer.validated_data["score"]
    comment = serializer.validated_data.get("comment", "")

    result = ReviewService.create_review(
      reviewer=reviewer,
      session=session,
      score=score,
      comment=comment,
    )

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Review"],
  responses={
    200: UserReviewSummarySerializer,
    401: OpenApiTypes.OBJECT,
    404: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Get user review summary",
  description="Get a summary of reviews received by a specific user.",
)
class UserReviewSummaryView(GenericAPIView):
  """
  Returns a summary of reviews received by a user.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = UserReviewSummarySerializer

  def get(self, request, user_id, *args, **kwargs):
    result = ReviewService.get_user_review_summary(user_id=user_id)

    if result.success:
      # Serialize the recent reviews
      recent_reviews_data = ReviewSerializer(result.data.pop("recent_reviews"), many=True).data

      # Add serialized reviews back to the result data
      result.data["recent_reviews"] = recent_reviews_data

      return AppResponse.success(data=result.data)

    return AppResponse.error(message=result.message, status=404)


@extend_schema(
  tags=["Review"],
  responses={
    200: ReviewSerializer,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="List reviews received by the authenticated user",
  description="Get a list of reviews that the authenticated user has received.",
)
class MyReviewsView(ListAPIView):
  """
  Returns a list of reviews received by the authenticated user.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = ReviewSerializer

  def get_queryset(self):
    return Review.objects.filter(reviewee=self.request.user).order_by("-created_at")

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())
    serializer = self.get_serializer(queryset, many=True)
    return AppResponse.success(data=serializer.data)


@extend_schema(
  tags=["Review"],
  responses={
    200: ReviewSerializer,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="List reviews given by the authenticated user",
  description="Get a list of reviews that the authenticated user has given to others.",
)
class ReviewsGivenView(ListAPIView):
  """
  Returns a list of reviews given by the authenticated user.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = ReviewSerializer

  def get_queryset(self):
    return Review.objects.filter(reviewer=self.request.user).order_by("-created_at")

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())
    serializer = self.get_serializer(queryset, many=True)
    return AppResponse.success(data=serializer.data)
