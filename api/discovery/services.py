from access.models import User
from django.contrib.gis.measure import D
from typing import Optional, List
from datetime import date, timedelta
from django.contrib.gis.db.models.functions import Distance
from django.db.models import Count

from api.blocked_user.utils import exclude_blocked_users
from api.consts.service_result import ServiceResult
from api.stripe_connect.models import StripeConnectAccount
from core.utils.app_logger import AppLogger
from core.utils.app_msg import AppMsg
from rentie.settings import DISCOVERY_MAX_DISTANCE_KM

logger = AppLogger()


class DiscoverService:
  @staticmethod
  def get_discoverable_users(
    requesting_user: User,
    max_distance_km=DISCOVERY_MAX_DISTANCE_KM,
    genders: Optional[List[str]] = None,
    min_age: Optional[int] = None,
    max_age: Optional[int] = None,
    min_photos: Optional[int] = None,
  ) -> ServiceResult:
    """
    Fetches users suitable for display on the discovery map as a QuerySet,
    wrapped in a ServiceResult.

    Returns:
        ServiceResult: success, message, and data (QuerySet or empty list)
    """
    log_prefix = "DiscoverService - get_discoverable_users"

    # Validate and sanitize max_distance_km
    if max_distance_km is not None and max_distance_km > DISCOVERY_MAX_DISTANCE_KM:
      max_distance_km = DISCOVERY_MAX_DISTANCE_KM

    # Validate and sanitize age filters
    if min_age is not None and min_age < 18:
      min_age = 18

    if max_age is not None and max_age > 120:
      max_age = 120

    # Validate and sanitize photo count filter
    if min_photos is not None:
      if min_photos < 0:
        min_photos = 0
      elif min_photos > 10:
        min_photos = 10

    # Process gender filter if it's a string (comma-separated)
    if isinstance(genders, str) and genders:
      genders = [g.strip() for g in genders.split(",")]

    try:
      # Check if user is authenticated first
      if not requesting_user.is_authenticated:
        logger.log_info(
          log_prefix,
          "User is not authenticated. Cannot perform discovery.",
        )
        return ServiceResult(
          success=False,
          message="Authentication required",
          data=[],
        )

      if not requesting_user.location:
        logger.log_info(
          log_prefix,
          f"Requesting user {requesting_user.id} location not available. Cannot perform distance filtering.",
        )
        return ServiceResult(
          success=False,
          message=AppMsg.DISCOVERY_USER_LOCATION_NOT_AVAILABLE,
          data=[],
        )

      # Start with base filters
      queryset = (
        User.objects.filter(
          is_active=True,
          is_admin=False,
          location__isnull=False,
          activities__isnull=False,
          is_live_location_on=True,
        )
        .exclude(id=requesting_user.id)
        .distinct()
      )

      # Exclude blocked users
      queryset = exclude_blocked_users(queryset, requesting_user)

      # Annotate with distance
      queryset = queryset.annotate(distance=Distance("location", requesting_user.location))

      # Apply distance filter
      queryset = queryset.filter(distance__lte=D(km=max_distance_km))

      # Apply gender filter if provided
      if genders and len(genders) > 0:
        queryset = queryset.filter(gender__in=genders)

      # Apply age filters if provided
      if min_age is not None or max_age is not None:
        today = date.today()

        if min_age is not None:
          # Calculate the maximum date of birth for the minimum age
          max_dob = today - timedelta(days=min_age * 365.25)
          queryset = queryset.filter(dob__lte=max_dob)

        if max_age is not None:
          # Calculate the minimum date of birth for the maximum age
          min_dob = today - timedelta(days=(max_age + 1) * 365.25)
          queryset = queryset.filter(dob__gte=min_dob)

      # Apply minimum photos filter if provided
      if min_photos is not None and min_photos > 0:
        queryset = queryset.annotate(photo_count=Count("profile_images")).filter(
          photo_count__gt=min_photos
        )

      # Always filter for users with verified Stripe Connect accounts
      queryset = queryset.filter(
        stripe_connect_account__is_verified=True, stripe_connect_account__isnull=False
      )

      # Order by distance
      nearby_users_queryset = queryset.order_by("distance")

      return ServiceResult(
        success=True,
        message=AppMsg.DISCOVERY_GET_USERS_SUCCESS,
        data=nearby_users_queryset,
      )

    except Exception as e:
      logger.log_error(log_prefix, e)
      return ServiceResult(
        success=False,
        message=AppMsg.DISCOVERY_GET_USERS_FAILURE,
        data=[],
      )

  @staticmethod
  def _get_target_gender(user) -> Optional[str]:
    # Check if user is authenticated (for schema generation)
    if not hasattr(user, "gender") or not user.gender:
      return None

    if user.gender == "Male":
      return "Female"
    elif user.gender == "Female":
      return "Male"
    elif user.gender in ["Lesbian", "Gay", "Transgender", "Queer"]:
      return user.gender

    return None
