from rest_framework.generics import ListAPIView, RetrieveAPIView
from drf_spectacular.utils import extend_schema, OpenApiParameter
from django.shortcuts import get_object_or_404
from django.contrib.gis.db.models.functions import Distance


from api.discovery.serializers import UserProfileDiscoverySerializer, UserProfileMapSerializer
from api.discovery.services import DiscoverService
from api.consts.permissions import IsAuthenticated
from core.utils.app_response import AppMapPagination, AppResponse, AppPagination
from rentie.settings import DISCOVERY_MAX_DISTANCE_KM
from access.models import User


@extend_schema(
  tags=["Discovery"],
  parameters=[
    OpenApiParameter(
      name="maximum_distance",
      description="Maximum distance in kilometers",
      required=False,
      type=int,
    ),
    OpenApiParameter(
      name="gender",
      description="Filter by gender(s), comma separated values",
      required=False,
      type=str,
    ),
    OpenApiParameter(name="min_age", description="Minimum age filter", required=False, type=int),
    OpenApiParameter(name="max_age", description="Maximum age filter", required=False, type=int),
    OpenApiParameter(
      name="min_photos", description="Minimum number of profile photos", required=False, type=int
    ),
  ],
)
class DiscoveryUsersMapListView(ListAPIView):
  serializer_class = UserProfileMapSerializer
  permission_classes = [IsAuthenticated]
  pagination_class = AppMapPagination

  def get_queryset(self):
    """
    Returns the queryset that should be used for list views
    This lets the pagination work properly
    """
    requesting_user = self.request.user
    max_distance_km = self.request.query_params.get("maximum_distance") or DISCOVERY_MAX_DISTANCE_KM

    # Get gender filter and split by comma if provided
    gender_param = self.request.query_params.get("gender", "")
    genders = [g.strip() for g in gender_param.split(",")] if gender_param else None

    # Get age filters
    min_age = self.request.query_params.get("min_age")
    max_age = self.request.query_params.get("max_age")

    # Get minimum photos filter
    min_photos = self.request.query_params.get("min_photos")

    result = DiscoverService.get_discoverable_users(
      requesting_user=requesting_user,
      max_distance_km=int(max_distance_km) if max_distance_km else DISCOVERY_MAX_DISTANCE_KM,
      genders=genders,
      min_age=int(min_age) if min_age else None,
      max_age=int(max_age) if max_age else None,
      min_photos=int(min_photos) if min_photos else None,
    )

    if not result.success:
      return []

    return result.data

  def list(self, request, *args, **kwargs):
    """
    Override list method to handle service errors before pagination
    """
    # Check if there's an error from the service before trying to paginate
    requesting_user = request.user
    max_distance_km = request.query_params.get("maximum_distance") or DISCOVERY_MAX_DISTANCE_KM

    # Get gender filter and split by comma if provided
    gender_param = request.query_params.get("gender", "")
    genders = [g.strip() for g in gender_param.split(",")] if gender_param else None

    # Get age filters
    min_age = request.query_params.get("min_age")
    max_age = request.query_params.get("max_age")

    # Get minimum photos filter
    min_photos = request.query_params.get("min_photos")

    result = DiscoverService.get_discoverable_users(
      requesting_user=requesting_user,
      max_distance_km=int(max_distance_km) if max_distance_km else DISCOVERY_MAX_DISTANCE_KM,
      genders=genders,
      min_age=int(min_age) if min_age else None,
      max_age=int(max_age) if max_age else None,
      min_photos=int(min_photos) if min_photos else None,
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    # If successful, let the parent list() method handle pagination
    # The AppPagination class will format the response properly
    return super().list(request, *args, **kwargs)


@extend_schema(
  tags=["Discovery"],
  parameters=[
    OpenApiParameter(
      name="maximum_distance",
      description="Maximum distance in kilometers",
      required=False,
      type=int,
    ),
    OpenApiParameter(
      name="gender",
      description="Filter by gender(s), comma separated values",
      required=False,
      type=str,
    ),
    OpenApiParameter(name="min_age", description="Minimum age filter", required=False, type=int),
    OpenApiParameter(name="max_age", description="Maximum age filter", required=False, type=int),
    OpenApiParameter(
      name="min_photos", description="Minimum number of profile photos", required=False, type=int
    ),
  ],
)
class DiscoveryUsersListView(ListAPIView):
  serializer_class = UserProfileDiscoverySerializer
  permission_classes = [IsAuthenticated]
  pagination_class = AppPagination

  def get_queryset(self):
    """
    Returns the queryset that should be used for list views
    This lets the pagination work properly
    """
    requesting_user = self.request.user
    max_distance_km = self.request.query_params.get("maximum_distance") or DISCOVERY_MAX_DISTANCE_KM

    # Get gender filter and split by comma if provided
    gender_param = self.request.query_params.get("gender", "")
    genders = [g.strip() for g in gender_param.split(",")] if gender_param else None

    # Get age filters
    min_age = self.request.query_params.get("min_age")
    max_age = self.request.query_params.get("max_age")

    # Get minimum photos filter
    min_photos = self.request.query_params.get("min_photos")

    result = DiscoverService.get_discoverable_users(
      requesting_user=requesting_user,
      max_distance_km=int(max_distance_km) if max_distance_km else DISCOVERY_MAX_DISTANCE_KM,
      genders=genders,
      min_age=int(min_age) if min_age else None,
      max_age=int(max_age) if max_age else None,
      min_photos=int(min_photos) if min_photos else None,
    )

    if not result.success:
      return []

    return result.data

  def list(self, request, *args, **kwargs):
    """
    Override list method to handle service errors before pagination
    """
    # Check if there's an error from the service before trying to paginate
    requesting_user = request.user
    max_distance_km = request.query_params.get("maximum_distance") or DISCOVERY_MAX_DISTANCE_KM

    # Get gender filter and split by comma if provided
    gender_param = request.query_params.get("gender", "")
    genders = [g.strip() for g in gender_param.split(",")] if gender_param else None

    # Get age filters
    min_age = request.query_params.get("min_age")
    max_age = request.query_params.get("max_age")

    # Get minimum photos filter
    min_photos = request.query_params.get("min_photos")

    result = DiscoverService.get_discoverable_users(
      requesting_user=requesting_user,
      max_distance_km=int(max_distance_km) if max_distance_km else DISCOVERY_MAX_DISTANCE_KM,
      genders=genders,
      min_age=int(min_age) if min_age else None,
      max_age=int(max_age) if max_age else None,
      min_photos=int(min_photos) if min_photos else None,
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    # If successful, let the parent list() method handle pagination
    # The AppPagination class will format the response properly
    return super().list(request, *args, **kwargs)


@extend_schema(
  tags=["Discovery"],
)
class UserProfileDiscoveryDetailView(RetrieveAPIView):
  serializer_class = UserProfileDiscoverySerializer
  permission_classes = [IsAuthenticated]

  def get_object(self):
    """Get a specific user profile by ID"""
    user_id = self.kwargs.get("pk")
    user = get_object_or_404(User, pk=user_id)

    # Add distance annotation like in the list view
    requesting_user = self.request.user
    if requesting_user.location and user.location:
      # Use the same approach as in the DiscoverService
      # Import the Distance function to annotate the user with distance

      # Query the user with distance annotation to ensure consistent calculation
      annotated_user = (
        User.objects.filter(pk=user_id)
        .annotate(distance=Distance("location", requesting_user.location))
        .first()
      )

      # Copy the distance from the annotated user to our user object
      if annotated_user:
        user.distance = annotated_user.distance

    return user

  def retrieve(self, request, *args, **kwargs):
    """Override retrieve method to handle custom response formatting"""
    instance = self.get_object()
    serializer = self.get_serializer(instance)
    return AppResponse.success(data=serializer.data)
