from typing import Optional
from rest_framework import serializers

from access.models import User
from api.activity.serializers import ActivityPublicSerializer
from api.interest.serializers import InterestSerializer
from api.utils import ApiUtils, SparkStatusEnum


class UserProfileMapSerializer(serializers.ModelSerializer):
  """
  Serializer for representing any user public profile info.
  """

  profile_image = serializers.SerializerMethodField()

  class Meta:
    model = User
    fields = [
      "id",
      "full_name",
      "gender",
      "location",
      "profile_image",
    ]
    read_only_fields = [
      "id",
    ]

  def get_profile_image(self, obj) -> Optional[str]:
    request = self.context.get("request")
    return ApiUtils.get_first_profile_image(obj, request)


class UserProfileDiscoverySerializer(serializers.ModelSerializer):
  """
  Serializer for representing any user public profile info.
  """

  profile_image = serializers.SerializerMethodField()
  spark_status = serializers.SerializerMethodField()
  activities = ActivityPublicSerializer(many=True, read_only=True)
  interests = InterestSerializer(many=True, read_only=True)
  distance = serializers.SerializerMethodField()

  class Meta:
    model = User
    fields = [
      "id",
      "full_name",
      "gender",
      "location",
      "address",
      "dob",
      "activities",
      "profile_image",
      "spark_status",
      "distance",
      "interests",
    ]
    read_only_fields = [
      "id",
    ]

  def get_spark_status(self, obj) -> str:
    """
    Get the spark status between the authenticated user and the profile being viewed.
    
    Returns:
      - 'none': No spark has been sent between users
      - 'sent': Spark sent by auth user but not responded yet
      - 'received': Spark received by auth user from the viewed profile
      - 'matched': Spark sent and matched (both users sparked each other)
      - 'rejected': Spark was rejected
    """
    request = self.context.get("request")
    if not request:
      return SparkStatusEnum.NONE.value
      
    return ApiUtils.get_spark_status(request.user, obj)

  def get_profile_image(self, obj) -> Optional[str]:
    request = self.context.get("request")
    return ApiUtils.get_first_profile_image(obj, request)

  def get_distance(self, obj) -> int:
    # The distance annotation is added by the DiscoverService
    # Return the distance in kilometers without decimals
    if hasattr(obj, "distance"):
      # Convert from meters to kilometers and round to integer
      return int(obj.distance.m / 1000)
    return 0
