from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Notification


@admin.register(Notification)
class NotificationAdmin(ModelAdmin):
  list_display = (
    "id",
    "recipient",
    "sender",
    "notification_type",
    "title",
    "is_read",
    "created_at",
  )
  list_filter = ("is_read", "notification_type", "created_at")
  search_fields = ("recipient__email", "sender__email", "title", "body")
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  list_editable = ("is_read",)

  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "recipient",
          "sender",
          "notification_type",
          "title",
          "body",
          "is_read",
        )
      },
    ),
    (
      "Additional Information",
      {
        "fields": (
          "data",
          "created_at",
          "updated_at",
        ),
        "classes": ("collapse",),
      },
    ),
  )
