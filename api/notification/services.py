from typing import Dict, Any, Optional, List
import firebase_admin
from firebase_admin import credentials, messaging
from django.conf import settings
from django.db import transaction

from access.models import User
from .models import Notification
from core.utils.app_logger import AppLogger


logger = AppLogger()


class NotificationService:
  """
  Service class for handling notifications.
  Providers methods for creating and sending notifications via FCM.
  """

  _instance = None
  _firebase_initialized = False

  @classmethod
  def get_instance(cls):
    """
    Singleton pattern to ensure only one instance of the notification service exists.
    Also handles lazy initialization of Firebase
    """

    if cls._instance is None:
      cls._instance = cls()

    if not cls._firebase_initialized:
      cls._initialize_firebase()

    return cls._instance

  @classmethod
  def _initialize_firebase(cls):
    """Initialize Firebase Admin SDK for FCM"""

    try:
      cred = credentials.Certificate(settings.FIREBASE_CREDENTIALS_PATH)
      firebase_admin.initialize_app(cred)
      cls._firebase_initialized = True
      logger.log_info(
        "NotificationService - _initialize_firebase",
        "Firebase initialized successfully",
      )
    except Exception as e:
      logger.log_error("NotificationService - _initialize_firebase", e)

  @staticmethod
  def create_notification(
    recipient: User,
    title: str,
    body: str,
    notification_type: str,
    sender: Optional[User] = None,
    data: Optional[Dict[str, Any]] = None,
  ) -> Notification:
    """
    Create a notification in the database.

    Args:
      recipient: User to receive the notification
      title: Notification title
      body: Notification message body
      notification_type: Type of notification (e.g., 'chat_message', 'spark_sent')
      sender: User who triggered the notification (optional)
      data: Additional JSON data to be stored with the notification (optional)

    Returns:
      Created notification object
    """
    notification = Notification.objects.create(
      notification_type=notification_type,
      recipient=recipient,
      sender=sender,
      title=title,
      body=body,
      data=data,
      is_read=False,
    )

    logger.log_info(
      "NotificationService - create_notification",
      f"Notification created: {notification.id} for user {recipient.id}",
    )
    return notification

  @staticmethod
  def send_fcm_notification(
    recipient: User,
    title: str,
    body: str,
    data: Optional[Dict[str, Any]] = None,
  ) -> bool:
    """
    Send a push notification via Firebase Cloud Messaging.

    Args:
      recipient: User to receive the notification
      title: Notification title
      body: Notification message body
      data: Additional data to be sent with the notification (optional)

    Returns:
      Boolean indicating success or failure
    """
    if not recipient.fcm_token or not recipient.is_notification_on:
      logger.log_info(
        "NotificationService - send_fcm_notification",
        f"Skipping FCM for user {recipient.id}: no token or notifications disabled",
      )
      return False

    try:
      # Prepare notification message
      message = messaging.Message(
        notification=messaging.Notification(
          title=title,
          body=body,
        ),
        data=data or {},
        token=recipient.fcm_token,
      )

      # Send message
      response = messaging.send(message)
      logger.log_info(
        "NotificationService - send_fcm_notification",
        f"FCM notification sent successfully: {response}",
      )

      return True
    except Exception as e:
      logger.log_error("NotificationService - send_fcm_notification", e)
      if "invalid-registration-token" in str(e).lower():
        recipient.fcm_token = None
        recipient.save(update_fields=["fcm_token"])
        logger.log_info(
          "NotificationService - send_fcm_notification",
          f"Cleared invalid FCM token for user {recipient.id}",
        )

      return False

  @classmethod
  def notify(
    cls,
    recipient: User,
    title: str,
    body: str,
    notification_type: str,
    sender: Optional[User] = None,
    data: Optional[Dict[str, Any]] = None,
  ) -> Notification:
    """
    Create and send a notification in one operation.

    Args:
      recipient: User to receive the notification
      title: Notification title
      body: Notification message body
      notification_type: Type of notification (e.g., 'chat_message', 'spark_sent')
      sender: User who triggered the notification (optional)
      data: Additional JSON data for the notification (optional)

    Returns:
      Created notification object
    """
    # TODO: For now we creating notifications in the database even at this step we don't know if the user actually will see the notification e.g he could reject notifications permission
    with transaction.atomic():
      notification = cls.create_notification(
        recipient=recipient,
        title=title,
        body=body,
        notification_type=notification_type,
        sender=sender,
        data=data,
      )

      fcm_data = {
        "notification_id": str(notification.id),
        "notification_type": notification_type,
      }

      if data:
        fcm_data.update(data)

      cls.send_fcm_notification(
        recipient=recipient,
        title=title,
        body=body,
        data=fcm_data,
      )

    return notification

  @classmethod
  def send_bulk_notifications(
    cls,
    recipients: List[User],
    title: str,
    body: str,
    notification_type: str,
    sender: Optional[User] = None,
    data: Optional[Dict[str, Any]] = None,
  ) -> List[Notification]:
    """
    Send notifications to multiple users at once.

    Args:
      recipients: List of Users to receive the notification
      title: Notification title
      body: Notification message body
      notification_type: Type of notification
      sender: User who triggered the notification (optional)
      data: Additional JSON data for the notification (optional)

    Returns:
      List of created notification objects
    """
    notifications = []

    for recipient in recipients:
      notification = cls.notify(
        recipient=recipient,
        title=title,
        body=body,
        notification_type=notification_type,
        sender=sender,
        data=data,
      )
      notifications.append(notification)

    return notifications

  @staticmethod
  def mark_as_read(notification_id: str) -> bool:
    """
    Mark a notification as read.

    Args:
      notification_id: ID of the notification to mark as read

    Returns:
      Boolean indicating success or failure
    """
    try:
      notification = Notification.objects.get(id=notification_id)
      notification.is_read = True
      notification.save(update_fields=["is_read"])
      return True
    except Notification.DoesNotExist:
      logger.log_error(
        "NotificationService - mark_as_read",
        f"Notification {notification_id} not found",
      )
      return False

  @staticmethod
  def mark_all_as_read(user: User) -> int:
    """
    Mark all notifications for a user as read.

    Args:
      user: User whose notifications should be marked as read

    Returns:
      Number of notifications marked as read
    """
    return Notification.objects.filter(recipient=user, is_read=False).update(is_read=True)

  @staticmethod
  def delete_notification(notification_id: str) -> bool:
    """
    Delete a notification.

    Args:
      notification_id: ID of the notification to delete

    Returns:
      Boolean indicating success or failure
    """
    try:
      notification = Notification.objects.get(id=notification_id)
      notification.delete()
      return True
    except Notification.DoesNotExist:
      logger.log_error(
        "NotificationService - delete_notification",
        f"Notification {notification_id} not found",
      )
      return False
