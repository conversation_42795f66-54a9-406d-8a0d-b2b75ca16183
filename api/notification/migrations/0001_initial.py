# Generated by Django 4.2.9 on 2025-05-06 13:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('notification_type', models.CharField(blank=True, help_text="e.g., 'chat_message', 'review', 'request_accepted'", max_length=50, null=True, verbose_name='Notification Type')),
                ('title', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='Title')),
                ('body', models.TextField(verbose_name='Body')),
                ('data', models.JSONField(blank=True, help_text='Optional JSON data related to the notification', null=True, verbose_name='Additional Data')),
                ('is_read', models.BooleanField(default=False, verbose_name='Is Read')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='Recipient')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL, verbose_name='Sender')),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'ordering': ['-created_at'],
            },
        ),
    ]
