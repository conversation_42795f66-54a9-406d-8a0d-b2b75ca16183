from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import AbstractModel

from access.models import User


class Notification(AbstractModel):
  recipient = models.ForeignKey(
    User,
    related_name="notifications",
    on_delete=models.CASCADE,
    verbose_name=_("Recipient"),
  )
  sender = models.ForeignKey(
    User,
    related_name="sent_notifications",
    on_delete=models.SET_NULL,
    null=True,
    blank=True,
    verbose_name=_("Sender"),
  )
  notification_type = models.CharField(
    _("Notification Type"),
    max_length=50,
    blank=True,
    null=True,
    help_text=_("e.g., 'chat_message', 'review', 'request_accepted'"),
  )
  title = models.CharField(_("Title"), max_length=255, blank=True, null=True)
  body = models.TextField(_("Body"))
  data = models.JSONField(
    _("Additional Data"),
    blank=True,
    null=True,
    help_text=_("Optional JSON data related to the notification"),
  )
  is_read = models.Bo<PERSON>an<PERSON>ield(_("Is Read"), default=False)

  class Meta:
    verbose_name = _("Notification")
    verbose_name_plural = _("Notifications")
    ordering = ["-created_at"]

  def __str__(self):
    return f"Notification for {self.recipient.email} - {self.title or self.body[:50]}"
