from django.forms import <PERSON><PERSON>an<PERSON>ield
from typing import Optional
from .models import Notification

from rest_framework import serializers
from api.utils import ApiUtils


class NotificationSerializer(serializers.ModelSerializer):
  profile_image = serializers.SerializerMethodField()

  class Meta:
    model = Notification
    fields = [
      "id",
      "sender",
      "notification_type",
      "title",
      "body",
      "data",
      "is_read",
      "created_at",
      "profile_image",
    ]
    read_only_fields = [
      "id",
      "sender",
      "created_at",
    ]

  def get_profile_image(self, obj) -> Optional[str]:
    request = self.context.get("request")
    if obj.sender:
      return ApiUtils.get_first_profile_image(obj.sender, request)
    return None


class NotificationReadSerializer(serializers.Serializer):
  is_read = BooleanField()

  def update(self, instance, validated_data):
    instance.is_read = validated_data.get("is_read", instance.is_read)
    instance.save()
    return instance
