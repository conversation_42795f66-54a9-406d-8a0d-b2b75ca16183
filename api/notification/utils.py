class NotificationType:
  """
  Define all notification types used in the application for consistency.
  """

  # Spark related notifications
  SPARK_SENT = "spark_sent"
  SPARK_MATCHED = "spark_matched"
  SPARK_RESPONDED = "spark_responded"

  # Chat related notifications
  CHAT_MESSAGE = "chat_message"

  # System notifications
  ACCOUNT_UPDATE = "account_update"
  SYSTEM_ANNOUNCEMENT = "system_announcement"

  # Invitation related notifications
  INVITATION_RECEIVED = "invitation_received"
  INVITATION_ACCEPTED = "invitation_accepted"
  INVITATION_REJECTED = "invitation_rejected"
  INVITATION_CANCELED = "invitation_canceled"

  # Session related notifications
  SESSION_CREATED = "session_created"
  SESSION_STARTED = "session_started"
  SESSION_PAUSED = "session_paused"
  SESSION_RESUMED = "session_resumed"
  SESSION_ENDED = "session_ended"
  SESSION_ENDED_EARLY = "session_ended_early"
  SESSION_EMERGENCY = "session_emergency"

  PAYMENT_SUCCEEDED = "payment_succeeded"
  PAYMENT_PROCESSING = "payment_processing"
  PAYMENT_FAILED = "payment_failed"
  PAYMENT_REFUNDED = "payment_refunded"

  REVIEW_RECEIVED = "review_received"

  SUBSCRIPTION_CREATED = "subscription_created"

  # List of all notification types
  ALL_TYPES = [
    SPARK_SENT,
    SPARK_MATCHED,
    SPARK_RESPONDED,
    CHAT_MESSAGE,
    ACCOUNT_UPDATE,
    SYSTEM_ANNOUNCEMENT,
    INVITATION_RECEIVED,
    INVITATION_ACCEPTED,
    INVITATION_REJECTED,
    INVITATION_CANCELED,
    SESSION_CREATED,
    SESSION_STARTED,
    SESSION_PAUSED,
    SESSION_RESUMED,
    SESSION_ENDED,
    SESSION_ENDED_EARLY,
    SESSION_EMERGENCY,
    PAYMENT_SUCCEEDED,
    PAYMENT_PROCESSING,
    PAYMENT_FAILED,
    PAYMENT_REFUNDED,
    REVIEW_RECEIVED,
  ]

  @classmethod
  def is_valid_type(cls, notification_type):
    """Check if a notification type is valid"""
    return notification_type in cls.ALL_TYPES
