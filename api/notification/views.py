from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from rest_framework.generics import GenericAPIView, ListAPIView
from rest_framework import permissions, status, serializers
from drf_spectacular.utils import extend_schema, inline_serializer

from api.consts.permissions import IsAuthenticated
from core.utils.app_response import AppResponse, AppPagination

from .models import Notification
from .serializers import (
  NotificationReadSerializer,
  NotificationSerializer,
)


@extend_schema(tags=["Notification"])
class NotificationListView(ListAPIView):
  """
  List all notifications for the currently authenticated user.
  """

  serializer_class = NotificationSerializer
  permission_classes = [IsAuthenticated]
  pagination_class = AppPagination

  def get_queryset(self):
    return Notification.objects.filter(recipient=self.request.user)

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())

    page = self.paginate_queryset(queryset)
    if page is not None:
      serializer = self.get_serializer(page, many=True)
      return self.get_paginated_response(serializer.data)

    serializer = self.get_serializer(queryset, many=True)
    return AppResponse.success(data=serializer.data)


@extend_schema(tags=["Notification"])
class NotificationReadUpdateView(GenericAPIView):
  """
  Update notification's is_read field to true or false based on user input.
  This endpoint allows users to mark their notifications as read or unread
  """

  serializer_class = NotificationReadSerializer
  permission_classes = [IsAuthenticated]

  def get_object(self):
    """
    Retrieve the notification object based on the URL parameter
    Ensures that the user can only update their own notifications.
    """

    notification_id = self.kwargs.get("pk")
    return get_object_or_404(
      Notification,
      id=notification_id,
      recipient=self.request.user,
    )

  def patch(self, request, *args, **kwargs):
    notification = self.get_object()
    serializer = self.get_serializer(notification, data=request.data, partial=True)

    serializer.is_valid(raise_exception=True)
    updated_notification = serializer.update(notification, serializer.validated_data)

    return AppResponse.success(
      data={
        "id": updated_notification.id,
        "is_read": updated_notification.is_read,
      },
      status=status.HTTP_200_OK,
    )


@extend_schema(tags=["Notification"])
class NotificationDeleteView(GenericAPIView):
  """
  Delete a specific notification for the authenticated user.
  """

  permission_classes = [permissions.IsAuthenticated]
  queryset = Notification.objects.all()
  lookup_field = "pk"
  serializer_class = NotificationSerializer

  def get_object(self):
    """
    Retrieve the notification object based on the URL parameter
    Ensures that the user can only update their own notifications.
    """

    notification_id = self.kwargs.get("pk")
    return get_object_or_404(
      Notification,
      id=notification_id,
      recipient=self.request.user,
    )

  def delete(self, request, *args, **kwargs):
    notification = self.get_object()
    notification.delete()
    return AppResponse.success(
      message="Notification deleted successfully", status=status.HTTP_200_OK
    )


@extend_schema(tags=["Notification"])
class FCMTokenUpdateView(APIView):
  """
  Update FCM token for the authenticated user.
  This endpoint allows users to register or update their FCM token for push notifications.
  """

  permission_classes = [IsAuthenticated]

  @extend_schema(
    request=inline_serializer(
      name="FCMTokenRequest",
      fields={
        "fcm_token": serializers.CharField(
          required=True, help_text="Firebase Cloud Messaging token"
        )
      },
    ),
    responses={
      status.HTTP_200_OK: inline_serializer(
        name="FCMTokenSuccessResponse",
        fields={"success": serializers.BooleanField(), "message": serializers.CharField()},
      ),
      status.HTTP_400_BAD_REQUEST: inline_serializer(
        name="FCMTokenErrorResponse", fields={"error": serializers.CharField()}
      ),
    },
  )
  def post(self, request):
    fcm_token = request.data.get("fcm_token")

    if not fcm_token:
      return AppResponse.error(message="FCM token is required", status=status.HTTP_400_BAD_REQUEST)

    user = request.user
    user.fcm_token = fcm_token
    user.save(update_fields=["fcm_token"])

    return AppResponse.success(message="FCM token updated successfully", status=status.HTTP_200_OK)
