# Generated by Django 4.2.9 on 2025-05-14 21:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('activity', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('currency', models.CharField(default='EUR', max_length=3, verbose_name='Currency')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('canceled', 'Canceled')], default='pending', max_length=20, verbose_name='Status')),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='Stripe Payment Intent ID')),
                ('stripe_transfer_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='Stripe Transfer ID')),
                ('application_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Application Fee')),
                ('payment_date', models.DateTimeField(blank=True, null=True, verbose_name='Payment Date')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='activity.activity', verbose_name='Activity')),
                ('payer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments_made', to=settings.AUTH_USER_MODEL, verbose_name='Payer')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments_received', to=settings.AUTH_USER_MODEL, verbose_name='Recipient')),
            ],
            options={
                'ordering': ['-created_at'],
                'abstract': False,
            },
        ),
    ]
