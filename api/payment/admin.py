from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Payment


@admin.register(Payment)
class PaymentAdmin(ModelAdmin):
  """Admin interface for Payment model"""

  list_display = (
    "id",
    "payer",
    "recipient",
    "activity",
    "amount",
    "currency",
    "status",
    "payment_date",
    "created_at",
  )
  list_filter = ("status", "currency", "payment_date", "created_at")
  search_fields = (
    "payer__email",
    "recipient__email",
    "payer__full_name",
    "recipient__full_name",
    "stripe_payment_intent_id",
    "stripe_transfer_id",
  )
  readonly_fields = ("created_at", "updated_at")
  date_hierarchy = "payment_date"
  fieldsets = (
    ("Participants", {"fields": ("payer", "recipient", "activity")}),
    (
      "Payment Details",
      {"fields": ("amount", "currency", "description", "payment_date")},
    ),
    ("Status Information", {"fields": ("status",)}),
    (
      "Stripe Information",
      {"fields": ("stripe_payment_intent_id", "stripe_transfer_id", "application_fee")},
    ),
    ("Metadata", {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
  )

  def get_queryset(self, request):
    """Optimize queryset with select_related to reduce database queries"""
    return super().get_queryset(request).select_related("payer", "recipient", "activity")

  autocomplete_fields = ["payer", "recipient", "activity"]
