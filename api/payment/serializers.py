from rest_framework import serializers

from api.profile.serializers import UserProfilePublicSerializer
from .models import Payment
from api.activity.serializers import ActivitySerializer
from api.invitation.serializers import InvitationSerializer


class PaymentSerializer(serializers.ModelSerializer):
  payer = UserProfilePublicSerializer(read_only=True)
  recipient = UserProfilePublicSerializer(read_only=True)
  activity = ActivitySerializer(read_only=True)
  invitation = InvitationSerializer(read_only=True)

  class Meta:
    model = Payment
    fields = [
      "id",
      "payer",
      "recipient",
      "activity",
      "invitation",
      "amount",
      "currency",
      "status",
      "payment_date",
      "description",
      "created_at",
      "updated_at",
    ]


class PaymentCreateSerializer(serializers.Serializer):
  invitation_id = serializers.CharField()
  description = serializers.CharField(required=False, allow_blank=True)


class PaymentConfirmSerializer(serializers.Serializer):
  payment_id = serializers.CharField()
  payment_intent_id = serializers.Char<PERSON><PERSON>()


class PaymentIntentResponseSerializer(serializers.Serializer):
  client_secret = serializers.CharField()
  payment_id = serializers.UUIDField()
  amount = serializers.DecimalField(max_digits=10, decimal_places=2)
  application_fee = serializers.DecimalField(max_digits=10, decimal_places=2)
