import os
import stripe
from django.db import models
from decimal import Decimal
from typing import Dict, Any, Optional

from django.utils import timezone

from access.models import User
from api.stripe_connect.models import StripeConnectAccount
from core.utils.app_msg import AppMsg
from api.consts.service_result import ServiceResult
from core.utils.app_logger import AppLogger
from api.payment.models import Payment
from api.subscription.models import Subscription
from api.invitation.models import Invitation
from api.notification.services import NotificationService
from api.notification.utils import NotificationType


logger = AppLogger()

# For backend site must be secret key
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

# Platform fee percentage (e.g., 3%)
PLATFORM_FEE_PERCENTAGE = Decimal(os.getenv("PLATFORM_FEE_PERCENTAGE", "3"))


class PaymentService:
  @staticmethod
  def create_payment_intent(
    payer: User,
    invitation_id: str,
    description: Optional[str] = None,
  ) -> ServiceResult:
    """
    Create a payment intent for an activity payment based on an invitation
    Returns client_secret for the mobile app to confirm payment

    Args:
        payer: The user making the payment
        invitation_id: The ID of the invitation being paid for
        description: Optional description for the payment
    """
    try:
      # Get the invitation and related activity and owner
      try:
        invitation = Invitation.objects.select_related("activity", "activity__owner").get(
          id=invitation_id
        )
      except Invitation.DoesNotExist:
        logger.log_error(
          "PaymentService - create_payment_intent",
          f"Invitation not found: {invitation_id}",
        )
        return ServiceResult(success=False, message=AppMsg.INVITATION_NOT_FOUND)

      activity = invitation.activity
      recipient = activity.owner

      # Step 1: Check if recipient has a valid Stripe Connect account
      try:
        connect_account = StripeConnectAccount.objects.get(user=recipient)

        # Verify account capabilities using Stripe API
        stripe_account = stripe.Account.retrieve(connect_account.account_id)
        if not stripe_account.get("charges_enabled"):
          logger.log_error(
            "PaymentService - create_payment_intent",
            f"Recipient {recipient.id} has connect account but charges are not enabled",
          )
          return ServiceResult(success=False, message=AppMsg.RECIPIENT_ACCOUNT_INVALID)

        # Additional validation for the connect account
        if not stripe_account.get("payouts_enabled"):
          logger.log_error(
            "PaymentService - create_payment_intent",
            f"Recipient {recipient.id} has connect account but payouts are not enabled",
          )
          return ServiceResult(success=False, message=AppMsg.RECIPIENT_PAYOUTS_DISABLED)

      except StripeConnectAccount.DoesNotExist:
        logger.log_error(
          "PaymentService - create_payment_intent",
          f"Recipient {recipient.id} has no valid Stripe Connect account",
        )
        return ServiceResult(success=False, message=AppMsg.RECIPIENT_NO_PAYMENT_ACCOUNT)

      # Step 2: Get or create customer for the payer
      customer_id = PaymentService._get_or_create_stripe_customer(payer)
      if not customer_id:
        logger.log_error(
          "PaymentService - create_payment_intent",
          f"Failed to get or create customer for payer {payer.id}",
        )
        return ServiceResult(success=False, message=AppMsg.PAYMENT_CUSTOMER_CREATION_FAILED)

      # We're not handling payment methods in this flow
      # The payment method will be selected on the client side

      # Step 4: Calculate platform fee
      amount_decimal = Decimal(str(activity.price))
      platform_fee = (amount_decimal * PLATFORM_FEE_PERCENTAGE) / Decimal("100")

      # Convert to cents for Stripe
      amount_cents = int(amount_decimal * 100)
      application_fee_cents = int(platform_fee * 100)

      # Step 5: Create a PaymentIntent with the connected account
      try:
        # Generate idempotency key to prevent duplicate charges
        # Convert datetime to timestamp for the idempotency key
        timestamp = int(timezone.now().timestamp())
        idempotency_key = f"payment_{payer.id}_{invitation.id}_{timestamp}"

        payment_intent_params = {
          "amount": amount_cents,
          "currency": "eur",  # Default to EUR
          "customer": customer_id,
          "payment_method_types": ["card"],
          "application_fee_amount": application_fee_cents,
          "transfer_data": {
            "destination": connect_account.account_id,
          },
          "metadata": {
            "payer_id": str(payer.id),
            "recipient_id": str(recipient.id),
            "activity_id": str(activity.id),
            "invitation_id": str(invitation.id),
            "description": description or f"Payment for {activity.name}",
          },
          "idempotency_key": idempotency_key,
        }

        # We're not specifying a payment method here
        # The client will handle payment method selection and confirmation

        payment_intent = stripe.PaymentIntent.create(**payment_intent_params)
        logger.log_info(
          "PaymentService - create_payment_intent",
          f"Created payment intent: {payment_intent.id}",
        )
      except stripe.error.CardError as e:
        logger.log_error(
          "PaymentService - create_payment_intent",
          f"Card error creating payment intent: {str(e)}",
        )
        return ServiceResult(success=False, message=AppMsg.PAYMENT_CARD_ERROR)
      except stripe.error.InvalidRequestError as e:
        logger.log_error(
          "PaymentService - create_payment_intent",
          f"Invalid request creating payment intent: {str(e)}",
        )
        return ServiceResult(success=False, message=AppMsg.PAYMENT_INTENT_FAILED)
      except Exception as e:
        logger.log_error(
          "PaymentService - create_payment_intent",
          f"Unexpected error creating payment intent: {str(e)}",
        )
        return ServiceResult(success=False, message=AppMsg.PAYMENT_INTENT_FAILED)

      # Step 6: Create a pending payment record
      try:
        payment = Payment.objects.create(
          payer=payer,
          recipient=recipient,
          activity=activity,
          amount=amount_decimal,
          currency="EUR",
          status="pending",
          stripe_payment_intent_id=payment_intent.id,
          application_fee=platform_fee,
          description=description or f"Payment for {activity.name}",
        )
        logger.log_info(
          "PaymentService - create_payment_intent",
          f"Created payment record: {payment.id}",
        )
      except Exception as e:
        logger.log_error(
          "PaymentService - create_payment_intent",
          e,
        )
        # If we fail to create the payment record but already created the payment intent,
        # we should cancel the payment intent to avoid orphaned payment intents
        try:
          stripe.PaymentIntent.cancel(payment_intent.id)
          logger.log_info(
            "PaymentService - create_payment_intent",
            f"Cancelled orphaned payment intent: {payment_intent.id}",
          )
        except Exception as cancel_error:
          logger.log_error(
            "PaymentService - create_payment_intent",
            cancel_error,
          )

        return ServiceResult(success=False, message=AppMsg.PAYMENT_RECORD_CREATION_FAILED)

      result_data = {
        "client_secret": payment_intent.client_secret,
        "payment_id": payment.id,
        "amount": float(amount_decimal),
        "application_fee": float(platform_fee),
      }

      return ServiceResult(
        success=True,
        data=result_data,
        message=AppMsg.PAYMENT_INTENT_CREATED,
      )

    except Invitation.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.INVITATION_NOT_FOUND)
    except Exception as e:
      logger.log_error("PaymentService - create_payment_intent", e)
      return ServiceResult(success=False, message=AppMsg.PAYMENT_INTENT_FAILED)

  @staticmethod
  def confirm_payment(payment_id: str, payment_intent_id: str) -> ServiceResult:
    """
    Confirm a payment after the client has processed the payment intent

    Args:
        payment_id: The ID of the payment record
        payment_intent_id: The Stripe payment intent ID

    Returns:
        ServiceResult with success status and payment data or error message
    """
    try:
      # Step 1: Retrieve the payment record
      try:
        payment = Payment.objects.select_related("payer", "recipient", "activity").get(
          id=payment_id, stripe_payment_intent_id=payment_intent_id
        )
        logger.log_info(
          "PaymentService - confirm_payment",
          f"Retrieved payment record: {payment.id}",
        )
      except Payment.DoesNotExist:
        logger.log_error(
          "PaymentService - confirm_payment",
          f"Payment not found: {payment_id} with intent {payment_intent_id}",
        )
        return ServiceResult(success=False, message=AppMsg.PAYMENT_NOT_FOUND)

      # Step 2: Retrieve the payment intent from Stripe
      try:
        payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
        logger.log_info(
          "PaymentService - confirm_payment",
          f"Retrieved payment intent: {payment_intent_id} with status {payment_intent.status}",
        )
      except stripe.error.InvalidRequestError as e:
        logger.log_error(
          "PaymentService - confirm_payment",
          f"Invalid payment intent: {payment_intent_id}",
          e,
        )
        return ServiceResult(success=False, message=AppMsg.PAYMENT_INTENT_FAILED)
      except Exception as e:
        logger.log_error("PaymentService - confirm_payment", e)
        return ServiceResult(success=False, message=AppMsg.PAYMENT_CONFIRMATION_FAILED)

      # Get notification service instance
      notification_service = NotificationService.get_instance()

      # Prepare common notification data - all values must be strings for FCM
      notification_data = {
        "payment_id": str(payment.id),
        "payment_intent_id": str(payment_intent_id),
        "amount": str(float(payment.amount)),
        "currency": str(payment.currency),
        "activity_name": str(payment.activity.name),
        "activity_id": str(payment.activity.id),
        "invitation_id": str(payment_intent.metadata.get("invitation_id"))
        if hasattr(payment_intent, "metadata") and payment_intent.metadata.get("invitation_id")
        else "",
      }

      if payment_intent.status == "succeeded":
        payment.status = "succeeded"
        payment.payment_date = timezone.now()
        payment.save()

        # Update the invitation with this payment if available
        invitation_id = payment_intent.metadata.get("invitation_id")
        if invitation_id:
          try:
            from api.invitation.models import Invitation

            invitation = Invitation.objects.filter(id=invitation_id).first()
            if invitation:
              invitation.payment = payment
              invitation.save()
              logger.log_info(
                "PaymentService - confirm_payment",
                f"Updated invitation {invitation_id} with payment {payment.id}",
              )
          except Exception as e:
            # Don't fail the payment confirmation if updating the invitation fails
            logger.log_error(
              "PaymentService - confirm_payment",
              f"Failed to update invitation {invitation_id} with payment {payment.id}: {str(e)}",
            )

        # Add payment date to notification data - must be a string for FCM
        notification_data["payment_date"] = payment.payment_date.isoformat()
        # For successful payments, we want to notify both parties
        # Notify payer about successful payment
        notification_service.notify(
          recipient=payment.payer,
          title="Payment Successful",
          body=f"Your payment of {payment.currency} {payment.amount} for {payment.activity.name} was successful",
          notification_type=NotificationType.PAYMENT_SUCCEEDED,
          data=notification_data,
        )

        # Notify recipient about received payment
        notification_service.notify(
          recipient=payment.recipient,
          title="Payment Received",
          body=f"You received a payment of {payment.currency} {payment.amount} from {payment.payer.full_name} for {payment.activity.name}",
          notification_type=NotificationType.PAYMENT_SUCCEEDED,
          sender=payment.payer,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          data=payment,
          message=AppMsg.PAYMENT_SUCCEEDED,
        )
      elif payment_intent.status == "processing":
        payment.status = "processing"
        payment.save()

        return ServiceResult(
          success=True,
          data=payment,
          message=AppMsg.PAYMENT_PROCESSING,
        )
      else:
        payment.status = "failed"
        payment.save()

        # Notify payer about failed payment
        notification_service.notify(
          recipient=payment.payer,
          title="Payment Failed",
          body=f"Your payment of {payment.currency} {payment.amount} for {payment.activity.name} has failed",
          notification_type=NotificationType.PAYMENT_FAILED,
          data=notification_data,
        )

        # Notify recipient about failed payment
        notification_service.notify(
          recipient=payment.recipient,
          title="Payment Failed",
          body=f"A payment of {payment.currency} {payment.amount} from {payment.payer.full_name} has failed",
          notification_type=NotificationType.PAYMENT_FAILED,
          sender=payment.payer,
          data=notification_data,
        )

        return ServiceResult(
          success=False,
          message=f"Payment failed with status: {payment_intent.status}",
        )

    except Payment.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.PAYMENT_NOT_FOUND)
    except Exception as e:
      logger.log_error("PaymentService - confirm_payment", e)
      return ServiceResult(success=False, message=AppMsg.PAYMENT_CONFIRMATION_FAILED)

  @staticmethod
  def update_payment_from_webhook(
    payment_intent_id: str, event_data: Dict[str, Any]
  ) -> ServiceResult:
    """
    Update payment details from webhook
    """
    try:
      payment = Payment.objects.select_related("payer", "recipient", "activity").get(
        stripe_payment_intent_id=payment_intent_id
      )
      status = event_data.get("status")

      # Get notification service instance
      notification_service = NotificationService.get_instance()

      # Prepare common notification data
      notification_data = {
        "payment_id": str(payment.id),
        "payment_intent_id": payment_intent_id,
        "amount": float(payment.amount),
        "currency": payment.currency,
        "activity_name": payment.activity.name,
        "activity_id": str(payment.activity.id),
        "invitation_id": str(payment.invitation.id) if payment.invitation else None,
        "webhook_event": event_data.get("type"),
      }

      if status:
        # Map Stripe status to our status
        status_mapping = {
          "succeeded": "succeeded",
          "processing": "processing",
          "requires_payment_method": "failed",
          "requires_action": "pending",
          "requires_confirmation": "pending",
          "canceled": "canceled",
        }
        old_status = payment.status
        payment.status = status_mapping.get(status, payment.status)

        # If payment succeeded, record the payment date
        if payment.status == "succeeded" and not payment.payment_date:
          payment.payment_date = timezone.now()
          notification_data["payment_date"] = payment.payment_date.isoformat()

          # Only send notifications if status changed
          if old_status != "succeeded":
            # Notify payer about successful payment
            notification_service.notify(
              recipient=payment.payer,
              title="Payment Successful",
              body=f"Your payment of {payment.currency} {payment.amount} for {payment.activity.name} was successful",
              notification_type=NotificationType.PAYMENT_SUCCEEDED,
              data=notification_data,
            )

            # Notify recipient about received payment
            notification_service.notify(
              recipient=payment.recipient,
              title="Payment Received",
              body=f"You received a payment of {payment.currency} {payment.amount} from {payment.payer.full_name} for {payment.activity.name}",
              notification_type=NotificationType.PAYMENT_SUCCEEDED,
              sender=payment.payer,
              data=notification_data,
            )

        # If payment failed and status changed, send notifications
        elif payment.status == "failed" and old_status != "failed":
          # Notify payer about failed payment
          notification_service.notify(
            recipient=payment.payer,
            title="Payment Failed",
            body=f"Your payment of {payment.currency} {payment.amount} for {payment.activity.name} has failed",
            notification_type=NotificationType.PAYMENT_FAILED,
            data=notification_data,
          )

          # Notify recipient about failed payment
          notification_service.notify(
            recipient=payment.recipient,
            title="Payment Failed",
            body=f"A payment of {payment.currency} {payment.amount} from {payment.payer.full_name} has failed",
            notification_type=NotificationType.PAYMENT_FAILED,
            sender=payment.payer,
            data=notification_data,
          )

        payment.save()

      return ServiceResult(
        success=True,
        data=payment,
        message=AppMsg.PAYMENT_WEBHOOK_UPDATE_SUCCESS,
      )

    except Payment.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.PAYMENT_NOT_FOUND)
    except Exception as e:
      logger.log_error("PaymentService - update_payment_from_webhook", e)
      return ServiceResult(success=False, message=AppMsg.PAYMENT_WEBHOOK_UPDATE_FAILED)

  @staticmethod
  def get_user_payments(user: User, as_recipient: bool = False) -> ServiceResult:
    """
    Get all payments for a user, either as payer or recipient
    """
    try:
      if as_recipient:
        payments = Payment.objects.filter(recipient=user).select_related(
          "payer", "activity", "invitation"
        )
      else:
        payments = Payment.objects.filter(payer=user).select_related(
          "recipient", "activity", "invitation"
        )

      return ServiceResult(
        success=True,
        data=payments,
        message=AppMsg.PAYMENTS_RETRIEVED,
      )

    except Exception as e:
      logger.log_error("PaymentService - get_user_payments", e)
      return ServiceResult(success=False, message=AppMsg.PAYMENTS_RETRIEVAL_FAILED)

  @staticmethod
  def get_payment_details(payment_id: str, user: User) -> ServiceResult:
    """
    Get details for a specific payment
    """
    try:
      payment = Payment.objects.get(
        models.Q(payer=user) | models.Q(recipient=user),
        id=payment_id,
      )

      return ServiceResult(
        success=True,
        data=payment,
        message=AppMsg.PAYMENT_RETRIEVED,
      )

    except Payment.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.PAYMENT_NOT_FOUND)
    except Exception as e:
      logger.log_error("PaymentService - get_payment_details", e)
      return ServiceResult(success=False, message=AppMsg.PAYMENT_RETRIEVAL_FAILED)

  # TODO: Do we need refund system?
  @staticmethod
  def refund_payment(payment_id: str, user: User) -> ServiceResult:
    """
    Refund a payment (only available to the recipient)
    """
    try:
      payment = Payment.objects.select_related("payer", "activity").get(
        id=payment_id, recipient=user
      )

      if payment.status != "succeeded":
        return ServiceResult(success=False, message=AppMsg.PAYMENT_CANNOT_REFUND)

      # Create refund in Stripe
      refund = stripe.Refund.create(
        payment_intent=payment.stripe_payment_intent_id,
        metadata={
          "payment_id": payment.id,
          "refunded_by": user.id,
        },
      )

      # Update payment status
      payment.status = "refunded"
      payment.save()

      # Get notification service instance
      notification_service = NotificationService.get_instance()

      # Prepare notification data
      notification_data = {
        "payment_id": str(payment.id),
        "payment_intent_id": payment.stripe_payment_intent_id,
        "refund_id": refund.id,
        "amount": float(payment.amount),
        "currency": payment.currency,
        "activity_name": payment.activity.name,
        "activity_id": str(payment.activity.id),
        "refund_date": timezone.now().isoformat(),
      }

      # Notify payer about refund
      notification_service.notify(
        recipient=payment.payer,
        title="Payment Refunded",
        body=f"Your payment of {payment.currency} {payment.amount} for {payment.activity.name} has been refunded",
        notification_type=NotificationType.PAYMENT_REFUNDED,
        sender=user,
        data=notification_data,
      )

      # Notify recipient (refunder) about refund
      notification_service.notify(
        recipient=user,
        title="Payment Refunded",
        body=f"You have refunded {payment.currency} {payment.amount} to {payment.payer.full_name} for {payment.activity.name}",
        notification_type=NotificationType.PAYMENT_REFUNDED,
        data=notification_data,
      )

      return ServiceResult(
        success=True,
        data=payment,
        message=AppMsg.PAYMENT_REFUNDED,
      )

    except Payment.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.PAYMENT_NOT_FOUND)
    except Exception as e:
      logger.log_error("PaymentService - refund_payment", e)
      return ServiceResult(success=False, message=AppMsg.PAYMENT_REFUND_FAILED)

  @staticmethod
  def _get_or_create_stripe_customer(user: User) -> str:
    """
    Get or create a Stripe customer for the given user
    Returns the stripe customer ID
    """

    # First check if user already has a customer ID from subscriptions
    subscription = Subscription.objects.filter(user=user).first()
    if subscription and subscription.stripe_customer_id:
      return subscription.stripe_customer_id

    # Check if user has made payments before
    payment = Payment.objects.filter(payer=user).first()
    if payment and payment.stripe_payment_intent_id:
      # Retrieve the payment intent to get customer ID
      payment_intent = stripe.PaymentIntent.retrieve(payment.stripe_payment_intent_id)
      if payment_intent.customer:
        return payment_intent.customer

    # Create a new customer
    customer = stripe.Customer.create(
      email=user.email,
      name=user.full_name,
      metadata={"user_id": user.id},
    )

    return customer.id
