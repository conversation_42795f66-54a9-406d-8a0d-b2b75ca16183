from django.db import models
from django.utils.translation import gettext_lazy as _
from access.models import User
from api.activity.models import Activity
from core.models import AbstractModel


class Payment(AbstractModel):
  """Model to store payment transactions between users"""

  STATUS_CHOICES = [
    ("pending", _("Pending")),
    ("processing", _("Processing")),
    ("succeeded", _("Succeeded")),
    ("failed", _("Failed")),
    ("refunded", _("Refunded")),
    ("canceled", _("Canceled")),
  ]

  payer = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="payments_made",
    verbose_name=_("Payer"),
  )
  recipient = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="payments_received",
    verbose_name=_("Recipient"),
  )
  activity = models.ForeignKey(
    Activity,
    on_delete=models.CASCADE,
    related_name="payments",
    verbose_name=_("Activity"),
  )
  amount = models.DecimalField(
    _("Amount"),
    max_digits=10,
    decimal_places=2,
  )
  currency = models.CharField(
    _("Currency"),
    max_length=3,
    default="EUR",
  )
  status = models.CharField(
    _("Status"),
    max_length=20,
    choices=STATUS_CHOICES,
    default="pending",
  )
  stripe_payment_intent_id = models.CharField(
    _("Stripe Payment Intent ID"),
    max_length=100,
    null=True,
    blank=True,
  )
  stripe_transfer_id = models.CharField(
    _("Stripe Transfer ID"),
    max_length=100,
    null=True,
    blank=True,
  )
  application_fee = models.DecimalField(
    _("Application Fee"),
    max_digits=10,
    decimal_places=2,
    default=0,
  )
  payment_date = models.DateTimeField(
    _("Payment Date"),
    null=True,
    blank=True,
  )
  description = models.TextField(
    _("Description"),
    blank=True,
    null=True,
  )

  def __str__(self):
    return (
      f"Payment {self.id}: {self.payer.email} to {self.recipient.email} for {self.activity.name}"
    )
