from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiTypes
from core.utils.app_response import AppResponse
from .serializers import (
  PaymentIntentResponseSerializer,
  PaymentSerializer,
  PaymentCreateSerializer,
  PaymentConfirmSerializer,
)
from .services import PaymentService


@extend_schema(tags=["Payments"])
class PaymentViewSet(viewsets.ViewSet):
  """
  A viewset for managing payments between users
  """

  permission_classes = [permissions.IsAuthenticated]

  @extend_schema(
    summary="List payments for current user",
    responses=PaymentSerializer(many=True),
  )
  def list(self, request):
    """List all payments for the current user (as payer)"""
    result = PaymentService.get_user_payments(request.user)

    if not result.success:
      return AppResponse.error(message=result.message)

    serializer = PaymentSerializer(result.data, many=True)

    response = {
      'items': serializer.data
    }

    return AppResponse.success(data=response)

  @extend_schema(
    summary="Retrieve a specific payment by ID",
    responses=PaymentSerializer,
    parameters=[
      OpenApiParameter(
        name="id", type=OpenApiTypes.UUID, location=OpenApiParameter.PATH, description="Payment ID"
      )
    ],
  )
  def retrieve(self, request, pk=None):
    """Get a specific payment"""
    result = PaymentService.get_payment_details(pk, request.user)

    if not result.success:
      return AppResponse.error(message=result.message)

    serializer = PaymentSerializer(result.data)
    return AppResponse.success(data=serializer.data)

  @extend_schema(
    summary="Create a payment intent for an invitation",
    request=PaymentCreateSerializer,
    responses={200: PaymentIntentResponseSerializer},
  )
  @action(detail=False, methods=["post"])
  def create_payment_intent(self, request):
    """
    Create a payment intent for an invitation
    Returns client_secret needed for Flutter Stripe SDK
    """
    serializer = PaymentCreateSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    result = PaymentService.create_payment_intent(
      request.user,
      serializer.validated_data["invitation_id"],
      serializer.validated_data.get("description"),
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    return AppResponse.success(message=result.message, data=result.data)

  @extend_schema(
    summary="Confirm a payment after client-side processing",
    request=PaymentConfirmSerializer,
    responses=PaymentSerializer,
  )
  @action(detail=False, methods=["post"])
  def confirm_payment(self, request):
    """
    Confirm a payment after the client has processed the payment intent
    """
    serializer = PaymentConfirmSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    result = PaymentService.confirm_payment(
      serializer.validated_data["payment_id"],
      serializer.validated_data["payment_intent_id"],
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    payment_serializer = PaymentSerializer(result.data)
    return AppResponse.success(message=result.message, data=payment_serializer.data)

  @extend_schema(
    summary="List payments received by current user",
    responses=PaymentSerializer(many=True),
  )
  @action(detail=False, methods=["get"])
  def received(self, request):
    """List all payments received by the current user (as recipient)"""
    result = PaymentService.get_user_payments(request.user, as_recipient=True)

    if not result.success:
      return AppResponse.error(message=result.message)

    serializer = PaymentSerializer(result.data, many=True)
    return AppResponse.success(data=serializer.data)
