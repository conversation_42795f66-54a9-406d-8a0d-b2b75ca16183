from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import BlockedUser


@admin.register(BlockedUser)
class BlockedUserAdmin(ModelAdmin):
  list_display = ("id", "user", "blocked_user", "created_at", "updated_at")
  search_fields = ("user__full_name", "blocked_user__full_name")
  list_filter = ("created_at",)
  readonly_fields = ("id", "created_at", "updated_at")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "user",
          "blocked_user",
          "created_at",
          "updated_at",
        )
      },
    ),
  )
