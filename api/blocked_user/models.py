from django.utils.translation import gettext_lazy as _
from access.models import User
from core.models import AbstractModel
from django.db import models


class BlockedUser(AbstractModel):
  user = models.ForeignKey(
    User,
    verbose_name=_("User"),
    related_name="blocked_users",
    on_delete=models.CASCADE,
    help_text=_("User who blocks another user"),
  )

  blocked_user = models.ForeignKey(
    User,
    verbose_name=_("Blocked User"),
    related_name="blocked_by",
    on_delete=models.CASCADE,
    help_text=_("User who is blocked"),
  )

  class Meta:
    verbose_name = _("Blocked User")
    verbose_name_plural = _("Blocked Users")
    unique_together = ("user", "blocked_user")
    ordering = ["-created_at"]

  def __str__(self):
    return f"{self.user} blocked {self.blocked_user}"
