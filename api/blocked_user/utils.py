from django.db.models import Exists, OuterRef

from api.blocked_user.models import BlockedUser


def exclude_blocked_users(queryset, user):
  """
  Filter out users that have been blocked by the current user
  or users that have blocked the current user

  Can be applied to user querysets or querysets with user relations

  Args:
      queryset: The queryset to filter
      user: The current user

  Returns:
      Filtered queryset excluding blocked relationships
  """
  if not user or not user.is_authenticated:
    return queryset

  # For User querysets (where the base model is User)
  if queryset.model.__name__ == "User":
    blocked_by_user = BlockedUser.objects.filter(user=user, blocked_user=OuterRef("pk"))

    blocked_user = BlockedUser.objects.filter(user=OuterRef("pk"), blocked_user=user)
  # For querysets with a ForeignKey to User (adjust the field name as needed)
  else:
    # Assuming 'user' is the field name pointing to User model
    # Change this if your relation field has a different name
    user_field = "user"

    blocked_by_user = BlockedUser.objects.filter(user=user, blocked_user=OuterRef(f"{user_field}"))

    blocked_user = BlockedUser.objects.filter(user=OuterRef(f"{user_field}"), blocked_user=user)

  # Filter the queryset to exclude both cases
  return queryset.exclude(Exists(blocked_by_user) | Exists(blocked_user))
