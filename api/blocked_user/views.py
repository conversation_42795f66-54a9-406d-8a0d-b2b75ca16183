from rest_framework.generics import GenericAPI<PERSON>iew, RetrieveAPIView, ListAPIView
from drf_spectacular.utils import extend_schema, OpenApiResponse
from rest_framework.permissions import IsAuthenticated
from rest_framework import mixins, status
from rest_framework.views import APIView

from api.blocked_user.models import BlockedUser
from api.blocked_user.querysets import get_blocked_users_for_user
from api.blocked_user.serializers import (
  BlockUserSerializer,
  BlockedUserSerializer,
)
from core.utils.app_response import AppPagination, AppResponse
from core.utils.app_msg import AppMsg


@extend_schema(tags=["Blocked Users"])
class BlockedUserMeListView(ListAPIView):
  """List all users blocked by the authenticated user"""

  serializer_class = BlockedUserSerializer
  permission_classes = [IsAuthenticated]
  pagination_class = AppPagination

  def get_queryset(self):
    return get_blocked_users_for_user(self.request.user)

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())
    serializer = self.get_serializer(queryset, many=True)
    return AppResponse.success(data=serializer.data, message=AppMsg.BLOCKED_USERS_RETRIEVED)


@extend_schema(tags=["Blocked Users"])
class BlockedUserMeRetrieveView(RetrieveAPIView):
  """Retrieve a single blocked user entry by ID - only if created by the authenticated user"""

  serializer_class = BlockedUserSerializer
  permission_classes = [IsAuthenticated]

  def get_queryset(self):
    return get_blocked_users_for_user(self.request.user)

  def retrieve(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance)
    return AppResponse.success(data=serializer.data, message=AppMsg.BLOCKED_USER_DETAILS_RETRIEVED)


@extend_schema(
  tags=["Blocked Users"],
  summary="Block a user",
  description="Block a user. The authenticated user will be set as the blocker.",
  request=BlockUserSerializer,
  responses={201: BlockedUserSerializer},
)
class BlockUserView(APIView):
  serializer_class = BlockUserSerializer
  permission_classes = [IsAuthenticated]

  def post(self, request, *args, **kwargs):
    serializer = BlockUserSerializer(data=request.data, context={"request": request})
    serializer.is_valid(raise_exception=True)

    # Create the BlockedUser instance
    blocked_user = BlockedUser.objects.create(
      user=request.user, blocked_user=serializer.validated_data["blocked_user_id"]
    )

    # Serialize the newly created BlockedUser with full model serializer
    response_serializer = BlockedUserSerializer(blocked_user, context={"request": request})

    return AppResponse.success(
      data=response_serializer.data,
      message=AppMsg.USER_BLOCKED_SUCCESSFULLY,
      status=status.HTTP_201_CREATED,
    )


@extend_schema(
  tags=["Blocked Users"],
  summary="Unblock a user",
  responses={204: OpenApiResponse(description="User unblocked successfully")},
)
class UnblockUserView(mixins.DestroyModelMixin, GenericAPIView):
  """Unblock a user - only if blocked by the authenticated user"""

  serializer_class = BlockedUserSerializer
  permission_classes = [IsAuthenticated]

  def get_queryset(self):
    return get_blocked_users_for_user(self.request.user)

  def delete(self, request, *args, **kwargs):
    instance = self.get_object()
    self.perform_destroy(instance)
    return AppResponse.success(
      message=AppMsg.USER_UNBLOCKED_SUCCESSFULLY, status=status.HTTP_204_NO_CONTENT
    )
