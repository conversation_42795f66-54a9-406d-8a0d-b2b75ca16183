from rest_framework import serializers
from django.db import IntegrityError

from access.models import User
from api.blocked_user.models import BlockedUser
from api.profile.serializers import UserProfileImageSerializer
from core.utils.app_msg import AppMsg


class BlockedUserSerializer(serializers.ModelSerializer):
  blocked_user_full_name = serializers.EmailField(source="blocked_user.full_name", read_only=True)
  blocked_user_images = UserProfileImageSerializer(
    source="blocked_user.profile_images",
    many=True,
    read_only=True,
  )

  class Meta:
    model = BlockedUser
    fields = [
      "id",
      "user",
      "blocked_user",
      "blocked_user_full_name",
      "blocked_user_images",
      "created_at",
      "updated_at",
    ]
    read_only_fields = ["id", "user", "blocked_user_full_name", "created_at", "updated_at"]

  def validate_blocked_user(self, value):
    request = self.context.get("request")
    if request and value == request.user:
      raise serializers.ValidationError(AppMsg.CANNOT_BLOCK_YOURSELF)
    return value

  def create(self, validated_data):
    try:
      return super().create(validated_data)
    except IntegrityError:
      raise serializers.ValidationError(AppMsg.USER_ALREADY_BLOCKED)


class BlockUserSerializer(serializers.Serializer):
  blocked_user_id = serializers.UUIDField(required=True)

  def validate_blocked_user_id(self, value):
    try:
      user = User.objects.get(id=value)
      return user
    except User.DoesNotExist:
      raise serializers.ValidationError(AppMsg.USER_NOT_EXISTS)

  def validate(self, attrs):
    request = self.context.get("request")
    if attrs["blocked_user_id"] == request.user:
      raise serializers.ValidationError({"blocked_user_id": AppMsg.CANNOT_BLOCK_YOURSELF})
    return attrs
