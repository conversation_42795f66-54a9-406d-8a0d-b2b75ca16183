from datetime import datetime
from django.db import transaction
from django.utils import timezone
from typing import Dict

from api.activity.models import Activity
from api.consts.service_result import ServiceResult
from core.utils.app_logger import AppLogger
from core.utils.app_msg import AppMsg
from core.utils.notification_helpers import NotificationHelpers
from api.notification.services import NotificationService
from api.notification.utils import NotificationType
from api.session.models import Session

from .models import Invitation, InvitationStatus
from access.models import User

logger = AppLogger()


class InvitationService:
  """
  Service class for Invitation-related business logic.
  Separates domain logic from view/presentation.
  """

  @staticmethod
  def send_invitation(
    sender: User,
    recipient: User,
    invitation_date: str,
    invitation_time: str,
    activity_id: str,
    location: Dict,
    meeting_address: str,
  ) -> ServiceResult:
    """
    Send an invitation from one user to another.

    Args:
        sender: The user sending the invitation
        recipient: The user receiving the invitation
        invitation_date: Date of the planned meeting
        invitation_time: Time of the planned meeting
        activity_id: ID of the activity for the invitation
        location: Geographic point for the meeting location
        meeting_address: String representation of the meeting address

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      notification_service = NotificationService.get_instance()
      activity = Activity.objects.get(id=activity_id)
      activity_name = activity.name

      sender_name = sender.full_name

      with transaction.atomic():
        # Create the invitation
        invitation = Invitation.objects.create(
          sender=sender,
          recipient=recipient,
          invitation_date=invitation_date,
          invitation_time=invitation_time,
          activity=activity,
          location=location,
          status=InvitationStatus.PENDING,
          meeting_address=meeting_address,
        )

        # Prepare notification data
        notification_data = {
          "invitation_id": str(invitation.id),
          "sender_id": str(sender.id),
          "sender_name": sender_name,
          "date": invitation_date.isoformat()
          if hasattr(invitation_date, "isoformat")
          else invitation_date,
          "time": invitation_time.isoformat()
          if hasattr(invitation_time, "isoformat")
          else invitation_time,
          "activity_name": activity_name,
        }

        # Send notification to recipient
        notification_service.notify(
          recipient=recipient,
          title="New Invitation",
          body=f"{sender_name} invited you for {activity_name} on {invitation_date}",
          notification_type=NotificationType.INVITATION_RECEIVED,
          sender=sender,
          data=notification_data,
        )

        return ServiceResult(
          success=True, message=AppMsg.INVITATION_SENT, data={"invitation_id": str(invitation.id)}
        )

    except Exception as e:
      logger.log_error("InvitationService - send_invitation", e)
      return ServiceResult(success=False, message=AppMsg.INVITATION_SEND_FAILURE)

  @staticmethod
  def respond_to_invitation(
    responder: User, invitation: Invitation, response_status: str
  ) -> ServiceResult:
    """
    Respond to an invitation (accept or reject).

    Args:
        responder: The user responding to the invitation
        invitation: The invitation object
        response_status: The response (accepted/rejected)

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if responder.id != invitation.recipient.id:
        return ServiceResult(success=False, message=AppMsg.INVITATION_UNAUTHORIZED_RESPONSE)

      if invitation.status != InvitationStatus.PENDING:
        return ServiceResult(success=False, message=AppMsg.INVITATION_ALREADY_RESPONDED)

      notification_service = NotificationService.get_instance()

      sender = invitation.sender
      recipient = invitation.recipient

      recipient_name = recipient.full_name

      with transaction.atomic():
        # Update invitation status
        invitation.status = response_status
        invitation.responded_at = timezone.now()
        invitation.save(update_fields=["status", "responded_at", "updated_at"])

        # Prepare notification data
        notification_data = {
          "invitation_id": str(invitation.id),
          "recipient_id": str(recipient.id),
          "recipient_name": recipient_name,
          "response_status": response_status,
        }

        # Status-specific response message
        if response_status == InvitationStatus.ACCEPTED:
          notification_title = "Invitation Accepted"
          notification_body = f"{recipient_name} accepted your invitation!"
          notification_type = NotificationType.INVITATION_ACCEPTED

          # Create a session when invitation is accepted
          Session.objects.create(
            invitation=invitation,
            host=recipient,  # The invitation recipient becomes the host
            guest=sender,  # The invitation sender becomes the guest
          )

        else:  # REJECTED
          notification_title = "Invitation Declined"
          notification_body = f"{recipient_name} declined your invitation."
          notification_type = NotificationType.INVITATION_REJECTED

        # Send notification to the original sender
        notification_service.notify(
          recipient=sender,
          title=notification_title,
          body=notification_body,
          notification_type=notification_type,
          sender=recipient,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.INVITATION_RESPONDED,
          data={"invitation_id": str(invitation.id)},
        )

    except Exception as e:
      logger.log_error("InvitationService - respond_to_invitation", e)
      return ServiceResult(success=False, message=AppMsg.INVITATION_RESPONSE_FAILURE)

  @staticmethod
  def cancel_invitation(sender: User, invitation: Invitation) -> ServiceResult:
    """
    Cancel a pending/accepted invitation.

    Args:
        sender: The user canceling the invitation
        invitation: The invitation object

    Returns:
        ServiceResult with success status, message, and data
    """
    try:
      if sender.id != invitation.sender.id and sender.id != invitation.recipient.id:
        return ServiceResult(success=False, message=AppMsg.INVITATION_UNAUTHORIZED_CANCEL)
      
      # Check if the invitation has already started
      current_datetime = timezone.now()
      invitation_datetime = timezone.make_aware(
        datetime.combine(invitation.invitation_date, invitation.invitation_time)
      )
      
      if current_datetime >= invitation_datetime:
        return ServiceResult(success=False, message=AppMsg.INVITATION_ALREADY_STARTED)

      notification_service = NotificationService.get_instance()

      # Determine who should receive the notification
      notification_recipient, notification_sender_name = NotificationHelpers.determine_invitation_participants(
        action_initiator=sender,
        invitation_sender=invitation.sender,
        invitation_recipient=invitation.recipient
      )

      with transaction.atomic():
        # Delete associated session if it exists
        try:
          if hasattr(invitation, "session"):
            logger.log_info(
              "InvitationService - cancel_invitation",
              f"Deleting session for canceled invitation {invitation.id}",
            )
            invitation.session.delete()
            logger.log_info(
              "InvitationService - cancel_invitation",
              f"Session for invitation {invitation.id} deleted successfully",
            )
        except Exception as e:
          logger.log_error(
            "InvitationService - cancel_invitation",
            f"Error deleting session for invitation {invitation.id}: {str(e)}",
          )

        # Update invitation status
        invitation.status = InvitationStatus.CANCELED
        invitation.save(update_fields=["status", "updated_at"])

        # Prepare notification data
        notification_data = {
          "invitation_id": str(invitation.id),
          "sender_id": str(sender.id),
          "sender_name": notification_sender_name,
        }

        # Send notification to the appropriate recipient
        notification_service.notify(
          recipient=notification_recipient,
          title="Invitation Canceled",
          body=f"{notification_sender_name} canceled the invitation.",
          notification_type=NotificationType.INVITATION_CANCELED,
          sender=sender,
          data=notification_data,
        )

        return ServiceResult(
          success=True,
          message=AppMsg.INVITATION_CANCELED,
          data={"invitation_id": str(invitation.id)},
        )

    except Exception as e:
      logger.log_error("InvitationService - cancel_invitation", e)
      return ServiceResult(success=False, message=AppMsg.INVITATION_CANCEL_FAILURE)

  # TODO: Add expiration mechanism
  @staticmethod
  def mark_expired_invitations():
    """
    Utility method to mark invitations as expired if their date has passed.
    This could be run as a scheduled task.

    Returns:
        Number of invitations marked as expired
    """
    try:
      today = timezone.now().date()

      expired_count = Invitation.objects.filter(
        status=InvitationStatus.PENDING, invitation_date__lt=today
      ).update(status=InvitationStatus.EXPIRED)

      if expired_count > 0:
        logger.log_info(
          "InvitationService - mark_expired_invitations",
          f"Marked {expired_count} invitations as expired",
        )

      return expired_count

    except Exception as e:
      logger.log_error("InvitationService - mark_expired_invitations", e)
      return 0
