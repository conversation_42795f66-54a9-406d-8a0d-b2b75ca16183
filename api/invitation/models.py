from django.db import models
from api.activity.models import Activity
from core.models import AbstractModel
from django.utils.translation import gettext_lazy as _
from django.contrib.gis.db import models as gis_models

from access.models import User
from api.payment.models import Payment


class InvitationStatus(models.TextChoices):
  """Enum for invitation statuses"""

  PENDING = "pending", _("Pending")
  ACCEPTED = "accepted", _("Accepted")
  REJECTED = "rejected", _("Rejected")
  EXPIRED = "expired", _("Expired")
  CANCELED = "canceled", _("Canceled")


class Invitation(AbstractModel):
  """
  Model to store invitation data between users.

  Stores who invited who, when and where they want to meet,
  what activity they want to do, and the current status.
  """

  sender = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="sent_invitations",
    verbose_name=_("Sender"),
    help_text=_("User who sent the invitation"),
  )

  recipient = models.ForeignKey(
    User,
    on_delete=models.CASCADE,
    related_name="received_invitations",
    verbose_name=_("Recipient"),
    help_text=_("User who received the invitation"),
  )

  invitation_date = models.DateField(
    verbose_name=_("Invitation Date"), help_text=_("Date of the planned meeting")
  )

  invitation_time = models.TimeField(
    verbose_name=_("Invitation Time"), help_text=_("Time of the planned meeting")
  )

  activity = models.ForeignKey(
    Activity,
    on_delete=models.CASCADE,
    verbose_name=_("Activity"),
    help_text=_("Activity planned for the invitation"),
  )

  location = gis_models.PointField(
    verbose_name=_("Meeting Location"),
    geography=True,
    spatial_index=True,
    help_text=_("Geographic location for the meeting"),
  )

  status = models.CharField(
    max_length=20,
    choices=InvitationStatus.choices,
    default=InvitationStatus.PENDING,
    verbose_name=_("Status"),
    help_text=_("Current status of the invitation"),
  )

  responded_at = models.DateTimeField(
    null=True,
    blank=True,
    verbose_name=_("Responded At"),
    help_text=_("When the recipient responded to the invitation"),
  )

  meeting_address = models.CharField(
    max_length=255,
    null=True,
    blank=True,
    verbose_name=_("Meeting Address"),
    help_text=_("Address of the meeting location"),
  )

  payment = models.OneToOneField(
    Payment,
    on_delete=models.SET_NULL,
    null=True,
    blank=True,
    related_name="invitation",
    verbose_name=_("Payment"),
    help_text=_("Payment associated with this invitation"),
  )

  is_verified = models.BooleanField(
    default=False,
    verbose_name=_("Is Verified"),
    help_text=_("Indicates if the invitation has been verified via session QR code"),
  )

  class Meta:
    verbose_name = _("Invitation")
    verbose_name_plural = _("Invitations")
    ordering = ["-created_at"]

  def __str__(self):
    return f"Invitation from {self.sender.full_name} to {self.recipient.full_name}"
