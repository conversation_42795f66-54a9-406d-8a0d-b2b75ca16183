from django.urls import path

from .views import (
  SendInvitationView,
  RespondInvitationView,
  PendingInvitationsView,
  AcceptedInvitationsView,
  CancelInvitationView,
  InvitationDetailView,
  InvitationVerificationViewSet,
)

urlpatterns = [
  path("send/", SendInvitationView.as_view(), name="send_invitation"),
  path("respond/", RespondInvitationView.as_view(), name="respond_invitation"),
  path("cancel/", CancelInvitationView.as_view(), name="cancel_invitation"),
  path("pending/", PendingInvitationsView.as_view(), name="pending_invitations"),
  path("accepted/", AcceptedInvitationsView.as_view(), name="accepted_invitations"),
  path(
    "<uuid:pk>/verify-invitation/",
    InvitationVerificationViewSet.as_view({"post": "verify_invitation"}),
    name="verify-invitation",
  ),
  path(
    "<uuid:pk>/qr-code/",
    InvitationVerificationViewSet.as_view({"get": "get_session_qr_code"}),
    name="get-session-qr-code",
  ),
  path("<uuid:pk>/", InvitationDetailView.as_view(), name="invitation_detail"),
]
