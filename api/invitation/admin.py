from django.contrib import admin
from django.contrib.gis.admin import OSMGeoAdmin
from unfold.admin import ModelAdmin
from .models import Invitation


@admin.register(Invitation)
class InvitationAdmin(ModelAdmin, OSMGeoAdmin):
  """Admin interface for Invitation model"""

  list_display = (
    "id",
    "sender",
    "recipient",
    "invitation_date",
    "is_verified",
    "invitation_time",
    "meeting_address",
    "activity",
    "status",
    "payment",
    "created_at",
  )
  list_filter = ("status", "invitation_date", "created_at", "payment__status")
  search_fields = ("sender__email", "recipient__email", "sender__full_name", "recipient__full_name")
  readonly_fields = ("created_at", "updated_at")
  date_hierarchy = "invitation_date"
  fieldsets = (
    ("Participants", {"fields": ("sender", "recipient")}),
    (
      "Invitation Details",
      {"fields": ("invitation_date", "invitation_time", "activity", "location", "meeting_address")},
    ),
    ("Status Information", {"fields": ("status", "responded_at", "payment", "is_verified")}),
    ("Metadata", {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
  )

  def get_queryset(self, request):
    """Optimize queryset with select_related to reduce database queries"""
    return (
      super().get_queryset(request).select_related("sender", "recipient", "activity", "payment")
    )

  autocomplete_fields = ["sender", "recipient", "activity", "payment"]
