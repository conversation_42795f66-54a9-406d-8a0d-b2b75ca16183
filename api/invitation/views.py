from django.contrib.gis.geos import Point
from django.db import models
from rest_framework import viewsets
from rest_framework.decorators import action

from django.shortcuts import get_object_or_404
from rest_framework.generics import GenericAPIView, ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema
from drf_spectacular.types import OpenApiTypes

from core.utils.app_response import AppPagination, AppResponse
from core.utils.app_logger import AppLogger
from core.utils.app_msg import AppMsg
from api.consts.swagger_utils import SwaggerCommonExamples

from api.session.models import Session
from .models import Invitation, InvitationStatus
from .serializers import (
  InvitationSerializer,
  SendInvitationSerializer,
  RespondInvitationSerializer,
  CancelInvitationSerializer,
  InvitationListSerializer,
  VerifyInvitationSerializer,
  SessionQRCodeSerializer,
)
from .services import InvitationService


logger = AppLogger()


@extend_schema(tags=["Invitation"])
class InvitationVerificationViewSet(viewsets.ViewSet):
  permission_classes = [IsAuthenticated]

  @extend_schema(
    summary="Verify an invitation using a session code",
    request=VerifyInvitationSerializer,
    responses={
      200: OpenApiTypes.OBJECT,
      400: OpenApiTypes.OBJECT,
      403: OpenApiTypes.OBJECT,
      404: OpenApiTypes.OBJECT,
    },
  )
  @action(detail=True, methods=["post"], url_path="verify-invitation")
  def verify_invitation(self, request, pk=None):
    invitation = get_object_or_404(Invitation, pk=pk)
    session = get_object_or_404(Session, invitation=invitation)

    if request.user != session.guest:
      return AppResponse.error(message=AppMsg.USER_IS_NOT_A_GUEST)

    serializer = VerifyInvitationSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    if serializer.validated_data["session_code"] == session.session_code:
      invitation.is_verified = True
      invitation.save()
      return AppResponse.success(message=AppMsg.INVITATION_VERIFIED)
    else:
      return AppResponse.error(message=AppMsg.INVALID_SESSION_CODE)

  @extend_schema(
    summary="Get the session QR code for an invitation",
    responses={
      200: SessionQRCodeSerializer,
      403: OpenApiTypes.OBJECT,
      404: OpenApiTypes.OBJECT,
    },
  )
  @action(detail=True, methods=["get"], url_path="qr-code")
  def get_session_qr_code(self, request, pk=None):
    invitation = get_object_or_404(Invitation, pk=pk)
    session = get_object_or_404(Session, invitation=invitation)

    if request.user != session.host:
      return AppResponse.error(message=AppMsg.USER_IS_NOT_A_HOST)

    serializer = SessionQRCodeSerializer(session)
    return AppResponse.success(data=serializer.data)


@extend_schema(
  tags=["Invitation"],
  responses={
    200: OpenApiTypes.OBJECT,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Send an invitation to another user",
  description="Send an invitation from the authenticated user to another user with date, time, activity and location.",
)
class SendInvitationView(GenericAPIView):
  """
  Handles sending an invitation from the authenticated user to another user.
  Requires date, time, activity type, and location coordinates.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = SendInvitationSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data, context={"request": request})
    serializer.is_valid(raise_exception=True)

    sender = request.user
    recipient = serializer.validated_data["recipient_id"]
    invitation_date = serializer.validated_data["invitation_date"]
    invitation_time = serializer.validated_data["invitation_time"]
    activity_id = serializer.validated_data["activity_id"]
    meeting_address = serializer.validated_data["meeting_address"]

    # Create a GIS Point from the coordinates
    lat = serializer.validated_data["latitude"]
    lng = serializer.validated_data["longitude"]
    location = Point(lng, lat, srid=4326)  # WGS84 coordinate system

    result = InvitationService.send_invitation(
      sender=sender,
      recipient=recipient,
      invitation_date=invitation_date,
      invitation_time=invitation_time,
      activity_id=activity_id,
      location=location,
      meeting_address=meeting_address,
    )

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Invitation"],
  responses={
    200: OpenApiTypes.OBJECT,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Respond to a received invitation",
  description="Respond to a received invitation (accept or reject).",
)
class RespondInvitationView(GenericAPIView):
  """
  Handles responding to a received invitation (accept or reject).
  """

  permission_classes = [IsAuthenticated]
  serializer_class = RespondInvitationSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data, context={"request": request})
    serializer.is_valid(raise_exception=True)

    responder = request.user
    invitation = serializer.validated_data["invitation_id"]
    response_status = serializer.validated_data["status"]

    result = InvitationService.respond_to_invitation(
      responder=responder, invitation=invitation, response_status=response_status
    )

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Invitation"],
  responses={
    200: OpenApiTypes.OBJECT,
    400: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Cancel a sent invitation",
  description="Cancel an invitation that was sent by the authenticated user and is still pending.",
)
class CancelInvitationView(GenericAPIView):
  """
  Handles canceling a sent invitation that is still pending.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = CancelInvitationSerializer

  def post(self, request, *args, **kwargs):
    serializer = self.get_serializer(data=request.data, context={"request": request})
    serializer.is_valid(raise_exception=True)

    sender = request.user
    invitation = serializer.validated_data["invitation_id"]

    result = InvitationService.cancel_invitation(sender=sender, invitation=invitation)

    if result.success:
      return AppResponse.success(message=result.message, data=result.data)

    return AppResponse.error(message=result.message)


@extend_schema(
  tags=["Invitation"],
  responses={
    200: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="List pending invitations",
  description="Get a list of pending invitations for the authenticated user (both sent and received).",
)
class PendingInvitationsView(ListAPIView):
  """
  Returns a list of pending invitations for the authenticated user (both sent and received).
  """

  permission_classes = [IsAuthenticated]
  serializer_class = InvitationListSerializer
  pagination_class = AppPagination

  def get_queryset(self):
    # Get both sent and received invitations with PENDING status
    user = self.request.user
    return Invitation.objects.filter(
      models.Q(sender=user) | models.Q(recipient=user), status=InvitationStatus.PENDING
    )

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())

    # Apply pagination
    page = self.paginate_queryset(queryset)
    if page is not None:
      serializer = self.get_serializer(page, many=True, context={"request": request})
      return self.get_paginated_response(serializer.data)

    serializer = self.get_serializer(queryset, many=True, context={"request": request})
    return AppResponse.success(data=serializer.data)


@extend_schema(
  tags=["Invitation"],
  responses={
    200: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="List accepted invitations",
  description="Get a list of accepted invitations for the authenticated user (both sent and received).",
)
class AcceptedInvitationsView(ListAPIView):
  """
  Returns a list of accepted invitations for the authenticated user (both sent and received).
  """

  permission_classes = [IsAuthenticated]
  serializer_class = InvitationListSerializer
  pagination_class = AppPagination

  def get_queryset(self):
    # Get both sent and received invitations with ACCEPTED status
    user = self.request.user

    # Base queryset with invitation filtering
    queryset = Invitation.objects.filter(
      models.Q(sender=user) | models.Q(recipient=user), status=InvitationStatus.ACCEPTED
    )

    # Prefetch related data for better performance
    queryset = queryset.select_related("sender", "recipient", "activity", "payment")

    return queryset

  def list(self, request, *args, **kwargs):
    queryset = self.filter_queryset(self.get_queryset())

    # Apply pagination
    page = self.paginate_queryset(queryset)
    if page is not None:
      serializer = self.get_serializer(page, many=True, context={"request": request})
      return self.get_paginated_response(serializer.data)

    serializer = self.get_serializer(queryset, many=True, context={"request": request})
    return AppResponse.success(data=serializer.data)


@extend_schema(
  tags=["Invitation"],
  responses={
    200: OpenApiTypes.OBJECT,
    401: OpenApiTypes.OBJECT,
    404: OpenApiTypes.OBJECT,
    500: OpenApiTypes.OBJECT,
  },
  examples=[
    SwaggerCommonExamples.not_authenticated,
  ],
  summary="Get invitation details",
  description="Get detailed information about a specific invitation.",
)
class InvitationDetailView(RetrieveAPIView):
  """
  Returns detailed information about a specific invitation.
  User must be either the sender or recipient.
  """

  permission_classes = [IsAuthenticated]
  serializer_class = InvitationSerializer

  def get_queryset(self):
    user = self.request.user
    return Invitation.objects.filter(models.Q(sender=user) | models.Q(recipient=user))

  def retrieve(self, request, *args, **kwargs):
    instance = self.get_object()
    serializer = self.get_serializer(instance)
    return AppResponse.success(data=serializer.data)
