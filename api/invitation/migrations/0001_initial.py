# Generated by Django 4.2.9 on 2025-05-14 14:48

from django.conf import settings
import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('activity', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Invitation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('invitation_date', models.DateField(help_text='Date of the planned meeting', verbose_name='Invitation Date')),
                ('invitation_time', models.TimeField(help_text='Time of the planned meeting', verbose_name='Invitation Time')),
                ('location', django.contrib.gis.db.models.fields.PointField(geography=True, help_text='Geographic location for the meeting', srid=4326, verbose_name='Meeting Location')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('expired', 'Expired'), ('canceled', 'Canceled')], default='pending', help_text='Current status of the invitation', max_length=20, verbose_name='Status')),
                ('responded_at', models.DateTimeField(blank=True, help_text='When the recipient responded to the invitation', null=True, verbose_name='Responded At')),
                ('activity', models.ForeignKey(help_text='Activity planned for the invitation', on_delete=django.db.models.deletion.CASCADE, to='activity.activity', verbose_name='Activity')),
                ('recipient', models.ForeignKey(help_text='User who received the invitation', on_delete=django.db.models.deletion.CASCADE, related_name='received_invitations', to=settings.AUTH_USER_MODEL, verbose_name='Recipient')),
                ('sender', models.ForeignKey(help_text='User who sent the invitation', on_delete=django.db.models.deletion.CASCADE, related_name='sent_invitations', to=settings.AUTH_USER_MODEL, verbose_name='Sender')),
            ],
            options={
                'verbose_name': 'Invitation',
                'verbose_name_plural': 'Invitations',
                'ordering': ['-created_at'],
            },
        ),
    ]
