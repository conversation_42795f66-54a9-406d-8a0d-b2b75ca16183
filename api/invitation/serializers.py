from rest_framework import serializers
from django.utils import timezone
from django.contrib.gis.geos import Point
from django.db.models import Q

from access.models import User
from .models import Invitation, InvitationStatus
from api.activity.models import Activity
from api.utils import ApiUtils
from core.utils.app_msg import AppMsg
from api.session.models import Session, SessionStatus


class VerifyInvitationSerializer(serializers.Serializer):
  """Serializer for verifying an invitation with a session code"""

  session_code = serializers.CharField(max_length=8, required=True)


class SessionQRCodeSerializer(serializers.ModelSerializer):
  """Serializer for exposing the session QR code"""

  class Meta:
    model = Session
    fields = ["qr_code"]


class PublicActivitySerializer(serializers.ModelSerializer):
  """Serializer for representing public activity details (without sensitive info)"""

  class Meta:
    model = Activity
    fields = ["id", "name", "price", "created_at", "updated_at"]
    read_only_fields = ["id", "created_at", "updated_at"]


class InvitationSerializer(serializers.ModelSerializer):
  """Serializer for representing invitation details"""

  sender_name = serializers.SerializerMethodField()
  recipient_name = serializers.SerializerMethodField()
  latitude = serializers.FloatField(write_only=True)
  longitude = serializers.FloatField(write_only=True)
  activity = PublicActivitySerializer(read_only=True)
  activity_id = serializers.UUIDField(write_only=True)

  class Meta:
    model = Invitation
    fields = [
      "id",
      "sender",
      "sender_name",
      "recipient",
      "recipient_name",
      "invitation_date",
      "invitation_time",
      "meeting_address",
      "activity",
      "activity_id",
      "latitude",
      "longitude",
      "is_verified",
      "location",
      "status",
      "responded_at",
      "created_at",
      "updated_at",
    ]
    read_only_fields = [
      "id",
      "sender_name",
      "recipient_name",
      "activity",
      "status",
      "responded_at",
      "location",
      "created_at",
      "updated_at",
    ]

  def get_sender_name(self, obj) -> str:
    return obj.sender.full_name

  def get_recipient_name(self, obj) -> str:
    return obj.recipient.full_name

  def validate_activity_id(self, value):
    """Validate that the activity exists"""
    try:
      Activity.objects.get(id=value)
      return value
    except Activity.DoesNotExist:
      raise serializers.ValidationError("Invalid activity ID. Activity does not exist.")

  def validate_invitation_date(self, value):
    """Validate that the invitation date is not in the past"""
    if value < timezone.now().date():
      raise serializers.ValidationError("Invitation date cannot be in the past")
    return value

  def validate(self, data):
    """Validate that the sender cannot invite themselves"""
    if "sender" in data and "recipient" in data and data["sender"] == data["recipient"]:
      raise serializers.ValidationError("You cannot invite yourself")

    # Create Point object from latitude and longitude
    if "latitude" in data and "longitude" in data:
      data["location"] = Point(data.pop("longitude"), data.pop("latitude"), srid=4326)

    return data

  def create(self, validated_data):
    activity_id = validated_data.pop("activity_id")
    activity = Activity.objects.get(id=activity_id)
    instance = Invitation.objects.create(**validated_data, activity=activity)
    return instance

  def update(self, instance, validated_data):
    if "activity_id" in validated_data:
      activity_id = validated_data.pop("activity_id")
      activity = Activity.objects.get(id=activity_id)
      instance.activity = activity

    return super().update(instance, validated_data)


class SendInvitationSerializer(serializers.Serializer):
  """Serializer for sending an invitation"""

  recipient_id = serializers.UUIDField()
  invitation_date = serializers.DateField()
  invitation_time = serializers.TimeField()
  activity_id = serializers.UUIDField()
  latitude = serializers.FloatField()
  longitude = serializers.FloatField()
  meeting_address = serializers.CharField(max_length=255, required=True)

  def validate_recipient_id(self, value):
    """Validate that the recipient exists"""
    try:
      recipient = User.objects.get(id=value)
      return recipient
    except User.DoesNotExist:
      raise serializers.ValidationError("Recipient not found")

  def validate_activity_id(self, value):
    """Validate that the activity exists"""
    try:
      Activity.objects.get(id=value)
      return value
    except Activity.DoesNotExist:
      raise serializers.ValidationError("Activity not found")

  def validate(self, data):
    """Validate that the invitation date is not in the past"""
    if data["invitation_date"] < timezone.now().date():
      raise serializers.ValidationError(
        {"invitation_date": "Invitation date cannot be in the past"}
      )

    request = self.context.get("request")
    if request and request.user:
      sender = request.user
      recipient = data["recipient_id"]

      if sender.id == recipient.id:
        raise serializers.ValidationError("You cannot invite yourself")

    return data


class RespondInvitationSerializer(serializers.Serializer):
  """Serializer for responding to an invitation"""

  invitation_id = serializers.UUIDField()
  status = serializers.ChoiceField(
    choices=[(InvitationStatus.ACCEPTED, "Accept"), (InvitationStatus.REJECTED, "Reject")]
  )

  def validate_invitation_id(self, value):
    """Validate that the invitation exists and belongs to the user"""
    request = self.context.get("request")
    if not request or not request.user:
      raise serializers.ValidationError("Authentication required")

    try:
      invitation = Invitation.objects.get(id=value, recipient=request.user)
      return invitation
    except Invitation.DoesNotExist:
      raise serializers.ValidationError("Invitation not found or does not belong to you")

  def validate(self, data):
    """Validate that the invitation is still pending"""
    invitation = data["invitation_id"]

    if invitation.status == InvitationStatus.CANCELED:
      raise serializers.ValidationError({"invitation_id": AppMsg.INVITATION_CANCELLED})

    if invitation.status != InvitationStatus.PENDING:
      raise serializers.ValidationError({"invitation_id": AppMsg.INVITATION_ALREADY_RESPONDED})

    if invitation.invitation_date < timezone.now().date():
      raise serializers.ValidationError({"invitation_id": AppMsg.INVITATION_EXPIRED})

    return data


class CancelInvitationSerializer(serializers.Serializer):
  """Serializer for canceling an invitation"""

  invitation_id = serializers.UUIDField()

  def validate_invitation_id(self, value):
    """Validate that the invitation exists and belongs to the user"""
    request = self.context.get("request")
    if not request or not request.user:
      raise serializers.ValidationError("Authentication required")

    try:
      # Use Q objects to check if either sender OR recipient is the current user
      invitation = Invitation.objects.get(
        Q(sender=request.user) | Q(recipient=request.user), id=value
      )
      return invitation
    except Invitation.DoesNotExist:
      raise serializers.ValidationError(AppMsg.INVITATION_NOT_FOUND)


class InvitationListSerializer(serializers.ModelSerializer):
  """Base serializer for invitation list views with common fields"""

  display_name = serializers.SerializerMethodField()
  is_inviter = serializers.SerializerMethodField()
  display_profile_image = serializers.SerializerMethodField()
  other_user_id = serializers.SerializerMethodField()
  activity = PublicActivitySerializer(read_only=True)
  payment_required = serializers.SerializerMethodField()
  payment_status = serializers.SerializerMethodField()
  can_start_session = serializers.SerializerMethodField()
  is_date_time_reached = serializers.SerializerMethodField()
  session_id = serializers.SerializerMethodField()
  is_finished = serializers.SerializerMethodField()

  class Meta:
    model = Invitation
    fields = [
      "id",
      "invitation_date",
      "invitation_time",
      "activity",
      "status",
      "is_finished",
      "is_verified",
      "meeting_address",
      "display_name",
      "is_inviter",
      "display_profile_image",
      "other_user_id",
      "responded_at",
      "payment_required",
      "payment_status",
      "session_id",
      "can_start_session",
      "is_date_time_reached",
      "created_at",
      "updated_at",
    ]
    read_only_fields = fields

  def get_is_finished(self, obj) -> bool:
    try:
      return obj.session.status == SessionStatus.COMPLETED
    except Exception:
      return False

  def get_session_id(self, obj):
    """Get the session ID associated with this invitation"""
    # The 'session' is a reverse one-to-one relationship from the Session model.
    # It can be null if a session hasn't been created for the invitation yet.
    # Accessing obj.session directly raises an exception if the session
    # does not exist. We use a try-except block to handle this gracefully.
    try:
      return obj.session.id
    except Exception:
      return None

  def get_other_user_id(self, obj):
    """Get the ID of the other user (not the authenticated user)"""
    request = self.context.get("request")
    if not request or not request.user.is_authenticated:
      return None

    if obj.sender == request.user:
      return str(obj.recipient.id)
    else:
      return str(obj.sender.id)

  def get_payment_required(self, obj):
    """
    Check if payment is required for this invitation.
    Payment is required if:
    1. The user is the sender (inviter)
    2. The invitation status is ACCEPTED
    3. The invitation date and time have been reached
    """
    request = self.context.get("request")
    if not request or not request.user.is_authenticated:
      return False

    # Only the sender needs to pay
    if obj.sender != request.user:
      return False

    # Only accepted invitations require payment
    if obj.status != InvitationStatus.ACCEPTED:
      return False

    invitation_datetime = timezone.make_aware(
      timezone.datetime.combine(obj.invitation_date, obj.invitation_time)
    )
    return timezone.now() >= invitation_datetime

  def get_payment_status(self, obj):
    """
    Get the payment status for this invitation.
    Returns:
      - "not_required": if payment is not required
      - "required": if payment is required but not made
      - "pending": if payment is in progress
      - "succeeded": if payment was successful
      - "failed": if payment failed
    """
    request = self.context.get("request")
    if not request or not request.user.is_authenticated:
      return "not_required"

    # If payment is not required, return "not_required"
    if not self.get_payment_required(obj):
      return "not_required"

    # Check if we have a direct payment relationship
    if obj.payment:
      return obj.payment.status

    return "required"

  def get_is_date_time_reached(self, obj):
    """Check if the invitation date and time have been reached"""
    invitation_datetime = timezone.make_aware(
      timezone.datetime.combine(obj.invitation_date, obj.invitation_time)
    )
    return timezone.now() >= invitation_datetime

  def get_can_start_session(self, obj):
    """
    Check if a session can be started for this invitation.
    A session can be started if:
    1. The invitation status is ACCEPTED
    2. The invitation date and time have been reached
    3. The payment has been made and succeeded (if payment is required)
    """
    request = self.context.get("request")
    if not request or not request.user.is_authenticated:
      return False

    # Only accepted invitations can start sessions
    if obj.status != InvitationStatus.ACCEPTED:
      return False

    invitation_datetime = timezone.make_aware(
      timezone.datetime.combine(obj.invitation_date, obj.invitation_time)
    )
    if timezone.now() < invitation_datetime:
      return False

    # If the user is the sender, check if payment has been made
    if obj.sender == request.user:
      payment_status = self.get_payment_status(obj)
      return payment_status == "succeeded"

    # If the user is the recipient, they can start the session if the payment has been made
    return obj.payment is not None and obj.payment.status == "succeeded"

  def get_is_inviter(self, obj):
    """Check if the authenticated user is the one who sent the invitation (inviter)

    This is useful for the frontend to determine whether to show accept/reject buttons,
    which should only be visible to the invitee (recipient of the invitation).
    """
    request = self.context.get("request")
    if not request or not request.user:
      return False

    return obj.sender == request.user

  def get_display_profile_image(self, obj):
    """Get the profile image of the other user (not the authenticated user)"""
    request = self.context.get("request")
    if not request or not request.user:
      # TODO: Should be handled with validators?
      return None

    other_user = obj.recipient if obj.sender == request.user else obj.sender
    return ApiUtils.get_first_profile_image(other_user, request)

  def get_display_name(self, obj):
    """Get the name of the other user (not the authenticated user)"""
    request = self.context.get("request")
    if not request or not request.user:
      return None

    if obj.sender == request.user:
      return obj.recipient.full_name
    return obj.sender.full_name
