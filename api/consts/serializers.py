from rest_framework import serializers


class BaseResponseSerializer(serializers.Serializer):
  status = serializers.BooleanField()
  message = serializers.CharField()
  errors = serializers.JSONField(allow_null=True)


class ErrorResponseSerializer(serializers.Serializer):
  status = serializers.BooleanField()
  message = serializers.CharField()
  data = serializers.JSONField(allow_null=True)
  errors = serializers.JSONField()
