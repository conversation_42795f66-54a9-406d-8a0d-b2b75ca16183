from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from django.http import HttpResponse
from django.views import View
from drf_spectacular.utils import extend_schema

from core.utils.app_response import AppResponse
from .services import StripeConnectService
from .serializers import (
  StripeConnectAccountCreateSerializer,
  StripeConnectVerifySerializer,
  StripeConnectLinkSerializer,
  StripeConnectRefreshSerializer,
)


@extend_schema(tags=["Stripe Connect"])
class StripeConnectViewSet(viewsets.ViewSet):
  """
  Viewset for managing Stripe Connect account creation and verification
  """

  permission_classes = [permissions.IsAuthenticated]

  @extend_schema(
    summary="Create Stripe Connect Account",
    request=StripeConnectAccountCreateSerializer,
    responses={200: StripeConnectAccountCreateSerializer},
  )
  @action(detail=False, methods=["POST"], url_path="create")
  def create_stripe_connect_account(self, request):
    """
    Create a Stripe Connect account for the current user
    """
    serializer = StripeConnectAccountCreateSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    result = StripeConnectService.create_stripe_connect_account(
      request.user, serializer.validated_data
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    return AppResponse.success(message=result.message, data=result.data)

  @extend_schema(
    summary="Generate Stripe Connect Account Onboarding Link",
    request=StripeConnectLinkSerializer,
    responses={200: StripeConnectLinkSerializer},
  )
  @action(detail=False, methods=["POST"], url_path="link")
  def generate_account_link(self, request):
    """
    Generate an account link for Stripe Connect onboarding
    """
    serializer = StripeConnectLinkSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    result = StripeConnectService.get_account_link(
      request.user, serializer.validated_data.get("refresh", True)
    )

    if not result.success:
      return AppResponse.error(message=result.message)

    return AppResponse.success(message=result.message, data=result.data)

  @extend_schema(
    summary="Verify Stripe Connect Account Status",
    request=StripeConnectVerifySerializer,
    responses={200: StripeConnectVerifySerializer},
  )
  @action(detail=False, methods=["GET"], url_path="verify")
  def verify_account(self, request):
    """
    Check the verification status of the Stripe Connect account
    """
    result = StripeConnectService.verify_stripe_connect_account(request.user)

    if not result.success:
      return AppResponse.error(message=result.message)

    return AppResponse.success(message=result.message, data=result.data)

  @extend_schema(
    summary="Refresh Stripe Connect Account Data",
    responses={200: StripeConnectRefreshSerializer},
  )
  @action(detail=False, methods=["GET"], url_path="refresh")
  def refresh_account(self, request):
    """
    Refresh Stripe Connect account data from Stripe API
    """
    result = StripeConnectService.refresh_from_stripe(request.user)

    if not result.success:
      return AppResponse.error(message=result.message)

    return AppResponse.success(message=result.message, data=result.data)


class OnboardingCompleteView(View):
  """
  Handle the redirect from Stripe after onboarding completion
  This view renders a simple HTML page that communicates with the mobile app
  """

  def get(self, request):
    """
    Handle GET request from Stripe redirect
    """
    # Create a simple HTML response that will communicate with the mobile app
    # The mobile app should be registered to handle the rentie:// scheme
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Stripe Connect Onboarding Complete</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #32325d;
            }
            p {
                color: #525f7f;
                margin-bottom: 20px;
            }
            .success {
                color: #3ecf8e;
                font-weight: bold;
            }
            .button {
                background-color: #6772e5;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 16px;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Stripe Connect Setup</h1>
            <p class="success">✅ Onboarding process completed!</p>
            <p>Your account information has been submitted to Stripe.</p>
            <p>You can now close this browser window and return to the app.</p>
        </div>
        
        <script>
            // Try to use a custom URL scheme to return to the app
            // This will attempt to open the app if the user has it installed
            // but won't do anything if they don't
            setTimeout(function() {
                window.location.href = "rentie://stripe-connect/onboarding-complete";
            }, 1500);
        </script>
    </body>
    </html>
    """

    return HttpResponse(html_content)
