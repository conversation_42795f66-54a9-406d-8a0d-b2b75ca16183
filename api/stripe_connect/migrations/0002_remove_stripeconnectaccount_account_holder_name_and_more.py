# Generated by Django 4.2.9 on 2025-06-05 11:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stripe_connect', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='account_holder_name',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='account_number',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='business_address',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='business_email',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='business_name',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='business_type',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='business_website',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='city',
        ),
        migrations.Remove<PERSON>ield(
            model_name='stripeconnectaccount',
            name='date_of_birth',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='email_address',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='first_name',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='iban',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='last_name',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='phone_number',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='postal_code',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='residential_address',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='state',
        ),
        migrations.RemoveField(
            model_name='stripeconnectaccount',
            name='swift_code',
        ),
        migrations.AddField(
            model_name='stripeconnectaccount',
            name='account_type',
            field=models.CharField(blank=True, help_text='Type of Stripe account (individual, company, etc.)', max_length=50, null=True, verbose_name='Account Type'),
        ),
        migrations.AddField(
            model_name='stripeconnectaccount',
            name='display_name',
            field=models.CharField(blank=True, help_text='Name to display in the UI', max_length=255, null=True, verbose_name='Display Name'),
        ),
        migrations.AddField(
            model_name='stripeconnectaccount',
            name='last_verification_check',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Last Verification Check'),
        ),
    ]
