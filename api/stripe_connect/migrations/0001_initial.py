# Generated by Django 4.2.9 on 2025-05-13 16:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StripeConnectAccount',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True, verbose_name='id')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account_id', models.CharField(max_length=255, unique=True, verbose_name='Stripe Connect Account ID')),
                ('customer_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Customer ID')),
                ('is_verified', models.BooleanField(default=False, verbose_name='Account Verified')),
                ('details_submitted', models.BooleanField(default=True, verbose_name='Account Details Submitted')),
                ('business_name', models.CharField(max_length=255, verbose_name='Business Name')),
                ('business_type', models.CharField(max_length=100, verbose_name='Business Type')),
                ('business_address', models.CharField(max_length=255, verbose_name='Business Address')),
                ('business_email', models.EmailField(max_length=254, verbose_name='Business Email')),
                ('business_website', models.URLField(blank=True, verbose_name='Business Website')),
                ('first_name', models.CharField(max_length=100, verbose_name='First Name')),
                ('last_name', models.CharField(max_length=100, verbose_name='Last Name')),
                ('date_of_birth', models.DateField(verbose_name='Date of Birth')),
                ('email_address', models.EmailField(max_length=254, verbose_name='Email Address')),
                ('phone_number', models.CharField(max_length=20, verbose_name='Phone Number')),
                ('residential_address', models.CharField(max_length=255, verbose_name='Residential Address')),
                ('city', models.CharField(max_length=100, verbose_name='City')),
                ('state', models.CharField(max_length=100, verbose_name='State')),
                ('postal_code', models.CharField(max_length=20, verbose_name='Postal Code')),
                ('account_holder_name', models.CharField(max_length=255, verbose_name='Account Holder Name')),
                ('account_number', models.CharField(max_length=50, verbose_name='Account Number')),
                ('swift_code', models.CharField(blank=True, max_length=50, verbose_name='SWIFT/BIC Code')),
                ('iban', models.CharField(blank=True, max_length=50, verbose_name='IBAN')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stripe_connect_account', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Stripe Connect Account',
                'verbose_name_plural': 'Stripe Connect Accounts',
            },
        ),
    ]
