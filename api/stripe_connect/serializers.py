from rest_framework import serializers

from api.profile.serializers import UserProfilePublicSerializer


class StripeConnectAccountSerializer(serializers.Serializer):
  """
  Serializer for retrieving StripeConnectAccount data.
  """

  id = serializers.UUIDField(read_only=True)
  user = UserProfilePublicSerializer(read_only=True)
  account_id = serializers.CharField(read_only=True)
  customer_id = serializers.CharField(read_only=True)
  is_verified = serializers.BooleanField(read_only=True)
  details_submitted = serializers.BooleanField(read_only=True)
  
  # Display information
  display_name = serializers.CharField(read_only=True)
  account_type = serializers.CharField(read_only=True)
  last_verification_check = serializers.DateTimeField(read_only=True)

  # Timestamps
  created_at = serializers.DateTimeField(read_only=True)
  updated_at = serializers.DateTimeField(read_only=True)


class StripeConnectAccountCreateSerializer(serializers.Serializer):
  """
  Simplified serializer for creating a Stripe Connect account.
  This serializer works with the StripeConnectService and aims to provide
  a more streamlined user experience while collecting all required information.
  """

  # Business Information
  business_name = serializers.CharField(required=True, max_length=255)
  business_type = serializers.CharField(required=True, max_length=100, 
                                      help_text="Either 'individual' or 'company'")
  
  # Contact Information (consolidated)
  email = serializers.EmailField(required=True, 
                               help_text="Used for both business and personal communication")
  phone_number = serializers.CharField(required=True, max_length=20)
  
  # Personal Information
  first_name = serializers.CharField(required=True, max_length=100)
  last_name = serializers.CharField(required=True, max_length=100)
  date_of_birth = serializers.DateField(required=True)
  
  # Address Information (consolidated)
  address = serializers.CharField(required=True, max_length=255, 
                               help_text="Primary address for business and personal use")
  city = serializers.CharField(required=True, max_length=100)
  state = serializers.CharField(required=True, max_length=100)
  postal_code = serializers.CharField(required=True, max_length=20)
  use_different_personal_address = serializers.BooleanField(default=False, 
                                                        help_text="Set to true if personal address differs from business address")
  personal_address = serializers.CharField(required=False, max_length=255, 
                                        help_text="Only required if use_different_personal_address is true")
  
  # Bank Account Information (one of these must be provided)
  iban = serializers.CharField(required=False, allow_blank=True, max_length=50, 
                            help_text="IBAN for European bank accounts")
  account_number = serializers.CharField(required=False, max_length=50, 
                                      help_text="Account number for non-IBAN countries")
  routing_number = serializers.CharField(required=False, max_length=50, 
                                      help_text="Routing/SWIFT/BIC code for non-IBAN countries")
  
  # Optional fields
  business_website = serializers.URLField(required=False, allow_blank=True)

  def validate(self, data):
    """
    Validate the data to ensure all required information is present.
    """
    # Check bank account information
    iban = data.get("iban")
    account_number = data.get("account_number")
    routing_number = data.get("routing_number")

    if not iban and not (account_number and routing_number):
      raise serializers.ValidationError(
          "Either IBAN or both account number and routing number must be provided"
      )

    # Check personal address if different from business address
    if data.get("use_different_personal_address") and not data.get("personal_address"):
      raise serializers.ValidationError(
          "Personal address is required when use_different_personal_address is true"
      )

    # Auto-generate account_holder_name
    data["account_holder_name"] = f"{data.get('first_name')} {data.get('last_name')}"
    
    # For backward compatibility with existing code
    data["business_email"] = data.get("email")
    data["email_address"] = data.get("email")
    data["business_address"] = data.get("address")
    data["residential_address"] = data.get("personal_address") if data.get("use_different_personal_address") else data.get("address")
    data["swift_code"] = data.get("routing_number")
            
    return data


class StripeConnectAccountPublicSerializer(serializers.Serializer):
  """
  Serializer for public information about a StripeConnectAccount.
  Excludes sensitive information.
  """

  id = serializers.UUIDField(read_only=True)
  is_verified = serializers.BooleanField(read_only=True)
  details_submitted = serializers.BooleanField(read_only=True)
  display_name = serializers.CharField(read_only=True)
  account_type = serializers.CharField(read_only=True)
  last_verification_check = serializers.DateTimeField(read_only=True)


class StripeConnectVerifySerializer(serializers.Serializer):
  """
  Serializer for verifying Stripe Connect account status
  """

  pass  # No input fields needed, uses current user context


class StripeConnectLinkSerializer(serializers.Serializer):
  """
  Serializer for generating Stripe Connect account link
  """

  refresh = serializers.BooleanField(default=False, required=False)
  url = serializers.URLField(read_only=True)


class StripeConnectRefreshSerializer(serializers.Serializer):
  """
  Serializer for refreshing Stripe Connect account data from Stripe API
  """
  
  account_id = serializers.CharField(read_only=True)
  is_verified = serializers.BooleanField(read_only=True)
  details_submitted = serializers.BooleanField(read_only=True)
  display_name = serializers.CharField(read_only=True)
  account_type = serializers.CharField(read_only=True)
  last_verification_check = serializers.DateTimeField(read_only=True)
