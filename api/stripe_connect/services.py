import os
import stripe
from typing import Dict, Any, Optional
from django.utils import timezone

from access.models import User
from api.stripe_connect.models import StripeConnectAccount
from core.utils.app_msg import AppMsg
from core.utils.app_logger import AppLogger
from api.consts.service_result import ServiceResult
from rentie import settings

logger = AppLogger()

# Ensure Stripe is configured with the secret key
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")


class StripeConnectService:
  def create_stripe_connect_account(user: User, business_info: Dict[str, Any]) -> ServiceResult:
    """
    Create a Stripe Connect account for the user with simplified field requirements

    This method first checks if the user already has a Stripe Connect account, either in our database
    or in Stripe's system. If an account exists in Stripe but not in our database, it will be
    synchronized and updated with the provided information.
    """
    try:
      # Check if user already has a Stripe Connect account
      stripe_account_model = StripeConnectService._get_stripe_account(user)

      # If we found an account model, check if it's complete
      if stripe_account_model:
        # If the account has both account_id and customer_id, it's already set up
        if stripe_account_model.account_id and stripe_account_model.customer_id:
          return ServiceResult(
            success=False,
            message=AppMsg.STRIPE_CONNECT_ALREADY_EXISTS,
          )

        # If we have an account_id but no customer_id, this might be a case where
        # we found an account in Stripe but need to complete the setup in our database
        elif stripe_account_model.account_id and not stripe_account_model.customer_id:
          logger.log_info(
            "StripeConnectService - create_stripe_connect_account",
            f"Found incomplete Stripe Connect account for user {user.id}. Updating with provided information.",
          )

          # We'll update the existing account with the provided information
          # and create a customer if needed
          try:
            # Create a customer for P2P transactions
            customer = stripe.Customer.create(
              name=f"{business_info.get('first_name')} {business_info.get('last_name')}",
              email=business_info.get("email"),
              description="User for P2P transactions",
            )

            # Update the existing account model with only essential information
            display_name = f"{business_info.get('first_name')} {business_info.get('last_name')}"
            if business_info.get("business_name"):
              display_name = business_info.get("business_name")

            stripe_account_model.customer_id = customer.id
            stripe_account_model.display_name = display_name
            stripe_account_model.account_type = business_info.get("business_type", "individual")
            stripe_account_model.details_submitted = True

            # Save the updated model
            stripe_account_model.save()

            return ServiceResult(
              success=True,
              data={"account_id": stripe_account_model.account_id, "customer_id": customer.id},
              message=AppMsg.STRIPE_CONNECT_CREATE_SUCCESS,
            )

          except Exception as e:
            logger.log_error(
              "StripeConnectService - create_stripe_connect_account",
              f"Error updating incomplete Stripe Connect account: {str(e)}",
            )
            # Continue with normal account creation flow if update fails

      # Parse date of birth (using date_of_birth from serializer)
      date_of_birth = business_info.get("date_of_birth")
      dob_day, dob_month, dob_year = None, None, None
      if date_of_birth:
        if isinstance(date_of_birth, str):
          # Handle string format (YYYY-MM-DD)
          dob_parts = date_of_birth.split("-")
          if len(dob_parts) == 3:
            dob_year, dob_month, dob_day = dob_parts
        elif (
          hasattr(date_of_birth, "day")
          and hasattr(date_of_birth, "month")
          and hasattr(date_of_birth, "year")
        ):
          # Handle Django/Python date object
          dob_day = date_of_birth.day
          dob_month = date_of_birth.month
          dob_year = date_of_birth.year

      # Get email (consolidated field for both business and personal)
      email = business_info.get("email")

      # Get address information
      address = business_info.get("address")
      residential_address = (
        business_info.get("personal_address")
        if business_info.get("use_different_personal_address")
        else address
      )

      # Get IBAN (required for European customers)
      iban = business_info.get("iban", "")

      # Validate IBAN
      if not iban:
        return ServiceResult(
          success=False,
          message="IBAN is required for European customers",
        )

      # Extract country code from IBAN
      country = iban[:2].upper() if len(iban) >= 2 else ""

      # List of European countries
      european_countries = [
        "AT",  # Austria
        "BE",  # Belgium
        "BG",  # Bulgaria
        "HR",  # Croatia
        "CY",  # Cyprus
        "CZ",  # Czech Republic
        "DK",  # Denmark
        "EE",  # Estonia
        "FI",  # Finland
        "FR",  # France
        "DE",  # Germany
        "GR",  # Greece
        "HU",  # Hungary
        "IE",  # Ireland
        "IT",  # Italy
        "LV",  # Latvia
        "LT",  # Lithuania
        "LU",  # Luxembourg
        "MT",  # Malta
        "NL",  # Netherlands
        "PL",  # Poland
        "PT",  # Portugal
        "RO",  # Romania
        "SK",  # Slovakia
        "SI",  # Slovenia
        "ES",  # Spain
        "SE",  # Sweden
        "GB",  # United Kingdom
      ]

      # Only support European countries
      if country not in european_countries:
        return ServiceResult(
          success=False,
          message="Currently, we only support European customers",
        )

      # Always use EUR currency for all European countries, regardless of their native currency

      # Create bank account token with IBAN
      bank_account_data = {
        "country": country,
        "currency": "EUR",  # Always use EUR
        "account_holder_name": business_info.get("account_holder_name"),
        "account_holder_type": "individual",
        "account_number": iban,  # IBAN is used as the account number
      }

      bank_account_token = stripe.Token.create(bank_account=bank_account_data)

      # Create a Custom Stripe Connect account
      account = stripe.Account.create(
        type="custom",  # Custom account type is required
        business_type=business_info.get("business_type", "individual"),
        business_profile={
          "name": business_info.get("business_name"),
          "mcc": "5734",  # Using the hardcoded value from legacy code
          "url": business_info.get("business_website", ""),
          "support_email": email,
          "support_address": {
            "line1": address,
          },
        },
        individual={
          "first_name": business_info.get("first_name"),
          "last_name": business_info.get("last_name"),
          "dob": {
            "day": dob_day,
            "month": dob_month,
            "year": dob_year,
          }
          if all([dob_day, dob_month, dob_year])
          else None,
          "email": email,
          "phone": business_info.get("phone_number"),
          "address": {
            "line1": residential_address,
            "postal_code": business_info.get("postal_code"),
            "city": business_info.get("city"),
            "state": business_info.get("state"),
          },
        },
        settings={
          "payouts": {
            "schedule": {
              "interval": "daily",
              "delay_days": 7,  # Minimum delay required by Stripe
            },
            "statement_descriptor": business_info.get("business_name"),
          }
        },
        # TOS acceptance (required for custom accounts)
        tos_acceptance={
          "date": timezone.now(),
          "ip": "*******",  # Hardcoded IP from legacy code
        },
        # Set capabilities (required for custom accounts)
        capabilities={
          "card_payments": {"requested": True},
          "transfers": {"requested": True},
        },
        # Attach the bank account
        external_account=bank_account_token.id,
        metadata={
          "user_id": user.id,
        },
      )

      # Create a customer for P2P transactions
      customer = stripe.Customer.create(
        name=f"{business_info.get('first_name')} {business_info.get('last_name')}",
        email=email,
        description="User for P2P transactions",
      )

      # Create the simplified database record with only essential information
      display_name = f"{business_info.get('first_name')} {business_info.get('last_name')}"
      if business_info.get("business_name"):
        display_name = business_info.get("business_name")

      # Create the database record
      stripe_account_model = StripeConnectAccount.objects.create(
        user=user,
        account_id=account.id,
        customer_id=customer.id,
        display_name=display_name,
        account_type=business_info.get("business_type", "individual"),
        is_verified=False,  # Will be updated later via verification check
        details_submitted=True,
      )

      # Check if verification is required by retrieving the account requirements
      verification_required = False
      try:
        # Retrieve the account to check requirements
        retrieved_account = stripe.Account.retrieve(account.id)

        # Use the same verification logic as verify_stripe_connect_account method
        is_verified = (
          retrieved_account.capabilities.get("transfers", "inactive") == "active"
          and retrieved_account.details_submitted
          and not retrieved_account.requirements.currently_due
        )

        # If not verified, verification is required
        verification_required = not is_verified

        logger.log_info(
          "StripeConnectService - create_stripe_connect_account",
          f"Verification check for account {account.id}: verification_required={verification_required}",
        )
      except Exception as e:
        logger.log_error(
          "StripeConnectService - create_stripe_connect_account",
          f"Error checking verification requirements: {str(e)}",
        )
        # If we can't check, assume verification might be needed
        verification_required = True

      return ServiceResult(
        success=True,
        data={
          "account_id": account.id,
          "customer_id": customer.id,
          "verification_required": verification_required,
        },
        message=AppMsg.STRIPE_CONNECT_CREATE_SUCCESS,
      )

    except stripe.error.StripeError as e:
      return ServiceResult(
        success=False,
        message=e.error.get("message", AppMsg.STRIPE_CONNECT_CREATE_FAILURE),
      )

    except Exception as e:
      logger.log_error("StripeConnectService - create_stripe_connect_account", e)

      return ServiceResult(
        success=False,
        message=AppMsg.STRIPE_CONNECT_CREATE_FAILURE,
      )

  @staticmethod
  def get_account_link(user: User, refresh: bool = True) -> ServiceResult:
    """
    Generate Stripe Connect account onboarding link
    """
    try:
      # Get the related Stripe Connect account model
      stripe_account_model = StripeConnectService._get_stripe_account(user)

      if not stripe_account_model or not stripe_account_model.account_id:
        return ServiceResult(
          success=False,
          message=AppMsg.STRIPE_CONNECT_NO_ACCOUNT,
        )

      # Create account link for onboarding
      link_type = "account_onboarding"

      # If this is a refresh (user returning to complete verification),
      # we might want to use account_update instead for existing accounts
      if refresh and stripe_account_model.details_submitted:
        link_type = "account_update"

      account_link = stripe.AccountLink.create(
        account=stripe_account_model.account_id,
        refresh_url=settings.STRIPE_CONNECT_REFRESH_URL,
        return_url=settings.STRIPE_CONNECT_RETURN_URL,
        type=link_type,
        collect="eventually_due",  # Only collect what's needed
      )

      return ServiceResult(
        success=True,
        data={"account_link_url": account_link.url, "link_type": link_type},
        message=AppMsg.STRIPE_CONNECT_GENERATE_LINK_SUCCESS,
      )

    except Exception as e:
      logger.log_error("StripeConnectService - get_account_link", e)
      return ServiceResult(success=False, message=AppMsg.STRIPE_CONNECT_GENERATE_LINK_FAILURE)

  # Modified verify_stripe_connect_account method
  @classmethod
  def verify_stripe_connect_account(cls, user):
    """Verify the status of a Stripe Connect account"""
    try:
      # Get the Stripe Connect account for the user
      stripe_account = StripeConnectAccount.objects.filter(user=user).first()

      if not stripe_account:
        return ServiceResult(
          success=False,
          message="No Stripe Connect account found for this user",
        )

      # Get the account from Stripe
      account = stripe.Account.retrieve(stripe_account.account_id)

      # Collect all types of requirements
      all_requirements = []
      if hasattr(account, "requirements"):
        if hasattr(account.requirements, "currently_due"):
          all_requirements.extend(account.requirements.currently_due or [])
        if hasattr(account.requirements, "eventually_due"):
          all_requirements.extend(account.requirements.eventually_due or [])
        if hasattr(account.requirements, "past_due"):
          all_requirements.extend(account.requirements.past_due or [])
        if hasattr(account.requirements, "pending_verification"):
          all_requirements.extend(account.requirements.pending_verification or [])

      # Check if the account is fully verified
      is_verified = (
        account.capabilities.get("transfers", "inactive") == "active"
        and account.details_submitted
        and not all_requirements  # No requirements of any type
      )

      # Update the verification status
      stripe_account.is_verified = is_verified
      stripe_account.details_submitted = account.details_submitted
      stripe_account.save()

      # Log detailed information about requirements
      logger.log_info(
        "StripeConnectService - verify_stripe_connect_account",
        f"Account verification for {account.id}: is_verified={is_verified}, "
        + f"currently_due={account.requirements.currently_due}, "
        + f"eventually_due={account.requirements.eventually_due}, "
        + f"past_due={account.requirements.past_due}, "
        + f"pending_verification={account.requirements.pending_verification}",
      )

      return ServiceResult(
        success=True,
        message="Account verification status retrieved successfully",
        data={
          "is_verified": is_verified,
          "details_submitted": account.details_submitted,
          "pending_requirements": all_requirements,
          "requirement_details": {
            "currently_due": account.requirements.currently_due or [],
            "eventually_due": account.requirements.eventually_due or [],
            "past_due": account.requirements.past_due or [],
            "pending_verification": account.requirements.pending_verification or [],
          },
        },
      )

    except Exception as e:
      AppLogger.error(f"Error verifying Stripe Connect account: {str(e)}")
      return ServiceResult(
        success=False,
        message=f"Error verifying Stripe Connect account: {str(e)}",
      )

  @classmethod
  def refresh_from_stripe(cls, user):
    """Refresh Stripe Connect account data from Stripe API"""
    try:
      # Get the Stripe Connect account for the user
      stripe_account = StripeConnectAccount.objects.filter(user=user).first()

      if not stripe_account:
        return ServiceResult(
          success=False,
          message="No Stripe Connect account found for this user",
        )

      # Get the account from Stripe
      account = stripe.Account.retrieve(stripe_account.account_id)

      # Update account information
      stripe_account.is_verified = (
        account.capabilities.get("transfers", "inactive") == "active"
        and account.details_submitted
        and not account.requirements.currently_due
      )
      stripe_account.details_submitted = account.details_submitted

      # Update display name if business profile is available
      if hasattr(account, "business_profile") and account.business_profile:
        if account.business_profile.get("name"):
          stripe_account.display_name = account.business_profile.get("name")

      # Update account type
      stripe_account.account_type = account.type

      # Update last verification check timestamp
      stripe_account.last_verification_check = timezone.now()

      # Save the updated model
      stripe_account.save()

      return ServiceResult(
        success=True,
        message="Stripe Connect account refreshed successfully",
        data={
          "account_id": stripe_account.account_id,
          "is_verified": stripe_account.is_verified,
          "details_submitted": stripe_account.details_submitted,
          "display_name": stripe_account.display_name,
          "account_type": stripe_account.account_type,
          "last_verification_check": stripe_account.last_verification_check,
        },
      )

    except Exception as e:
      AppLogger.error(f"Error refreshing Stripe Connect account: {str(e)}")
      return ServiceResult(
        success=False,
        message=f"Error refreshing Stripe Connect account: {str(e)}",
      )

  @staticmethod
  def _get_stripe_account(user: User) -> Optional[StripeConnectAccount]:
    """
    Get the Stripe Connect account for a user.

    This method first checks our database for an existing account. If no account is found,
    it attempts to find any accounts in Stripe's system that might be associated with this user
    but not properly recorded in our database.

    Args:
        user: The user to get the Stripe Connect account for

    Returns:
        The StripeConnectAccount object if found, otherwise None
    """
    try:
      # First, check our database
      stripe_account_model = StripeConnectAccount.objects.filter(user=user).first()
      if stripe_account_model:
        return stripe_account_model

      # If not found in our database, check if there's an account in Stripe's system
      # that might be associated with this user but not properly recorded in our database
      try:
        # Search for accounts with metadata.user_id matching our user's ID
        accounts = stripe.Account.list(limit=3)
        for account in accounts.data:
          if account.metadata and account.metadata.get("user_id") == str(user.id):
            logger.log_info(
              "StripeConnectService - _get_stripe_account",
              f"Found Stripe account {account.id} for user {user.id} in Stripe but not in our database",
            )

            # Create a record in our database for this account
            # Note: This is a minimal record, you might want to fetch more details from Stripe
            stripe_account_model = StripeConnectAccount.objects.create(
              user=user,
              account_id=account.id,
              # Set minimal required fields
              business_name=account.business_profile.name
              if hasattr(account, "business_profile") and hasattr(account.business_profile, "name")
              else "Unknown",
              business_type=account.business_type or "individual",
              business_address="",  # Default empty values for required fields
              business_email="",
              first_name="",
              last_name="",
              date_of_birth=timezone.now().date(),  # Default value
              email_address="",
              phone_number="",
              residential_address="",
              city="",
              state="",
              postal_code="",
              account_holder_name="",
              account_number="",
            )

            # Log that we've created a record
            logger.log_info(
              "StripeConnectService - _get_stripe_account",
              f"Created database record for existing Stripe account {account.id}",
            )

            return stripe_account_model
      except stripe.error.StripeError as e:
        logger.log_error(
          "StripeConnectService - _get_stripe_account",
          f"Error checking Stripe for existing accounts: {str(e)}",
        )

      # If we get here, no account was found in our database or in Stripe
      return None
    except Exception as e:
      logger.log_error(
        "StripeConnectService - _get_stripe_account",
        f"Error retrieving Stripe Connect account: {str(e)}",
      )
      return None

  @staticmethod
  def update_connect_account_status(account_id: str) -> ServiceResult:
    """
    Update the status of a Connect account
    """
    try:
      connect_account = StripeConnectAccount.objects.get(stripe_account_id=account_id)

      # Retrieve the account from Stripe
      account = stripe.Account.retrieve(account_id)

      # Update the account status
      connect_account.charges_enabled = account.charges_enabled
      connect_account.payouts_enabled = account.payouts_enabled
      connect_account.save()

      return ServiceResult(
        success=True,
        data=connect_account,
        message=AppMsg.CONNECT_ACCOUNT_UPDATED,
      )

    except StripeConnectAccount.DoesNotExist:
      return ServiceResult(success=False, message=AppMsg.CONNECT_ACCOUNT_NOT_FOUND)
    except Exception as e:
      logger.log_error("PaymentService - update_connect_account_status", e)
      return ServiceResult(success=False, message=AppMsg.CONNECT_ACCOUNT_UPDATE_FAILED)
