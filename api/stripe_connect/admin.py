from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import StripeConnectAccount


@admin.register(StripeConnectAccount)
class StripeConnectAccountAdmin(ModelAdmin):
  list_display = (
    "id",
    "user",
    "account_id",
    "display_name",
    "is_verified",
    "last_verification_check",
    "created_at",
  )
  search_fields = ("account_id", "display_name", "user__email")
  list_filter = ("is_verified", "details_submitted", "account_type", "created_at")
  readonly_fields = ("id", "created_at", "updated_at", "last_verification_check")
  ordering = ("-created_at",)
  date_hierarchy = "created_at"
  fieldsets = (
    (
      None,
      {
        "fields": (
          "id",
          "user",
          "created_at",
          "updated_at",
        )
      },
    ),
    (
      "Stripe Information",
      {
        "fields": (
          "account_id",
          "customer_id",
          "is_verified",
          "details_submitted",
        )
      },
    ),
    (
      "Display Information",
      {
        "fields": (
          "display_name",
          "account_type",
          "last_verification_check",
        )
      },
    ),
  )
