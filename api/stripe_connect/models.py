# models.py
from django.db import models
from django.utils.translation import gettext_lazy as _

from access.models import User
from core.models import AbstractModel


class StripeConnectAccount(AbstractModel):
  """
  Model to store essential Stripe Connect account information for users.
  This model only stores the minimum required information to link a user to their
  Stripe Connect account. All sensitive data is stored securely in Stripe's system.
  """

  user = models.OneToOneField(
    User,
    on_delete=models.CASCADE,
    related_name="stripe_connect_account",
    verbose_name=_("User"),
    unique=True,
  )

  account_id = models.CharField(
    _("Stripe Connect Account ID"),
    max_length=255,
    unique=True,
  )

  customer_id = models.CharField(_("Stripe Customer ID"), max_length=255, blank=True, null=True)

  # Account status
  is_verified = models.BooleanField(_("Account Verified"), default=False)
  details_submitted = models.BooleanField(_("Account Details Submitted"), default=True)

  # Metadata for display purposes (optional)
  display_name = models.CharField(
    _("Display Name"),
    max_length=255,
    blank=True,
    null=True,
    help_text=_("Name to display in the UI"),
  )
  account_type = models.CharField(
    _("Account Type"),
    max_length=50,
    blank=True,
    null=True,
    help_text=_("Type of Stripe account (individual, company, etc.)"),
  )

  # Last verification check
  last_verification_check = models.DateTimeField(
    _("Last Verification Check"), null=True, blank=True
  )

  def __str__(self):
    return f"{self.user.email} - {self.account_id}"

  class Meta:
    verbose_name = _("Stripe Connect Account")
    verbose_name_plural = _("Stripe Connect Accounts")
