from datetime import timedelta
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

BASE_DOMAIN = os.environ.get("BASE_DOMAIN", "http://localhost")

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-0uoc7tjfchxtnmxc-dju_nfpzt&(!9)8v10q0=_pd3^&@50q9@"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get("DEBUG", "True") == "True"
DEFAULT_CHARSET = "utf8"

ALLOWED_HOSTS = [
  host.strip()
  for host in os.environ.get(
    "ALLOWED_HOSTS", "localhost,127.0.0.1,web.rentie-backend-new.orb.local,web.rentie-drf.orb.local"
  ).split(",")
]

# CSRF settings
CSRF_TRUSTED_ORIGINS = [
  BASE_DOMAIN,
  "http://localhost",
  "http://127.0.0.1",
  "https://web.rentie-backend-new.orb.local",
  "https://web.rentie-drf.orb.local"
]


# Application definition

INSTALLED_APPS = [
  "unfold",
  "unfold.contrib.filters",
  "unfold.contrib.forms",
  "unfold.contrib.inlines",
  "unfold.contrib.import_export",
  "unfold.contrib.guardian",
  "unfold.contrib.simple_history",
  "django.contrib.admin",
  "django.contrib.auth",
  "django.contrib.contenttypes",
  "django.contrib.sessions",
  "django.contrib.messages",
  "django.contrib.staticfiles",
  "django.contrib.gis",
  "core.apps.CoreConfig",
  "access.apps.AccessConfig",
  "api.apps.ApiConfig",
  "rest_framework_simplejwt",
  "rest_framework_gis",
  "drf_spectacular",
  "api.interest",
  "api.activity",
  "channels",
  "ws.apps.WsConfig",
  "api.chat",
  "api.spark",
  "api.notification",
  "api.review",
  "api.session",
  "api.subscription",
  "api.stripe_connect",
  "api.invitation",
  "api.payment",
  "api.blocked_user",
  'csp',
  'admin_emails.apps.AdminEmailsConfig',
]

CSP_IMG_SRC = [
    "'self'",
    "*.openstreetmap.org",
    "*.tile.openstreetmap.org",
    'data:',
    '*'
]

CSP_CONNECT_SRC = [
    "'self'",
    "*.openstreetmap.org",
    "nominatim.openstreetmap.org",
    '*'
]

CSP_SCRIPT_SRC = [
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",
    '*'
]

CSP_STYLE_SRC = [
    "'self'",
    "'unsafe-inline'",
    '*'
]

MIDDLEWARE = [
  "django.middleware.security.SecurityMiddleware",
  "django.contrib.sessions.middleware.SessionMiddleware",
  "django.middleware.common.CommonMiddleware",
  "django.middleware.csrf.CsrfViewMiddleware",
  "django.contrib.auth.middleware.AuthenticationMiddleware",
  "django.contrib.messages.middleware.MessageMiddleware",
  "django.middleware.clickjacking.XFrameOptionsMiddleware",
  "whitenoise.middleware.WhiteNoiseMiddleware",
  "csp.middleware.CSPMiddleware",
]

ROOT_URLCONF = "rentie.urls"

TEMPLATES = [
  {
    "BACKEND": "django.template.backends.django.DjangoTemplates",
    "DIRS": [os.path.join(BASE_DIR, 'templates')],
    "APP_DIRS": True,
    "OPTIONS": {
      "context_processors": [
        "django.template.context_processors.debug",
        "django.template.context_processors.request",
        "django.contrib.auth.context_processors.auth",
        "django.contrib.messages.context_processors.messages",
      ],
    },
  },
]

WSGI_APPLICATION = "rentie.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Database Configuration
DATABASES = {
  "default": {
    "ENGINE": os.environ.get("DB_ENGINE", "django.contrib.gis.db.backends.postgis"),
    "NAME": os.environ.get("DB_NAME", BASE_DIR / "db.sqlite3"),
    "USER": os.environ.get("DB_USER", ""),
    "PASSWORD": os.environ.get("DB_PASSWORD", ""),
    "HOST": os.environ.get("DB_HOST", ""),
    "PORT": os.environ.get("DB_PORT", ""),
    "OPTIONS": {
      "client_encoding": "UTF8",
    },
  }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
  {
    "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
  },
  {
    "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
  },
  {
    "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
  },
  {
    "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
  },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
STATICFILES_DIRS = [
  os.path.join(BASE_DIR, "static"),
  "/app/static",
]

# WhiteNoise configuration for serving static files in production
STATICFILES_STORAGE = "whitenoise.storage.CompressedStaticFilesStorage"


# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")
STRIPE_PUBLISHABLE_KEY = os.environ.get(
  "STRIPE_PUBLISHABLE_KEY",
  "pk_test_51PLl5m06exVdJifUBYgbl7A4xB6OKMWf4tQuK8as1GV94wXruccIrqJZGOcqqXp4Fi6XGUBlwAaWw4lvWcZrX7Vm0005uURZRW",
)
STRIPE_SECRET_KEY = os.environ.get(
  "STRIPE_SECRET_KEY",
  "sk_test_51PLl5m06exVdJifUcCtb5Q2kg9wTY4mUYAWmoInAcf0wQCrLYjd9XCjbHfE6IxCSBnWVG81luHpQHj6LAIokVT5V00HJJtvINk",
)

STRIPE_CONNECT_RETURN_URL = f"{BASE_DOMAIN}/api/stripe-connect/me/onboarding-complete"
STRIPE_CONNECT_REFRESH_URL = f"{BASE_DOMAIN}/stripe/connect/retry"

# Twilio Configuration
TWILIO_ACCOUNT_SID = os.environ.get("TWILIO_ACCOUNT_SID", "")
TWILIO_AUTH_TOKEN = os.environ.get("TWILIO_AUTH_TOKEN", "")
TWILIO_FROM_NUMBER = os.environ.get("TWILIO_FROM_NUMBER", "")
TWILIO_VERIFY_SERVICE_ID = os.environ.get("TWILIO_VERIFY_SERVICE_ID", "")

# OTP Configuration
OTP_EXPIRY_MINUTES = int(os.environ.get("OTP_EXPIRY_MINUTES", "5"))
OTP_MAX_ATTEMPTS = int(os.environ.get("OTP_MAX_ATTEMPTS", "3"))
OTP_RESEND_INTERVAL_SECONDS = int(os.environ.get("OTP_RESEND_INTERVAL_SECONDS", "60"))
OTP_DAILY_LIMIT = int(os.environ.get("OTP_DAILY_LIMIT", "10"))

AUTH_USER_MODEL = "access.User"

REST_FRAMEWORK = {
  "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
  "DEFAULT_RENDERER_CLASSES": [
    "rest_framework.renderers.JSONRenderer",
  ],
  "DEFAULT_AUTHENTICATION_CLASSES": [
    "rest_framework_simplejwt.authentication.JWTAuthentication",
  ],
  "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
  "PAGE_SIZE": 10,
  "EXCEPTION_HANDLER": "core.utils.exception_handler.custom_exception_handler",
}


SPECTACULAR_SETTINGS = {
  "TITLE": "Ren-Tie API",
  "DESCRIPTION": "Ren-Tie API Documentation",
  "VERSION": "1.0.0",
  "SERVE_INCLUDE_SCHEMA": False,
}


# TODO: Change 60mins - for debug purpose only
SIMPLE_JWT = {
  "ACCESS_TOKEN_LIFETIME": timedelta(minutes=60),
  "REFRESH_TOKEN_LIFETIME": timedelta(days=30),
}

# Redis settings
REDIS_HOST = os.environ.get("REDIS_HOST", "127.0.0.1")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")


# Channels configuration
ASGI_APPLICATION = "rentie.asgi.application"

CHANNEL_LAYERS = {
  "default": {
    "BACKEND": "channels_redis.core.RedisChannelLayer",
    "CONFIG": {
      "hosts": [(REDIS_HOST, int(REDIS_PORT))],
    },
  },
}

# Redis Cache
CACHES = {
  "default": {
    "BACKEND": "django_redis.cache.RedisCache",
    "LOCATION": f"redis://{REDIS_HOST}:{REDIS_PORT}/1",
    "OPTIONS": {
      "CLIENT_CLASS": "django_redis.client.DefaultClient",
    },
  }
}

# Session - use cache
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"


# CORS settings
CORS_ALLOW_ALL_ORIGINS = True if DEBUG else False
CORS_ALLOWED_ORIGINS = [
  "http://localhost:3000",
  "http://127.0.0.1:3000",
]


STARTER_SPARK_NUMBER = 5
DISCOVERY_MAX_DISTANCE_KM = 1000
# TODO: What's the admin commission?
ADMIN_COMMISSION_PERCENTAGE = 5

FIREBASE_CREDENTIALS_PATH = os.path.join(BASE_DIR, "serviceAccountKey.json")

# Email Configuration
EMAIL_BACKEND = os.environ.get('EMAIL_BACKEND', 'django.core.mail.backends.smtp.EmailBackend')
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'mailhog')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 1025))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'False').lower() == 'true'
EMAIL_USE_SSL = os.environ.get('EMAIL_USE_SSL', 'False').lower() == 'true'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')
SERVER_EMAIL = os.environ.get('SERVER_EMAIL', '<EMAIL>')

# Site Settings for Email Templates
SITE_NAME = os.environ.get('SITE_NAME', 'Rentie')
SITE_URL = os.environ.get('SITE_URL', 'https://rentie.com')
SUPPORT_EMAIL = os.environ.get('SUPPORT_EMAIL', '<EMAIL>')

# Celery Configuration for Email Tasks
CELERY_BROKER_URL = f'redis://{REDIS_HOST}:{REDIS_PORT}/0'
CELERY_RESULT_BACKEND = f'redis://{REDIS_HOST}:{REDIS_PORT}/0'
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Celery Beat Schedule
from celery.schedules import crontab

CELERY_BEAT_SCHEDULE = {
    # Add your Celery beat tasks here
}
