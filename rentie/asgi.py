"""
ASGI config for rentie project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "rentie.settings")
django.setup()

# Import these AFTER django.setup() to avoid AppRegistryNotReady errors
from channels.auth import AuthMiddlewareStack  # noqa: E402
from django.core.asgi import get_asgi_application  # noqa: E402
from channels.routing import ProtocolTypeRouter, URLRouter  # noqa: E402
from ws.middleware import JwtAuthMiddleware  # noqa: E402


# TODO: Make it better
# Create a function to avoid top-level imports of models
def get_application():
  from ws import routing  # Import here AFTER django setup

  http_application = get_asgi_application()

  return ProtocolTypeRouter(
    {
      "http": http_application,
      "websocket": JwtAuthMiddleware(AuthMiddlewareStack(URLRouter(routing.websocket_urlpatterns))),
    }
  )


# Get the application instance
application = get_application()
