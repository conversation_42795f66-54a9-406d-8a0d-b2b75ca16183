import os
import django
from django.core.management.base import BaseCommand
from dotenv import load_dotenv


# Load .env file
load_dotenv()

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "rentie.settings")
django.setup()

from api.subscription.models import SubscriptionPlan  # noqa: E402


def create_subscription_plans():
  """
  Create or update subscription plans with Stripe product and price IDs from env variables.

  Expected .env variables:
  STRIPE_12M_PRODUCT_ID=prod_xxxxx
  STRIPE_12M_PRICE_ID=price_xxxxx
  STRIPE_6M_PRODUCT_ID=prod_xxxxx
  STRIPE_6M_PRICE_ID=price_xxxxx
  STRIPE_3M_PRODUCT_ID=prod_xxxxx
  STRIPE_3M_PRICE_ID=price_xxxxx
  STRIPE_1M_PRODUCT_ID=prod_xxxxx
  STRIPE_1M_PRICE_ID=price_xxxxx
  """

  # 12-month plan (Active)
  SubscriptionPlan.objects.update_or_create(
    name="Ren-Tie Premium for 12 Months",
    defaults={
      "duration_months": 12,
      "price": 14.99,
      "currency": "EUR",
      "stripe_product_id": os.environ.get("STRIPE_12M_PRODUCT_ID"),
      "stripe_price_id": os.environ.get("STRIPE_12M_PRICE_ID"),
    },
  )
  print("✅ Created/Updated 12-month subscription plan")

  # 6-month plan (Coming Soon)
  SubscriptionPlan.objects.update_or_create(
    name="Ren-Tie Premium for 6 Months",
    defaults={
      "duration_months": 6,
      "price": 10.99,
      "currency": "EUR",
      "stripe_product_id": os.environ.get("STRIPE_6M_PRODUCT_ID"),
      "stripe_price_id": os.environ.get("STRIPE_6M_PRICE_ID"),
    },
  )
  print("✅ Created/Updated 6-month subscription plan")

  # 3-month plan (Coming Soon)
  SubscriptionPlan.objects.update_or_create(
    name="Ren-Tie Premium for 3 Months",
    defaults={
      "duration_months": 3,
      "price": 7.99,
      "currency": "EUR",
      "stripe_product_id": os.environ.get("STRIPE_3M_PRODUCT_ID"),
      "stripe_price_id": os.environ.get("STRIPE_3M_PRICE_ID"),
    },
  )
  print("✅ Created/Updated 3-month subscription plan")

  # 1-month plan (Coming Soon)
  SubscriptionPlan.objects.update_or_create(
    name="Ren-Tie Premium for 1 Month",
    defaults={
      "duration_months": 1,
      "price": 3.99,
      "currency": "EUR",
      "stripe_product_id": os.environ.get("STRIPE_1M_PRODUCT_ID"),
      "stripe_price_id": os.environ.get("STRIPE_1M_PRICE_ID"),
    },
  )
  print("✅ Created/Updated 1-month subscription plan")

  print("✨ All subscription plans have been created or updated!")


class Command(BaseCommand):
  help = "Create or update subscription plans with Stripe product and price IDs"

  def handle(self, *args, **kwargs):
    create_subscription_plans()
    self.stdout.write(self.style.SUCCESS("Successfully created subscription plans"))


# Execute when run directly
if __name__ == "__main__":
  create_subscription_plans()
