#!/usr/bin/env python
import os
import django
from django.contrib.auth import get_user_model

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "rentie.settings")
django.setup()

User = get_user_model()

email = os.environ.get("DJANGO_SUPERUSER_EMAIL", "<EMAIL>")
password = os.environ.get("DJANGO_SUPERUSER_PASSWORD", "admin")

# Create superuser if one doesn't exist with this username
if not User.objects.filter(email=email).exists():
  print(f"Creating superuser: {email}")
  User.objects.create_superuser(email=email, password=password)
  print("Superuser created successfully!")
else:
  print(f"Superuser {email} already exists")
