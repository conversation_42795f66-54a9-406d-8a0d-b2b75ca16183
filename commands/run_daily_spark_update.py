#!/usr/bin/env python
import os
import django
from django.utils import timezone


# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "rentie.settings")
django.setup()

from access.models import User  # noqa: E402
from api.subscription.models import Subscription  # noqa: E402


def run_spark_update():
  current_time = timezone.now()
  print(f"[{current_time}] Running daily spark update...")

  free_tier_increment = int(os.environ.get("DAILY_FREE_TIER_SPARK_INCREMENT", 3))
  premium_tier_increment = int(os.environ.get("DAILY_PREMIUM_TIER_SPARK_INCREMENT", 10))

  free_tier_limit = int(os.environ.get("FREE_TIER_SPARK_LIMIT", 50))
  premium_tier_limit = int(os.environ.get("PREMIUM_TIER_LIMIT", 100))

  users = User.objects.filter(is_active=True, is_admin=False)
  updated_users_count = 0

  for user in users:
    has_active_subscription = Subscription.objects.filter(
      user=user,
      status="active",
      current_period_start__lte=current_time,
      current_period_end__gte=current_time,
    ).exists()

    increment = premium_tier_increment if has_active_subscription else free_tier_increment
    max_limit = premium_tier_limit if has_active_subscription else free_tier_limit

    # Add increment first
    user.sparks_number += increment

    # Enforce max cap
    if user.sparks_number > max_limit:
      user.sparks_number = max_limit

    user.last_sparks_added_date = current_time
    user.save(update_fields=["sparks_number", "last_sparks_added_date"])
    updated_users_count += 1

  print(f"✅ Successfully updated sparks for {updated_users_count} users")


if __name__ == "__main__":
  run_spark_update()
