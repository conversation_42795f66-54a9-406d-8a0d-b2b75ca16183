# Ren-Tie

A Django DRF (Django Rest Framework) Ren-Tie backend project.

## Overview

Ren-Tie backend uses Django and Django Rest Framework. This README provides instructions for setting up the development environment and maintaining code quality.

## Requirements

- Docker
- Docker Compose
- Visual Studio Code (recommended)
- `.env` file (contact the maintainer to get the proper file)

## Installation

### First-time setup

1. Clone this repository:
   ```bash
   <NAME_EMAIL>:mbo<PERSON>niewicz/rentie_drf.git
   cd rentie_drf
   ```

2. Obtain the required `.env` file:
   - Contact the project maintainer to get the proper `.env` file with the necessary environment variables
   - Place the `.env` file in the root directory of the project
   - **Important**: Never commit the `.env` file to version control as it contains sensitive information

3. Build and start the containers (use this command whenever you make changes to the Docker configuration):
   ```bash
   docker-compose up --build
   ```

4. The application should now be running at `http://localhost:80`

### Subsequent runs

After the initial setup, you can start the application with:

```bash
docker-compose up
```

To run in detached mode (background):

```bash
docker-compose up -d
```

### Stopping the application

```bash
docker-compose down
```

### Common Tasks

- **Create migrations**:
  ```bash
  docker-compose exec web python manage.py makemigrations
  ```

- **Apply migrations**:
  ```bash
  docker-compose exec web python manage.py migrate
  ```

- **Access Django shell**:
  ```bash
  docker-compose exec web python manage.py shell
  ```

## Code Quality

### Linting with Ruff

This project uses [Ruff](https://github.com/charliermarsh/ruff) for linting, a fast Python linter written in Rust.

#### VS Code Setup

1. Install the Ruff extension for VS Code:
   - Open VS Code
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "Ruff"
   - Install the "Ruff" extension by Astral Software

2. Configure the extension to run on save:
   - Open Settings (Ctrl+,)
   - Search for "ruff"
   - Ensure "Format On Save" is checked

#### Ruff Configuration

Ruff configuration is stored in the `pyproject.toml` file at the root of the project.
