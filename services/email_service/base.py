from abc import ABC, abstractmethod
from typing import List, Optional, Dict, <PERSON><PERSON>, Any
from dataclasses import dataclass


@dataclass
class EmailAttachment:
    """Simple attachment data class"""
    filename: str
    content: bytes
    content_type: str = 'application/octet-stream'


class BaseEmailService(ABC):
    """
    Abstract base class for email service implementations.
    This allows for easy switching between different email providers.
    """

    @abstractmethod
    def send_email(
        self,
        to_emails: List[str],
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        from_email: Optional[str] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None,
        reply_to: Optional[str] = None,
        attachments: Optional[List[EmailAttachment]] = None
    ) -> Tuple[bool, str]:
        """
        Send an email to the specified recipients.

        Args:
            to_emails: List of recipient email addresses
            subject: Email subject line
            html_content: HTML content of the email
            text_content: Optional plain text version of the email
            from_email: Optional sender email (uses default if not provided)
            cc_emails: Optional list of CC recipients
            bcc_emails: Optional list of BCC recipients
            reply_to: Optional reply-to email address
            attachments: Optional list of file attachments

        Returns:
            Tuple of (success: bool, message: str)
            - success: Whether the email was sent successfully
            - message: Success message or error description
        """
        pass

    @abstractmethod
    def send_templated_email(
        self,
        to_emails: List[str],
        template_name: str,
        context: Dict[str, Any],
        from_email: Optional[str] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None,
        reply_to: Optional[str] = None,
        attachments: Optional[List[EmailAttachment]] = None
    ) -> Tuple[bool, str]:
        """
        Send an email using a predefined template.

        Args:
            to_emails: List of recipient email addresses
            template_name: Name of the email template to use
            context: Dictionary of variables to render in the template
            from_email: Optional sender email (uses default if not provided)
            cc_emails: Optional list of CC recipients
            bcc_emails: Optional list of BCC recipients
            reply_to: Optional reply-to email address
            attachments: Optional list of file attachments

        Returns:
            Tuple of (success: bool, message: str)
        """
        pass

    @abstractmethod
    def send_otp_email(
        self,
        email: str,
        otp_code: str,
        purpose: str = "verification"
    ) -> Tuple[bool, str]:
        """
        Send an OTP code via email.

        Args:
            email: Recipient's email address
            otp_code: The OTP code to send
            purpose: Purpose of the OTP (e.g., "verification", "password_reset")

        Returns:
            Tuple of (success: bool, message: str)
        """
        pass

    @abstractmethod
    def is_configured(self) -> bool:
        """
        Check if the email service is properly configured.

        Returns:
            True if the service has all required configuration, False otherwise
        """
        pass

    def validate_email(self, email: str) -> bool:
        """
        Basic email validation.

        Args:
            email: Email address to validate

        Returns:
            True if email appears valid, False otherwise
        """
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    def validate_emails(self, emails: List[str]) -> Tuple[bool, Optional[str]]:
        """
        Validate a list of email addresses.

        Args:
            emails: List of email addresses to validate

        Returns:
            Tuple of (all_valid: bool, first_invalid_email: Optional[str])
        """
        for email in emails:
            if not self.validate_email(email):
                return False, email
        return True, None
