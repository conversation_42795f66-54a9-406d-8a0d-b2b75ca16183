import logging
import threading
from typing import List, Optional, Dict, Tuple, Any
from email.mime.base import MIMEBase
from email import encoders

from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from .base import BaseEmailService, EmailAttachment

logger = logging.getLogger(__name__)


class SMTPEmailService(BaseEmailService):
    """
    SMTP-based email service implementation using Django's email backend.
    This provides a simple interface for sending emails without the complexity
    of queuing, models, or database storage.
    """

    def __init__(self):
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
        self.email_backend = getattr(settings, 'EMAIL_BACKEND', 'django.core.mail.backends.smtp.EmailBackend')

        # Template settings
        self.template_prefix = getattr(settings, 'EMAIL_TEMPLATE_PREFIX', 'emails/')
        self.otp_template = getattr(settings, 'EMAIL_OTP_TEMPLATE', 'otp_email.html')

        # Load common templates
        self.templates = {
            'welcome': 'welcome.html',
            'password_reset': 'password_reset.html',
            'email_verification': 'email_verification.html',
            'otp': self.otp_template,
        }

    def send_email(
        self,
        to_emails: List[str],
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        from_email: Optional[str] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None,
        reply_to: Optional[str] = None,
        attachments: Optional[List[EmailAttachment]] = None
    ) -> Tuple[bool, str]:
        """
        Send an email to the specified recipients.
        """
        try:
            # Validate recipients
            valid, invalid_email = self.validate_emails(to_emails)
            if not valid:
                return False, f"Invalid email address: {invalid_email}"

            # If no text content provided, strip HTML tags
            if text_content is None:
                text_content = strip_tags(html_content)

            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=from_email or self.from_email,
                to=to_emails,
                cc=cc_emails,
                bcc=bcc_emails,
                reply_to=[reply_to] if reply_to else None
            )

            # Attach HTML version
            email.attach_alternative(html_content, "text/html")

            # Add attachments if any
            if attachments:
                for attachment in attachments:
                    email.attach(
                        attachment.filename,
                        attachment.content,
                        attachment.content_type
                    )

            # Send email
            email.send(fail_silently=False)

            logger.info(f"Email sent successfully to {', '.join(to_emails)}")
            return True, "Email sent successfully"

        except Exception as e:
            error_msg = f"Failed to send email: {str(e)}"
            logger.error(error_msg)

            # In debug mode, log but return success for testing
            if settings.DEBUG:
                logger.warning(f"DEBUG MODE: Email would have been sent to {', '.join(to_emails)}")
                return True, "Email sent successfully (DEBUG MODE)"

            return False, error_msg

    def send_templated_email(
        self,
        to_emails: List[str],
        template_name: str,
        context: Dict[str, Any],
        from_email: Optional[str] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None,
        reply_to: Optional[str] = None,
        attachments: Optional[List[EmailAttachment]] = None
    ) -> Tuple[bool, str]:
        """
        Send an email using a predefined template.
        """
        try:
            # Get template path
            if template_name in self.templates:
                template_path = f"{self.template_prefix}{self.templates[template_name]}"
            else:
                template_path = f"{self.template_prefix}{template_name}"

            # Add common context variables
            context.update({
                'site_name': getattr(settings, 'SITE_NAME', 'Rentie'),
                'site_url': getattr(settings, 'SITE_URL', 'https://rentie.com'),
                'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
            })

            # Render HTML content
            try:
                html_content = render_to_string(template_path, context)
            except Exception as e:
                # Fallback to simple HTML if template not found
                logger.warning(f"Template {template_path} not found, using fallback")
                html_content = self._get_fallback_html(template_name, context)

            # Extract subject from context or use default
            subject = context.get('subject', f"Message from {context.get('site_name', 'Rentie')}")

            return self.send_email(
                to_emails=to_emails,
                subject=subject,
                html_content=html_content,
                from_email=from_email,
                cc_emails=cc_emails,
                bcc_emails=bcc_emails,
                reply_to=reply_to,
                attachments=attachments
            )

        except Exception as e:
            error_msg = f"Failed to send templated email: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def send_otp_email(
        self,
        email: str,
        otp_code: str,
        purpose: str = "verification"
    ) -> Tuple[bool, str]:
        """
        Send an OTP code via email.
        """
        purpose_subjects = {
            'verification': 'Your Verification Code',
            'password_reset': 'Password Reset Code',
            'login': 'Your Login Code',
        }

        subject = purpose_subjects.get(purpose, 'Your Verification Code')

        context = {
            'subject': subject,
            'otp_code': otp_code,
            'purpose': purpose,
            'expiry_minutes': getattr(settings, 'OTP_EXPIRY_MINUTES', 5),
        }

        # Try to use template first
        success, message = self.send_templated_email(
            to_emails=[email],
            template_name='otp',
            context=context
        )

        # If template fails, send simple email
        if not success:
            html_content = f"""
            <html>
                <body>
                    <h2>{subject}</h2>
                    <p>Your verification code is:</p>
                    <h1 style="font-size: 32px; letter-spacing: 5px; color: #333;">{otp_code}</h1>
                    <p>This code will expire in {context['expiry_minutes']} minutes.</p>
                    <p>If you didn't request this code, please ignore this email.</p>
                </body>
            </html>
            """

            return self.send_email(
                to_emails=[email],
                subject=subject,
                html_content=html_content
            )

        return success, message

    def is_configured(self) -> bool:
        """
        Check if the email service is properly configured.
        """
        # For Mailhog in development, we just need the host and port
        if settings.DEBUG:
            return bool(settings.EMAIL_HOST and settings.EMAIL_PORT)

        # For production, check if we have proper SMTP settings
        if self.email_backend == 'django.core.mail.backends.smtp.EmailBackend':
            return bool(
                settings.EMAIL_HOST and
                settings.EMAIL_PORT and
                (not settings.EMAIL_USE_TLS or (settings.EMAIL_HOST_USER and settings.EMAIL_HOST_PASSWORD))
            )

        # For other backends (console, file, etc.), assume they're configured
        return True

    def _get_fallback_html(self, template_name: str, context: Dict[str, Any]) -> str:
        """
        Generate fallback HTML content when template is not found.
        """
        site_name = context.get('site_name', 'Rentie')

        if template_name == 'welcome':
            return f"""
            <html>
                <body>
                    <h1>Welcome to {site_name}!</h1>
                    <p>Thank you for joining us. We're excited to have you on board.</p>
                    <p>If you have any questions, feel free to contact us.</p>
                </body>
            </html>
            """
        elif template_name == 'password_reset':
            reset_link = context.get('reset_link', '#')
            return f"""
            <html>
                <body>
                    <h1>Password Reset Request</h1>
                    <p>You requested a password reset. Click the link below to reset your password:</p>
                    <p><a href="{reset_link}">Reset Password</a></p>
                    <p>If you didn't request this, please ignore this email.</p>
                </body>
            </html>
            """
        else:
            # Generic fallback
            message = context.get('message', 'You have a new notification.')
            return f"""
            <html>
                <body>
                    <h1>{site_name}</h1>
                    <p>{message}</p>
                </body>
            </html>
            """


# Singleton pattern for email service
_email_service = None
_lock = threading.Lock()


def get_email_service() -> SMTPEmailService:
    """
    Get the singleton instance of the email service.
    """
    global _email_service
    if _email_service is None:
        with _lock:
            if _email_service is None:
                _email_service = SMTPEmailService()
    return _email_service
