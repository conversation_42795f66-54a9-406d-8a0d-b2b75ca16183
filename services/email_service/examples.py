"""
Examples of using the simple email service.

This demonstrates how to use the email service without the complexity
of models, queues, or database storage.
"""

from rentie_drf.services.email_service import get_email_service, EmailAttachment


def send_simple_email_example():
    """Example: Send a simple email"""
    email_service = get_email_service()

    success, message = email_service.send_email(
        to_emails=['<EMAIL>'],
        subject='Welcome to Rentie!',
        html_content="""
        <html>
            <body>
                <h1>Welcome!</h1>
                <p>Thank you for joining <PERSON><PERSON>.</p>
                <p>We're excited to have you on board.</p>
            </body>
        </html>
        """
    )

    if success:
        print("Email sent successfully!")
    else:
        print(f"Failed to send email: {message}")


def send_email_with_cc_and_bcc_example():
    """Example: Send email with CC and BCC recipients"""
    email_service = get_email_service()

    success, message = email_service.send_email(
        to_emails=['<EMAIL>'],
        cc_emails=['<EMAIL>'],
        bcc_emails=['<EMAIL>'],
        subject='Important Update',
        html_content='<p>This is an important update for all recipients.</p>',
        reply_to='<EMAIL>'
    )

    return success, message


def send_email_with_attachment_example():
    """Example: Send email with attachments"""
    email_service = get_email_service()

    # Create attachment from file content
    with open('path/to/document.pdf', 'rb') as f:
        pdf_content = f.read()

    attachment = EmailAttachment(
        filename='rental_agreement.pdf',
        content=pdf_content,
        content_type='application/pdf'
    )

    success, message = email_service.send_email(
        to_emails=['<EMAIL>'],
        subject='Your Rental Agreement',
        html_content="""
        <html>
            <body>
                <h2>Rental Agreement</h2>
                <p>Please find your rental agreement attached.</p>
                <p>If you have any questions, feel free to contact us.</p>
            </body>
        </html>
        """,
        attachments=[attachment]
    )

    return success, message


def send_otp_email_example():
    """Example: Send OTP verification email"""
    email_service = get_email_service()

    # For email verification
    success, message = email_service.send_otp_email(
        email='<EMAIL>',
        otp_code='1234',
        purpose='verification'
    )

    # For password reset
    success, message = email_service.send_otp_email(
        email='<EMAIL>',
        otp_code='5678',
        purpose='password_reset'
    )

    return success, message


def send_templated_email_example():
    """Example: Send email using templates"""
    email_service = get_email_service()

    # Welcome email
    success, message = email_service.send_templated_email(
        to_emails=['<EMAIL>'],
        template_name='welcome',
        context={
            'user_name': 'John Doe',
            'activation_link': 'https://rentie.com/activate/abc123',
        }
    )

    # Password reset email
    success, message = email_service.send_templated_email(
        to_emails=['<EMAIL>'],
        template_name='password_reset',
        context={
            'user_name': 'Jane Smith',
            'reset_link': 'https://rentie.com/reset-password/xyz789',
            'expiry_hours': 24,
        }
    )

    return success, message


def send_custom_template_email_example():
    """Example: Send email with custom template"""
    email_service = get_email_service()

    # Using a custom template not in the predefined list
    success, message = email_service.send_templated_email(
        to_emails=['<EMAIL>'],
        template_name='rental_application_received.html',  # Custom template
        context={
            'landlord_name': 'Mr. Johnson',
            'property_address': '123 Main St, Apt 4B',
            'applicant_name': 'Sarah Connor',
            'application_date': '2025-01-13',
            'review_link': 'https://rentie.com/applications/123',
        }
    )

    return success, message


def check_email_configuration():
    """Example: Check if email service is configured"""
    email_service = get_email_service()

    if email_service.is_configured():
        print("Email service is properly configured!")

        # In development with Mailhog
        print("You can view sent emails at: http://localhost:8025")
    else:
        print("Email service is not configured.")
        print("Please check your EMAIL_* settings in settings.py")


def validate_email_addresses_example():
    """Example: Validate email addresses before sending"""
    email_service = get_email_service()

    emails_to_validate = [
        '<EMAIL>',
        'invalid-email',
        '<EMAIL>'
    ]

    # Validate single email
    if email_service.validate_email('<EMAIL>'):
        print("Email is valid")

    # Validate multiple emails
    valid, invalid_email = email_service.validate_emails(emails_to_validate)
    if not valid:
        print(f"Invalid email found: {invalid_email}")


# Django view example
from django.http import JsonResponse
from django.views import View

class SendWelcomeEmailView(View):
    """Example Django view using the email service"""

    def post(self, request):
        email = request.POST.get('email')
        name = request.POST.get('name', 'User')

        if not email:
            return JsonResponse({'error': 'Email is required'}, status=400)

        email_service = get_email_service()

        # Send welcome email
        success, message = email_service.send_templated_email(
            to_emails=[email],
            template_name='welcome',
            context={
                'user_name': name,
                'subject': 'Welcome to Rentie!',
            }
        )

        if success:
            return JsonResponse({'message': 'Welcome email sent successfully'})
        else:
            return JsonResponse({'error': message}, status=500)


# Using in Django models
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver

class UserProfile(models.Model):
    user = models.OneToOneField('auth.User', on_delete=models.CASCADE)
    email_verified = models.BooleanField(default=False)

    def send_verification_email(self):
        """Send email verification"""
        email_service = get_email_service()

        # Generate OTP (in real app, use a proper OTP generator)
        import random
        otp = str(random.randint(1000, 9999))

        # Store OTP in cache (similar to SMS service)
        from django.core.cache import cache
        cache.set(f'email_otp:{self.user.email}', otp, timeout=300)  # 5 minutes

        # Send OTP email
        success, message = email_service.send_otp_email(
            email=self.user.email,
            otp_code=otp,
            purpose='verification'
        )

        return success, message


@receiver(post_save, sender='auth.User')
def send_welcome_email_on_user_creation(sender, instance, created, **kwargs):
    """Automatically send welcome email when user is created"""
    if created and instance.email:
        email_service = get_email_service()
        email_service.send_templated_email(
            to_emails=[instance.email],
            template_name='welcome',
            context={
                'user_name': instance.get_full_name() or instance.username,
            }
        )


# Celery task example (if using Celery)
from celery import shared_task

@shared_task
def send_email_async(to_emails, subject, html_content, **kwargs):
    """Send email asynchronously using Celery"""
    email_service = get_email_service()
    return email_service.send_email(
        to_emails=to_emails,
        subject=subject,
        html_content=html_content,
        **kwargs
    )


# Usage in API views
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

class ResendVerificationEmailAPIView(APIView):
    """API endpoint to resend verification email"""

    def post(self, request):
        user = request.user

        if not user.is_authenticated:
            return Response(
                {'error': 'Authentication required'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        if not user.email:
            return Response(
                {'error': 'No email address associated with this account'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Send verification email
        email_service = get_email_service()
        success, message = email_service.send_otp_email(
            email=user.email,
            otp_code='1234',  # In production, generate proper OTP
            purpose='verification'
        )

        if success:
            return Response({'message': 'Verification email sent'})
        else:
            return Response(
                {'error': 'Failed to send email. Please try again later.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
