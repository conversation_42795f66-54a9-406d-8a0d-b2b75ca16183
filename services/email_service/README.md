# Simple Email Service

A lightweight email service for Django applications that provides a simple interface for sending emails without the complexity of database models, queuing, or background tasks.

## Why Use This Instead of the Mailer App?

The `mailer` app is a full-featured email system with:
- Database models for providers, templates, queues, and logs
- Background task processing with Celery
- Email provider switching and failover
- Rate limiting and retry logic
- Full admin interface

**This simple email service is better when you:**
- Want to send emails immediately without queuing
- Don't need to store email history in the database
- Want minimal setup and configuration
- Prefer simplicity over advanced features
- Are building a small to medium-sized application

## Features

- ✅ Simple API similar to the SMS service
- ✅ Support for HTML and plain text emails
- ✅ Email attachments
- ✅ CC, BCC, and Reply-To support
- ✅ Built-in OTP email functionality
- ✅ Template support (optional)
- ✅ Email validation
- ✅ Works with <PERSON>jan<PERSON>'s email backends
- ✅ Singleton pattern for easy access
- ✅ Debug mode support with Mailhog

## Installation

No installation needed! The service uses Django's built-in email functionality.

## Configuration

Add these settings to your `settings.py`:

```python
# Basic email configuration (already in your settings)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'mailhog'  # or your SMTP server
EMAIL_PORT = 1025  # or your SMTP port
EMAIL_USE_TLS = False
EMAIL_USE_SSL = False
EMAIL_HOST_USER = ''  # if authentication required
EMAIL_HOST_PASSWORD = ''  # if authentication required

# Email service settings
DEFAULT_FROM_EMAIL = '<EMAIL>'
SITE_NAME = 'Rentie'
SITE_URL = 'https://rentie.com'
SUPPORT_EMAIL = '<EMAIL>'

# Optional: OTP settings
OTP_EXPIRY_MINUTES = 5
```

## Quick Start

```python
from services.email_service import get_email_service

# Get the email service instance
email_service = get_email_service()

# Send a simple email
success, message = email_service.send_email(
    to_emails=['<EMAIL>'],
    subject='Hello from Rentie',
    html_content='<h1>Welcome!</h1><p>Thanks for joining us.</p>'
)

if success:
    print("Email sent successfully!")
else:
    print(f"Failed: {message}")
```

## Usage Examples

### 1. Send Simple Email

```python
email_service = get_email_service()

success, message = email_service.send_email(
    to_emails=['<EMAIL>'],
    subject='Your Rental Application Status',
    html_content='''
        <h2>Application Approved!</h2>
        <p>Congratulations! Your rental application has been approved.</p>
        <p>Next steps: Sign the lease agreement and pay the security deposit.</p>
    '''
)
```

### 2. Send Email with Attachments

```python
from services.email_service import EmailAttachment

# Create attachment
attachment = EmailAttachment(
    filename='lease_agreement.pdf',
    content=pdf_bytes,  # Your PDF content as bytes
    content_type='application/pdf'
)

success, message = email_service.send_email(
    to_emails=['<EMAIL>'],
    subject='Your Lease Agreement',
    html_content='<p>Please find your lease agreement attached.</p>',
    attachments=[attachment]
)
```

### 3. Send OTP Email

```python
# Send OTP for email verification
success, message = email_service.send_otp_email(
    email='<EMAIL>',
    otp_code='1234',
    purpose='verification'  # or 'password_reset', 'login'
)
```

### 4. Send Templated Email

Create a template in `templates/emails/notification.html`, then:

```python
success, message = email_service.send_templated_email(
    to_emails=['<EMAIL>'],
    template_name='notification.html',
    context={
        'user_name': 'John Doe',
        'message': 'Your rent payment has been received.',
        'amount': '$1,500',
        'date': '2025-01-13'
    }
)
```

### 5. Send Email with CC and BCC

```python
success, message = email_service.send_email(
    to_emails=['<EMAIL>'],
    cc_emails=['<EMAIL>'],
    bcc_emails=['<EMAIL>'],
    subject='Maintenance Request Update',
    html_content='<p>Your maintenance request has been scheduled.</p>',
    reply_to='<EMAIL>'
)
```

## Using in Django Views

```python
from django.http import JsonResponse
from services.email_service import get_email_service

def send_welcome_email(request):
    email = request.POST.get('email')
    name = request.POST.get('name', 'User')

    email_service = get_email_service()

    success, message = email_service.send_templated_email(
        to_emails=[email],
        template_name='welcome',
        context={
            'user_name': name,
            'activation_link': f'https://rentie.com/activate/{user.activation_token}'
        }
    )

    if success:
        return JsonResponse({'status': 'success'})
    else:
        return JsonResponse({'status': 'error', 'message': message})
```

## Using in Models

```python
from django.db import models
from services.email_service import get_email_service

class RentalApplication(models.Model):
    # ... your fields ...

    def send_status_update_email(self):
        email_service = get_email_service()

        return email_service.send_email(
            to_emails=[self.applicant_email],
            subject=f'Application Update - {self.property.address}',
            html_content=f'''
                <h2>Application Status: {self.get_status_display()}</h2>
                <p>Your application for {self.property.address} has been updated.</p>
            '''
        )
```

## Template Structure

Templates should be placed in `templates/emails/`. The service looks for:
- `welcome.html` - Welcome emails
- `password_reset.html` - Password reset emails
- `email_verification.html` - Email verification
- `otp_email.html` - OTP emails

You can also use custom template names:
```python
email_service.send_templated_email(
    to_emails=['<EMAIL>'],
    template_name='custom_notification.html',  # Your custom template
    context={'key': 'value'}
)
```

## Debug Mode

In development (DEBUG=True), emails will be sent to Mailhog if configured. You can view all sent emails at:
```
http://localhost:8025
```

## Comparison: Simple Email Service vs Mailer App

| Feature | Simple Email Service | Mailer App |
|---------|---------------------|------------|
| Setup Complexity | ✅ Very Simple | ❌ Complex (models, migrations) |
| Database Required | ✅ No | ❌ Yes |
| Immediate Sending | ✅ Yes | ❌ Queued |
| Background Tasks | ✅ Not needed | ❌ Requires Celery |
| Email History | ❌ No | ✅ Full logs |
| Retry Failed Emails | ❌ No | ✅ Automatic |
| Multiple Providers | ❌ No | ✅ Yes |
| Rate Limiting | ❌ No | ✅ Yes |
| Admin Interface | ❌ No | ✅ Full admin |
| Template Management | ✅ File-based | ✅ Database |
| Best For | Small-Medium Apps | Large/Enterprise Apps |

## Testing

```python
# Check if email service is configured
email_service = get_email_service()
if email_service.is_configured():
    print("Email service is ready!")

# Validate email addresses
if email_service.validate_email('<EMAIL>'):
    print("Valid email!")

# Validate multiple emails
valid, invalid = email_service.validate_emails(['<EMAIL>', 'invalid-email'])
if not valid:
    print(f"Invalid email: {invalid}")
```

## Extending the Service

You can create custom email providers by extending the base class:

```python
from services.email_service.base import BaseEmailService

class SendGridEmailService(BaseEmailService):
    def send_email(self, to_emails, subject, html_content, **kwargs):
        # Your SendGrid implementation
        pass
```

## Error Handling

The service returns a tuple of (success: bool, message: str):

```python
success, message = email_service.send_email(...)

if not success:
    # Log the error
    logger.error(f"Email failed: {message}")

    # Handle the error appropriately
    if "Invalid email" in message:
        return HttpResponseBadRequest("Invalid email address")
    else:
        return HttpResponseServerError("Email service unavailable")
```

## Tips

1. **Use templates for consistency** - Create reusable HTML templates
2. **Always provide text content** - Some email clients prefer plain text
3. **Validate emails** - Use the built-in validation before sending
4. **Handle errors gracefully** - Always check the success status
5. **Test with Mailhog** - Great for development testing

## Migration from Mailer App

If you're currently using the mailer app and want to switch to this simpler service:

1. Replace `EmailQueue.objects.create()` with `email_service.send_email()`
2. Remove Celery tasks for email processing
3. Update your email sending logic to use the service directly
4. Keep the mailer app installed if you need the historical email logs

## Support

For issues or questions about the email service, check the examples in `examples.py` or create an issue in the repository.
