import logging
import threading
from typing import Optional, Tuple

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

from .base import BaseSMSService

# TODO: Use app logger
logger = logging.getLogger(__name__)


class TwilioSMSService(BaseSMSService):
  """
  Service for sending SMS messages via Twilio API
  Handles OTP generation, storage, and validation
  """

  def __init__(self):
    self.account_sid = getattr(settings, "TWILIO_ACCOUNT_SID", None)
    self.auth_token = getattr(settings, "TWILIO_AUTH_TOKEN", None)
    self.from_number = getattr(settings, "TWILIO_FROM_NUMBER", None)
    self.verify_service_id = getattr(settings, "TWILIO_VERIFY_SERVICE_ID", None)
    self.otp_expiry_minutes = getattr(settings, "OTP_EXPIRY_MINUTES", 5)
    self.otp_max_attempts = getattr(settings, "OTP_MAX_ATTEMPTS", 3)
    self.otp_resend_interval = getattr(settings, "OTP_RESEND_INTERVAL_SECONDS", 30)

    if self.account_sid and self.auth_token:
      self.client = Client(self.account_sid, self.auth_token)
    else:
      self.client = None
      logger.warning("Twilio credentials not configured. SMS sending will be disabled.")

  def _get_cache_key(self, mobile_number: str, key_type: str) -> str:
    """Generate cache key for storing OTP data"""
    return f"otp:{key_type}:{mobile_number}"

  def _can_send_otp(self, mobile_number: str) -> Tuple[bool, Optional[str]]:
    """
    Check if OTP can be sent to the mobile number
    Returns: (can_send, error_message)
    """
    last_sent_key = self._get_cache_key(mobile_number, "last_sent")
    last_sent = cache.get(last_sent_key)

    if last_sent:
      time_since_last = (timezone.now() - last_sent).total_seconds()
      if time_since_last < self.otp_resend_interval:
        remaining = int(self.otp_resend_interval - time_since_last)
        return False, f"Please wait {remaining} seconds before requesting a new OTP"

    daily_count_key = self._get_cache_key(mobile_number, "daily_count")
    daily_count = cache.get(daily_count_key, 0)
    daily_limit = getattr(settings, "OTP_DAILY_LIMIT", 10)

    if daily_count >= daily_limit:
      return False, "Daily OTP limit reached. Please try again tomorrow."

    return True, None

  def send_otp(self, mobile_number: str) -> Tuple[bool, str]:
    """
    Send OTP to the given mobile number
    Returns: (success, message)
    """
    if not mobile_number or len(mobile_number) < 7:
      return False, "Invalid mobile number"

    can_send, error_message = self._can_send_otp(mobile_number)
    if not can_send:
      return False, error_message

    last_sent_key = self._get_cache_key(mobile_number, "last_sent")
    daily_count_key = self._get_cache_key(mobile_number, "daily_count")

    cache.set(last_sent_key, timezone.now(), timeout=self.otp_resend_interval)
    daily_count = cache.get(daily_count_key, 0)
    cache.set(daily_count_key, daily_count + 1, timeout=86400)

    if self.client:
      try:
        verification = self.client.verify.v2.services(self.verify_service_id).verifications.create(
          to=mobile_number, channel="sms"
        )
        logger.info(
          f"OTP sent successfully via Verify API to {mobile_number}. SID: {verification.sid}"
        )
        return True, "OTP sent successfully"
      except TwilioRestException as e:
        logger.error(f"Failed to send OTP via Verify API to {mobile_number}: {str(e)}")
        if settings.DEBUG:
          return True, "OTP sent successfully"
        return False, "Failed to send OTP. Please try again later."
    else:
      return False, "OTP service not available"

  def verify_otp(self, mobile_number: str, otp_code: str) -> Tuple[bool, str]:
    """
    Verify the OTP for the given mobile number
    Returns: (is_valid, message)
    """
    if not mobile_number or not otp_code:
      return False, "Mobile number and OTP are required"

    if not otp_code.isdigit() or len(otp_code) != 4:
      return False, "OTP must be exactly 4 digits"

    if self.client:
      try:
        verification_check = self.client.verify.v2.services(
          self.verify_service_id
        ).verification_checks.create(to=mobile_number, code=otp_code)

        if verification_check.status == "approved":
          logger.info(f"OTP verified successfully via Verify API for {mobile_number}")
          return True, "OTP verified successfully"
        else:
          logger.warning(
            f"OTP verification failed for {mobile_number}. Status: {verification_check.status}"
          )
          return False, "Invalid OTP"
      except TwilioRestException as e:
        logger.error(f"Failed to verify OTP via Verify API for {mobile_number}: {str(e)}")
        if settings.DEBUG:
          if otp_code == "1234":
            return True, "OTP verified successfully"
        return False, "Failed to verify OTP. Please try again."
    else:
      if settings.DEBUG:
        logger.info("Using fallback verification for 4-digit OTP as configured")
        if otp_code == "1234":
          logger.info(f"OTP verified successfully via fallback for {mobile_number}")
          return True, "OTP verified successfully"
      logger.warning(f"OTP verification failed for {mobile_number} via fallback")
      return False, "Invalid OTP"

  def clear_otp(self, mobile_number: str) -> None:
    """Clear all OTP data for a mobile number"""
    keys_to_clear = ["code", "attempts", "last_sent"]
    for key_type in keys_to_clear:
      cache_key = self._get_cache_key(mobile_number, key_type)
      cache.delete(cache_key)

  def send_sms(self, to_number: str, message: str) -> Tuple[bool, str]:
    """
    Send a plain SMS message to the specified number.

    Args:
        to_number: The recipient's phone number (including country code)
        message: The message content to send

    Returns:
        Tuple of (success: bool, message: str)
    """
    if not self.client:
      if settings.DEBUG:
        logger.warning(f"DEBUG MODE: SMS to {to_number}: {message}")
        return True, "SMS sent successfully (DEBUG MODE - No SMS sent)"
      return False, "SMS service is not configured"

    try:
      sms_message = self.client.messages.create(body=message, from_=self.from_number, to=to_number)
      logger.info(f"SMS sent successfully to {to_number}. Message SID: {sms_message.sid}")
      return True, "SMS sent successfully"
    except TwilioRestException as e:
      logger.error(f"Failed to send SMS to {to_number}: {str(e)}")
      return False, f"Failed to send SMS: {str(e)}"

  def is_configured(self) -> bool:
    """
    Check if the SMS service is properly configured.

    Returns:
        True if Twilio credentials are set, False otherwise
    """
    has_basic_config = bool(self.account_sid and self.auth_token)

    has_sms_config = bool(self.from_number)
    has_verify_config = bool(self.verify_service_id)

    return has_basic_config and (has_sms_config or has_verify_config)


_sms_service = None
_lock = threading.Lock()


def get_sms_service() -> TwilioSMSService:
  global _sms_service
  if _sms_service is None:
    with _lock:
      if _sms_service is None:
        _sms_service = TwilioSMSService()
  return _sms_service
