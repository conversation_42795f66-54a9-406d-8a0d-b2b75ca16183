from abc import ABC, abstractmethod
from typing import <PERSON>ple, Optional

class BaseSMSService(ABC):
    """
    Abstract base class for SMS service implementations.
    This allows for easy switching between different SMS providers.
    """

    @abstractmethod
    def send_sms(self, to_number: str, message: str) -> Tuple[bool, str]:
        """
        Send an SMS message to the specified number.

        Args:
            to_number: The recipient's phone number (including country code)
            message: The message content to send

        Returns:
            Tuple of (success: bool, message: str)
            - success: Whether the SMS was sent successfully
            - message: Success message or error description
        """
        pass

    @abstractmethod
    def send_otp(self, mobile_number: str) -> Tuple[bool, str]:
        """
        Generate and send an OTP to the specified mobile number.

        Args:
            mobile_number: The recipient's mobile number

        Returns:
            Tuple of (success: bool, message: str)
        """
        pass

    @abstractmethod
    def verify_otp(self, mobile_number: str, otp_code: str) -> Tuple[bool, str]:
        """
        Verify an OTP code for the given mobile number.

        Args:
            mobile_number: The mobile number to verify
            otp_code: The OTP code to validate

        Returns:
            Tuple of (is_valid: bool, message: str)
        """
        pass

    @abstractmethod
    def is_configured(self) -> bool:
        """
        Check if the SMS service is properly configured.

        Returns:
            True if the service has all required configuration, False otherwise
        """
        pass
