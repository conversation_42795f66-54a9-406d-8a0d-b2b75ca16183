FROM python:3.11-slim

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONPATH=/app:$PYTHONPATH

# Install cron and other dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        cron \
        postgresql-client \
        libpq-dev \
        gdal-bin \
        libgdal-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt /app/
RUN pip install --upgrade pip \
    && pip install -r requirements.txt

# Copy application code
COPY . /app/

# Make entrypoint executable
RUN chmod +x cron_entrypoint.sh

# Create log directory
RUN mkdir -p /var/log/cron

# We run cron as root (standard practice for cron containers)
CMD ["/app/cron_entrypoint.sh"]
