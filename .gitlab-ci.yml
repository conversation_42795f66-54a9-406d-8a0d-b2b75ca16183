stages:
  - deploy
deploy-staging:
  stage: deploy
  only:
    - main
    - web
  environment: staging
  before_script:
    - AUTHOR=$(git log -1 --pretty=format:'%an')
    - EMAIL=$(git log -1 --pretty=format:'%ae')
    - MESSAGE=$(git log -1 --pretty=format:'%s')
    - HASH=$(git log -1 --pretty=format:'%h')
    - PAYLOAD=$(printf '{"text":"Deployment starting for %s by %s"}' "$HASH" "$AUTHOR")
    - 'curl -X POST "$MATTERMOST_WEBHOOK_URL" -H "Content-Type: application/json" -d "$PAYLOAD"'
  script:
    - echo "Moving into rentie folder..."
    - cd /srv/rentie/rentie_drf
    - echo "Current directory:"
    - pwd
    - make git
    - make build
    - echo "FINISH"
  after_script:
    - AUTHOR=$(git log -1 --pretty=format:'%an')
    - HASH=$(git log -1 --pretty=format:'%h')
    - if [ "$CI_JOB_STATUS" = "success" ]; then STATUS_MSG="✅ Deployment successful"; else STATUS_MSG="❌ Deployment failed"; fi
    - PAYLOAD=$(printf '{"text":"%s for %s by %s"}' "$STATUS_MSG" "$HASH" "$AUTHOR")
    - 'curl -X POST "$MATTERMOST_WEBHOOK_URL" -H "Content-Type: application/json" -d "$PAYLOAD"'
