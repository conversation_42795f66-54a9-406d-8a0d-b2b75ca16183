import os
import stripe
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from api.payment.services import PaymentService
from api.stripe_connect.services import StripeConnectService
from api.subscription.services import SubscriptionService
from core.utils.app_logger import AppLogger

logger = AppLogger()


@csrf_exempt
@require_POST
def stripe_webhook(request):
  payload = request.body
  sig_header = request.META.get("HTTP_STRIPE_SIGNATURE")
  webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")

  try:
    event = stripe.Webhook.construct_event(payload, sig_header, webhook_secret)
  except ValueError as e:
    # Invalid payload
    logger.log_error("Stripe webhook - Invalid payload", e)
    return HttpResponse(status=400)
  except stripe.error.SignatureVerificationError as e:
    # Invalid signature
    logger.log_error("Stripe webhook - Invalid signature", e)
    return HttpResponse(status=400)

  # Handle the event
  if event.type == "payment_intent.succeeded":
    payment_intent = event.data.object
    PaymentService.update_payment_from_webhook(payment_intent.id, {"status": payment_intent.status})
  elif event.type == "payment_intent.payment_failed":
    payment_intent = event.data.object
    PaymentService.update_payment_from_webhook(payment_intent.id, {"status": payment_intent.status})
  elif event.type == "account.updated":
    account = event.data.object
    StripeConnectService.update_connect_account_status(account.id)
  elif event.type == "subscription.updated" or event.type == "subscription.deleted":
    subscription = event.data.object
    SubscriptionService.update_subscription_from_webhook(
      subscription.id,
      {
        "status": subscription.status,
        "current_period_start": subscription.current_period_start,
        "current_period_end": subscription.current_period_end,
        "cancel_at_period_end": subscription.cancel_at_period_end,
      },
    )

  return HttpResponse(status=200)
