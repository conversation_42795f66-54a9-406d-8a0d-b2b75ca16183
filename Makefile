# Rentie port on sigma staging server
PORT ?= 7112
ALLOWED_HOSTS ?= localhost,web.rentie-backend-new.orb.local,127.0.0.1,0.0.0.0,**************,rentie.sigmacode.it,sigmacode.it,web.rentie-drf.orb.local
BASE_DOMAIN ?= https://web.rentie-drf.orb.local

GREEN := \033[0;32m
YELLOW := \033[0;33m
NC := \033[0m

.PHONY: help build up down logs clean

help:
	@echo "${GREEN}Rentie Backend Makefile Commands:${NC}"
	@echo "  ${YELLOW}make build${NC}        - Rebuild all Docker containers"
	@echo "  ${YELLOW}make up${NC}           - Start all containers"
	@echo "  ${YELLOW}make down${NC}         - Stop all containers"
	@echo "  ${YELLOW}make logs${NC}         - View logs from all services"
	@echo "  ${YELLOW}make logs-web${NC}     - View logs from web service only"
	@echo "  ${YELLOW}make logs-db${NC}      - View logs from database service only"
	@echo "  ${YELLOW}make logs-redis${NC}   - View logs from redis service only"
	@echo "  ${YELLOW}make logs-cron${NC}    - View logs from cron service only"
	@echo "  ${YELLOW}make clean${NC}        - Stop and remove containers, networks, and volumes"
	@echo ""
	@echo "Custom port and hosts:"
	@echo "  ${YELLOW}make build PORT=9000 ALLOWED_HOSTS=myhost.com,localhost${NC}"

git:
	@echo "Fetching latest changes from remote repository..."
	@git fetch --all
	@git reset --hard origin/main

build:
	@echo "${GREEN}Rebuilding all containers with PORT=${PORT} and ALLOWED_HOSTS=${ALLOWED_HOSTS}${NC}"
	PORT=$(PORT) ALLOWED_HOSTS="$(ALLOWED_HOSTS)" docker compose build
	PORT=$(PORT) ALLOWED_HOSTS="$(ALLOWED_HOSTS)" docker compose up -d

up:
	@echo "${GREEN}Starting all containers with PORT=${PORT} and ALLOWED_HOSTS=${ALLOWED_HOSTS}${NC}"
	PORT=$(PORT) ALLOWED_HOSTS="$(ALLOWED_HOSTS)" docker compose up -d

down:
	@echo "${GREEN}Stopping all containers${NC}"
	docker compose down

logs:
	@echo "${GREEN}Showing logs for all services${NC}"
	docker compose logs -f

logs-web:
	@echo "${GREEN}Showing logs for web service${NC}"
	docker compose logs -f web

logs-db:
	@echo "${GREEN}Showing logs for database service${NC}"
	docker compose logs -f db

logs-redis:
	@echo "${GREEN}Showing logs for redis service${NC}"
	docker compose logs -f redis

logs-cron:
	@echo "${GREEN}Showing logs for cron service${NC}"
	docker compose logs -f cron

clean:
	@echo "${GREEN}Stopping and removing containers, networks, and volumes${NC}"
	docker compose down -v
