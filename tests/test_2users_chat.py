import requests
import websocket
import json
import time
import threading
from datetime import datetime
import queue

# Configuration
API_BASE_URL = "http://localhost:80"  # Or your server's IP/hostname
WS_BASE_URL = "ws://localhost:80"  # Or your server's IP/hostname

# User credentials
USER1_ID = "28bf2659-11de-4e56-9661-9c09e703ab4d"
USER1_MOBILE = "000111222"
USER1_OTP = "1234"

USER2_ID = "9c191f88-c1e3-46c2-a5d9-269315fc004f"
USER2_MOBILE = "666666666"
USER2_OTP = "1234"

# Timeout for waiting for messages or connections
ACTION_TIMEOUT = 15.0  # Increased timeout for full flow
MESSAGE_RECEIVE_TIMEOUT = 5.0  # Max time to wait for a single message broadcast


# Constants for WebSocket message types (matching your backend)
class WebSocketMsgTypes:
  ERROR = "error"
  CHAT_MESSAGE = "chat_message"
  INFO = "info"


# --- Authentication Helper ---
def get_auth_token(mobile_number, otp_number):
  print(f"Attempting authentication for user with mobile: {mobile_number}...")
  url = f"{API_BASE_URL}/api/auth/otp/verify/"
  payload = {"mobile_number": mobile_number, "otp_number": otp_number}

  try:
    response = requests.post(url, json=payload)
    response.raise_for_status()
    auth_data = response.json()
    access_token = auth_data["data"]["access"]
    print(f"Authentication successful for {mobile_number}")
    return access_token
  except requests.exceptions.RequestException as e:
    print(f"Authentication failed for {mobile_number}: {e}")
    if hasattr(e, "response") and e.response:
      print(f"Response: {e.response.text}")
    return None


# --- WebSocket Connection Manager for a single user ---
class UserConnection:
  def __init__(self, user_id, auth_token, other_user_id):
    self.user_id = user_id
    self.auth_token = auth_token
    self.other_user_id = other_user_id
    self.ws = None
    self.thread = None
    self.is_connected = threading.Event()
    self.error = None
    self.message_queue = queue.Queue()

    ws_url = f"{WS_BASE_URL}/ws/chat/{self.other_user_id}/"
    headers = {"Authorization": f"Bearer {self.auth_token}"}

    self.ws = websocket.WebSocketApp(
      ws_url,
      header=headers,
      on_open=self._on_open,
      on_message=self._on_message,
      on_error=self._on_error,
      on_close=self._on_close,
    )

  def _on_message(self, ws, message):
    try:
      data = json.loads(message)
      msg_type = data.get("type")
      print(
        f"[User {self.user_id[:4]}...] Received message of type '{msg_type}': {json.dumps(data)}"
      )

      self.message_queue.put(data)

      if msg_type == WebSocketMsgTypes.INFO:
        if data.get("code") == "CONNECTED":
          print(f"[User {self.user_id[:4]}...] Connection successful.")
          self.is_connected.set()
      elif msg_type == WebSocketMsgTypes.ERROR:
        print(f"[User {self.user_id[:4]}...] Received Error: {json.dumps(data)}")
        self.error = data
    except json.JSONDecodeError:
      print(f"[User {self.user_id[:4]}...] Received non-JSON message: {message}")
      self.error = f"Non-JSON message received: {message}"
    except Exception as e:
      print(f"[User {self.user_id[:4]}...] Error processing message: {e}")
      self.error = f"Message processing error: {e}"

  def _on_error(self, ws, error):
    print(f"[User {self.user_id[:4]}...] WebSocket error: {error}")
    self.error = error
    self.is_connected.set()

  def _on_close(self, ws, close_status_code, close_msg):
    print(f"[User {self.user_id[:4]}...] WebSocket closed: {close_status_code} - {close_msg}")
    self.is_connected.set()

  def _on_open(self, ws):
    print(f"[User {self.user_id[:4]}...] WebSocket connection established.")

  def start(self):
    print(f"[User {self.user_id[:4]}...] Starting WebSocket thread...")
    self.thread = threading.Thread(target=self.ws.run_forever)
    self.thread.daemon = True
    self.thread.start()

  def stop(self):
    if self.ws:
      print(f"[User {self.user_id[:4]}...] Closing WebSocket connection...")
      self.ws.close()
    if self.thread and self.thread.is_alive():
      self.thread.join(timeout=1.0)

  def send_message(self, content):
    if not self.ws or not self.ws.sock or self.error:
      print(
        f"[User {self.user_id[:4]}...] Cannot send message: connection not ready or error occurred."
      )
      return False

    message_payload = {"type": WebSocketMsgTypes.CHAT_MESSAGE, "message": content}
    try:
      print(f"[User {self.user_id[:4]}...] Sending message: '{content[:50]}...'")
      self.ws.send(json.dumps(message_payload))
      return True
    except Exception as e:
      print(f"[User {self.user_id[:4]}...] Error sending message: {e}")
      self.error = f"Send error: {e}"
      return False

  def wait_for_message(
    self,
    expected_sender_id=None,
    expected_content=None,
    expected_message_id=None,
    timeout=MESSAGE_RECEIVE_TIMEOUT,
  ):
    start_time = time.time()
    received_message_data = None

    while time.time() - start_time < timeout:
      try:
        msg_data = self.message_queue.get(timeout=0.1)

        if msg_data.get("type") == WebSocketMsgTypes.CHAT_MESSAGE:
          is_match = True
          if expected_sender_id is not None and msg_data.get("sender") != expected_sender_id:
            is_match = False
          if expected_content is not None and msg_data.get("message") != expected_content:
            is_match = False
          if expected_message_id is not None and msg_data.get("message_id") != expected_message_id:
            is_match = False

          if is_match:
            received_message_data = msg_data
            break

      except queue.Empty:
        pass
      except Exception as e:
        print(f"[User {self.user_id[:4]}...] Error processing message from queue: {e}")
        self.error = f"Queue processing error: {e}"
        break

    if received_message_data is None:
      print(f"[User {self.user_id[:4]}...] Timed out waiting for expected message.")

    return received_message_data


# --- Main Test Function ---
def run_chat_test():
  print("\n--- Starting Two-User Chat Test ---")

  user1_token = get_auth_token(USER1_MOBILE, USER1_OTP)
  user2_token = get_auth_token(USER2_MOBILE, USER2_OTP)

  if not user1_token or not user2_token:
    print("❌ Failed to authenticate one or both users. Exiting.")
    return False

  print("\nSetting up WebSocket connections...")
  user1_conn = UserConnection(USER1_ID, user1_token, USER2_ID)
  user2_conn = UserConnection(USER2_ID, user2_token, USER1_ID)

  user1_conn.start()
  user2_conn.start()

  print("Waiting for both connections to become ready...")
  user1_connected = user1_conn.is_connected.wait(timeout=ACTION_TIMEOUT)
  user2_connected = user2_conn.is_connected.wait(timeout=ACTION_TIMEOUT)

  if not user1_connected or not user2_connected:
    print("❌ One or both WebSocket connections failed to establish within the timeout.")
    if user1_conn.error:
      print(f"User 1 connection error: {user1_conn.error}")
    if user2_conn.error:
      print(f"User 2 connection error: {user2_conn.error}")
    user1_conn.stop()
    user2_conn.stop()
    return False

  print("✅ Both users successfully connected.")
  test_success = True

  user1_messages_to_send = [
    "Hello from User 1!",
    "How are you?",
    "This is the third message.",
  ]
  user2_messages_to_send = [
    "Hi User 1, I'm User 2!",
    "I'm doing well, thanks! And you?",
    "Received your messages.",
  ]

  for msg1, msg2 in zip(user1_messages_to_send, user2_messages_to_send):
    user1_conn.send_message(msg1)
    user2_msg_received = user2_conn.wait_for_message(
      expected_sender_id=USER1_ID, expected_content=msg1
    )

    user2_conn.send_message(msg2)
    user1_msg_received = user1_conn.wait_for_message(
      expected_sender_id=USER2_ID, expected_content=msg2
    )

    if not user2_msg_received or not user1_msg_received:
      print("❌ Message not received by one of the users.")
      test_success = False
      break

  user1_conn.stop()
  user2_conn.stop()

  if test_success:
    print("\n✅ Chat test completed successfully.")
  else:
    print("\n❌ Chat test failed.")
  return test_success


# Run the test
if __name__ == "__main__":
  run_chat_test()
