import requests
import websocket
import json
import time
import threading

# Configuration
API_BASE_URL = "http://localhost:80"
WS_BASE_URL = "ws://localhost:80"
MOBILE_NUMBER = "000111222"
OTP_NUMBER = "1234"
MESSAGE_TIMEOUT = 10.0


class WebSocketMsgTypes:
  ERROR = "error"
  CHAT_MESSAGE = "chat_message"
  INFO = "info"


test_complete_event = threading.Event()
test_status = "waiting"
last_response = None
received_chat_message = None


def get_auth_token():
  print("Getting authentication token...")
  url = f"{API_BASE_URL}/api/auth/otp/verify/"
  payload = {"mobile_number": MOBILE_NUMBER, "otp_number": OTP_NUMBER}

  try:
    response = requests.post(url, json=payload)
    response.raise_for_status()
    access_token = response.json()["data"]["access"]
    print("Authentication successful")
    return access_token
  except requests.RequestException as e:
    print(f"Authentication failed: {e}")
    if e.response:
      print(f"Response: {e.response.text}")
    return None


def on_message(ws, message):
  global test_status, last_response, received_chat_message
  try:
    data = json.loads(message)
    print(f"Received response:\n{json.dumps(data, indent=2)}")
    last_response = data
    msg_type = data.get("type")

    if msg_type == WebSocketMsgTypes.CHAT_MESSAGE:
      received_chat_message = data
      test_status = "success"
      test_complete_event.set()

    elif msg_type == WebSocketMsgTypes.ERROR:
      test_status = "error"
      test_complete_event.set()

    elif msg_type == WebSocketMsgTypes.INFO:
      print("Received info message, continuing...")

    else:
      print(f"Unexpected message type: {msg_type}")

  except json.JSONDecodeError:
    print(f"Received non-JSON message: {message}")
    last_response = message
    test_status = "error"
    test_complete_event.set()
  except Exception as e:
    print(f"Error processing message: {e}")
    test_status = "error"
    test_complete_event.set()


def on_error(ws, error):
  global test_status
  print(f"WebSocket error: {error}")
  if test_status == "waiting":
    test_status = "error"
  test_complete_event.set()


def on_close(ws, close_status_code, close_msg):
  global test_status
  print(f"WebSocket closed: {close_status_code} - {close_msg}")
  if test_status == "waiting":
    test_status = "closed_prematurely"
  test_complete_event.set()


def on_open(ws):
  """Handle WebSocket connection open"""
  print("WebSocket connection established")

  timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

  # FIX: Add a 'type' field that matches what the backend expects
  test_message = {
    "type": "chat_message",  # Required for backend to route it properly
    "message": f"Test message sent at {timestamp}",
  }

  try:
    ws.send(json.dumps(test_message))
    print(f"Sent message: {json.dumps(test_message, indent=2)}")
  except Exception as e:
    print(f"Error sending message: {e}")
    global test_status
    test_status = "error"
    test_complete_event.set()


def test_websocket():
  global test_status, last_response, received_chat_message

  auth_token = get_auth_token()
  if not auth_token:
    print("❌ Failed to get token.")
    return False

  test_complete_event.clear()
  test_status = "waiting"
  last_response = None
  received_chat_message = None

  user_id = "9c191f88-c1e3-46c2-a5d9-269315fc004f"
  ws_url = f"{WS_BASE_URL}/ws/chat/{user_id}/"
  headers = {"Authorization": f"Bearer {auth_token}"}

  print(f"Connecting to WebSocket: {ws_url}")

  ws = websocket.WebSocketApp(
    ws_url,
    header=headers,
    on_open=on_open,
    on_message=on_message,
    on_error=on_error,
    on_close=on_close,
  )

  ws_thread = threading.Thread(target=ws.run_forever)
  ws_thread.daemon = True
  ws_thread.start()

  print(f"Waiting for '{WebSocketMsgTypes.CHAT_MESSAGE}' (timeout: {MESSAGE_TIMEOUT}s)...")
  event_set = test_complete_event.wait(timeout=MESSAGE_TIMEOUT)

  if event_set:
    if test_status == "success":
      print(f"✅ Test succeeded - Received '{WebSocketMsgTypes.CHAT_MESSAGE}'")
    elif test_status == "error":
      print("❌ Test failed - Server error received.")
    elif test_status == "closed_prematurely":
      print("❌ Test failed - Connection closed too early.")
  else:
    test_status = "timeout"
    print(f"❌ Test failed - Timeout waiting for '{WebSocketMsgTypes.CHAT_MESSAGE}'")

  print("Closing WebSocket...")
  try:
    ws.keep_running = False
    ws.close()
  except Exception as e:
    print(f"Error closing WebSocket: {e}")

  time.sleep(0.5)

  return test_status == "success"


if __name__ == "__main__":
  final_result = test_websocket()

  print("-" * 30)
  if final_result:
    print("✅ WEBSOCKET TEST PASSED")
    print(json.dumps(received_chat_message, indent=2))
  else:
    print("❌ WEBSOCKET TEST FAILED")
    print(f"Last message: {json.dumps(last_response, indent=2) if last_response else 'None'}")
    print(f"Status: {test_status}")
  print("-" * 30)
