import requests
import websocket
import json
import time
import threading

# Configuration
API_BASE_URL = "http://localhost:80"
WS_BASE_URL = "ws://localhost:80"
MOBILE_NUMBER = "000111222"
OTP_NUMBER = "1234"
# Timeout for waiting for specific messages
MESSAGE_TIMEOUT = 10.0


# Constants for WebSocket message types (matching backend)
class WebSocketMsgTypes:
  HEARTBEAT = "heartbeat"
  HEARTBEAT_ACK = "heartbeat_ack"
  USER_ONLINE_STATUS = "user_online_status"
  USER_ONLINE_QUERY = "user_online_query"
  USER_ONLINE_RESPONSE = "user_online_response"
  ERROR = "error"
  INFO = "info"


# Events to signal test completion or failure
test_complete_event = threading.Event()
status_response_event = threading.Event()
# Variables to store outcomes: "waiting", "success", "error", "closed_prematurely", "timeout"
test_status = "waiting"
status_response_status = "waiting"
last_response = None
received_online_statuses = None
heartbeat_thread = None
stop_heartbeat = threading.Event()


def get_auth_token():
  """Get JWT authentication token by verifying OTP"""
  print("Getting authentication token...")
  url = f"{API_BASE_URL}/api/auth/otp/verify/"
  payload = {"mobile_number": MOBILE_NUMBER, "otp_number": OTP_NUMBER}

  try:
    response = requests.post(url, json=payload)
    response.raise_for_status()
    auth_data = response.json()
    access_token = auth_data["data"]["access"]
    print("Authentication successful")
    return access_token
  except requests.exceptions.RequestException as e:
    print(f"Authentication failed: {e}")
    if hasattr(e, "response") and e.response:
      print(f"Response: {e.response.text}")
    return None


def on_message(ws, message):
  """Handle incoming WebSocket messages"""
  global test_status, last_response, received_online_statuses, status_response_status

  try:
    data = json.loads(message)
    print(f"Received response: {json.dumps(data, indent=2)}")
    last_response = data  # Store the last message for debugging

    msg_type = data.get("type")

    if msg_type == WebSocketMsgTypes.USER_ONLINE_STATUS:
      # This is a user status update message
      print(f"User {data['user_id']} is {'online' if data['is_online'] else 'offline'}")
      # We don't set test completion yet as this is just one of the expected messages

    elif msg_type == WebSocketMsgTypes.USER_ONLINE_RESPONSE:
      # Response to our status query
      received_online_statuses = data.get("statuses", [])
      print(f"Received statuses for {len(received_online_statuses)} users")
      status_response_status = "success"
      status_response_event.set()  # Signal completion of status query

      # We could set test completion here if this is our main test goal
      # But for now, let's wait for our specific success criteria

    elif msg_type == WebSocketMsgTypes.HEARTBEAT_ACK:
      # Server acknowledged our heartbeat
      print("Heartbeat acknowledged")

    elif msg_type == WebSocketMsgTypes.ERROR:
      # Server sent an error
      print(f"Error from server: {data.get('code')} - {data.get('message')}")
      if status_response_status == "waiting":
        status_response_status = "error"
        status_response_event.set()
      if test_status == "waiting":
        test_status = "error"
        test_complete_event.set()

    elif msg_type == WebSocketMsgTypes.INFO:
      # Info message (like connection confirmation)
      code = data.get("code", "")
      message = data.get("message", "")
      print(f"Info: {code} - {message}")

      # If our test is specifically looking for a successful connection
      if code == "CONNECTED" and test_status == "waiting":
        print("Connection established successfully!")
        # We set success if our test only needs connection confirmation
        # For broader tests, we'd keep waiting for other messages
        # test_status = "success"
        # test_complete_event.set()

    else:
      # Handle unexpected message types
      print(f"Received unexpected message type: {msg_type}")

  except json.JSONDecodeError:
    print(f"Received non-JSON message: {message}")
    last_response = message  # Store raw message if not JSON
    if test_status == "waiting":
      test_status = "error"
      test_complete_event.set()
    if status_response_status == "waiting":
      status_response_status = "error"
      status_response_event.set()

  except Exception as e:
    print(f"Error processing message: {e}")
    if test_status == "waiting":
      test_status = "error"
      test_complete_event.set()
    if status_response_status == "waiting":
      status_response_status = "error"
      status_response_event.set()


def on_error(ws, error):
  """Handle WebSocket errors"""
  global test_status, status_response_status
  print(f"WebSocket error: {error}")
  if test_status == "waiting":
    test_status = "error"
    test_complete_event.set()
  if status_response_status == "waiting":
    status_response_status = "error"
    status_response_event.set()


def on_close(ws, close_status_code, close_msg):
  """Handle WebSocket connection close"""
  global test_status, status_response_status
  print(f"WebSocket closed: {close_status_code} - {close_msg}")
  if test_status == "waiting":
    test_status = "closed_prematurely"
    test_complete_event.set()
  if status_response_status == "waiting":
    status_response_status = "closed_prematurely"
    status_response_event.set()


def on_open(ws):
  """Handle WebSocket connection open"""
  print("WebSocket connection established")

  # Start sending heartbeats
  global heartbeat_thread, stop_heartbeat
  stop_heartbeat.clear()
  heartbeat_thread = threading.Thread(target=send_heartbeats, args=(ws,))
  heartbeat_thread.daemon = True
  heartbeat_thread.start()

  # Query status of some users after connection is open
  test_user_ids = [
    "9c191f88-c1e3-46c2-a5d9-269315fc004f",  # Example ID - UPDATE THIS
    "51ca3997-8a3f-4d6a-9faa-d043ab3f707e",  # Example ID - UPDATE THIS
  ]

  try:
    query_message = {"type": WebSocketMsgTypes.USER_ONLINE_QUERY, "user_ids": test_user_ids}
    ws.send(json.dumps(query_message))
    print(f"Sent user status query: {json.dumps(query_message, indent=2)}")
  except Exception as e:
    print(f"Error sending status query: {e}")
    global test_status
    test_status = "error"
    test_complete_event.set()


def send_heartbeats(ws):
  """Send periodic heartbeats to keep the connection alive"""
  heartbeat_interval = 60  # 60 seconds between heartbeats

  while not stop_heartbeat.is_set():
    try:
      # Sleep for a bit to avoid sending heartbeat immediately on connection
      for _ in range(heartbeat_interval):
        if stop_heartbeat.is_set():
          return
        time.sleep(1)

      # Send heartbeat
      heartbeat_message = {"type": WebSocketMsgTypes.HEARTBEAT}
      ws.send(json.dumps(heartbeat_message))
      print("Sent heartbeat")
    except Exception as e:
      print(f"Error sending heartbeat: {e}")
      if stop_heartbeat.is_set():
        return


def test_online_status_tracking():
  global test_status, status_response_status, last_response, received_online_statuses
  global heartbeat_thread, stop_heartbeat

  # Get authentication token
  auth_token = get_auth_token()
  if not auth_token:
    print("Failed to get authentication token. Exiting.")
    return False

  # Reset global state for a new test run
  test_complete_event.clear()
  status_response_event.clear()
  test_status = "waiting"
  status_response_status = "waiting"
  last_response = None
  received_online_statuses = None
  stop_heartbeat.set()  # Make sure any existing heartbeat thread stops

  # WebSocket URL for online status
  ws_url = f"{WS_BASE_URL}/ws/status/online/"

  # Set up headers with authorization
  headers = {"Authorization": f"Bearer {auth_token}"}

  print(f"Connecting to WebSocket at {ws_url}")

  # Create WebSocket connection
  ws = websocket.WebSocketApp(
    ws_url,
    header=headers,
    on_open=on_open,
    on_message=on_message,
    on_error=on_error,
    on_close=on_close,
  )

  # Start the WebSocket connection in a separate thread
  ws_thread = threading.Thread(target=ws.run_forever)
  ws_thread.daemon = True
  ws_thread.start()

  # Wait for the status query response or timeout
  print(f"Waiting for '{WebSocketMsgTypes.USER_ONLINE_RESPONSE}' (timeout: {MESSAGE_TIMEOUT}s)...")
  event_set = status_response_event.wait(timeout=MESSAGE_TIMEOUT)

  # Determine the outcome of status query
  if event_set:
    if status_response_status == "success":
      print(f"✅ Received user status response with {len(received_online_statuses)} statuses")
      # Print out the status of each user
      for status in received_online_statuses:
        user_id = status.get("user_id")
        is_online = status.get("is_online")
        last_activity = status.get("last_activity")
        print(
          f"User {user_id}: {'Online' if is_online else 'Offline'}, Last active: {last_activity}"
        )

      # Set test as successful since we received status
      test_status = "success"
      test_complete_event.set()
    elif status_response_status == "error":
      print("❌ Status query failed - An error occurred.")
    elif status_response_status == "closed_prematurely":
      print("❌ Status query failed - Connection closed before receiving response.")
  else:
    # Timeout occurred waiting for status response
    status_response_status = "timeout"
    print(
      f"❌ Status query failed - Timeout reached before receiving '{WebSocketMsgTypes.USER_ONLINE_RESPONSE}'."
    )
    # Also mark overall test as timed out
    test_status = "timeout"
    test_complete_event.set()

  # Do other testing if needed
  # For now, if status query succeeded, we consider the whole test successful
  if not test_complete_event.is_set():
    # Wait for the test completion event or timeout
    print("Waiting for other test criteria to complete...")
    test_complete_event.wait(timeout=MESSAGE_TIMEOUT)

  # Stop heartbeat thread
  stop_heartbeat.set()
  if heartbeat_thread and heartbeat_thread.is_alive():
    heartbeat_thread.join(1.0)  # Wait for heartbeat thread to finish

  # Close the connection
  print("Closing WebSocket connection...")
  try:
    ws.close()
  except Exception as e:
    print(f"Error during connection close: {e}")

  # Give some time for the close to complete and the thread to clean up
  time.sleep(0.5)

  # Return True if the test status is 'success', False otherwise
  return test_status == "success"


if __name__ == "__main__":
  # Enable debug output for websocket-client if needed
  # websocket.enableTrace(True)

  # Run the test
  final_result = test_online_status_tracking()

  # Print overall result
  print("-" * 50)
  if final_result:
    print("\n✅ ONLINE STATUS WEBSOCKET TEST PASSED")
    if received_online_statuses:
      print(f"\nReceived status information for {len(received_online_statuses)} users:")
      for status in received_online_statuses:
        print(f"- User {status['user_id']}: {'Online' if status['is_online'] else 'Offline'}")
        if "last_activity" in status:
          print(f"  Last activity: {status['last_activity']}")
  else:
    print("\n❌ ONLINE STATUS WEBSOCKET TEST FAILED")
    print(f"\nLast received message before failure/timeout: {json.dumps(last_response, indent=2)}")
    print(f"Final test status: {test_status}")
    print(f"Status query result: {status_response_status}")

  print("-" * 50)
