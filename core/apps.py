from django.apps import AppConfig

from core.utils.app_logger import init_logger


class CoreConfig(AppConfig):
  default_auto_field = "django.db.models.BigAutoField"
  name = "core"

  def ready(self):
    """
    Initialize application components when Django starts
    """
    # Initialize the logger
    # TODO: Add sentry or other error logger service
    init_logger(app_name="rentie-backend-drf", sentry_dsn=None)
