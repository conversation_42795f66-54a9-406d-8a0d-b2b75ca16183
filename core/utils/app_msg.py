class AppMsg:
  """
  Static class containing all application messages.
  """

  REVIEW_CREATED = "Review submitted successfully"
  REVIEW_CREATE_FAILURE = "Failed to submit review"
  REVIEW_ALREADY_SUBMITTED = "You have already reviewed this session"
  REVIEW_NOT_SESSION_PARTICIPANT = "You must be a participant in the session to leave a review"
  REVIEW_SESSION_NOT_COMPLETED = "You can only review completed sessions"
  REVIEW_SUMMARY_RETRIEVED = "Review summary retrieved successfully"
  REVIEW_SUMMARY_RETRIEVAL_FAILURE = "Failed to retrieve review summary"

  CHAT_ROOM_CREATED = "Chat room created successfully"
  CHAT_ROOM_SELF = "You cannot create a chat room with yourself"

  SESSION_CREATED = "Session created successfully"
  SESSION_CREATE_FAILURE = "Failed to create session"
  SESSION_ALREADY_EXISTS = "Session already exists for this invitation"
  SESSION_INVITATION_NOT_ACCEPTED = "Cannot create session: invitation is not accepted"

  SESSION_STARTED = "Session started successfully"
  SESSION_START_FAILURE = "Failed to start session"
  SESSION_CANNOT_START = "Session cannot be started in its current state"
  USER_IS_NOT_A_HOST = "Only host can show QR code for session"
  USER_IS_NOT_A_GUEST = "Only guest can verify invitation"
  INVITATION_VERIFIED = "Invitation verified successfully"
  INVALID_SESSION_CODE = "Invalid session code"

  SESSION_PAUSED = "Session paused successfully"
  SESSION_PAUSE_FAILURE = "Failed to pause session"
  SESSION_CANNOT_PAUSE = "Session cannot be paused in its current state"

  SESSION_RESUMED = "Session resumed successfully"
  SESSION_RESUME_FAILURE = "Failed to resume session"
  SESSION_CANNOT_RESUME = "Session cannot be resumed in its current state"

  SESSION_ENDED = "Session ended successfully"
  SESSION_END_FAILURE = "Failed to end session"
  SESSION_CANNOT_END = "Session cannot be ended in its current state"

  SESSION_LEFT_EARLY = "Session terminated early"
  SESSION_LEAVE_FAILURE = "Failed to leave session"
  SESSION_CANNOT_LEAVE = "Session cannot be left in its current state"

  SESSION_EMERGENCY_REPORTED = "Emergency reported successfully"
  SESSION_EMERGENCY_REPORT_FAILURE = "Failed to report emergency"
  SESSION_CANNOT_REPORT_EMERGENCY = "Emergency cannot be reported for this session"

  SESSION_CODE_INVALID = "Invalid session code"
  SESSION_CODE_EXPIRED = "Session code has expired"

  BLOCKED_USERS_RETRIEVED = "Blocked users retrieved successfully"
  BLOCKED_USER_DETAILS_RETRIEVED = "Blocked user details retrieved successfully"
  USER_BLOCKED_SUCCESSFULLY = "User blocked successfully"
  USER_ALREADY_BLOCKED = "User is already blocked"
  USER_UNBLOCKED_SUCCESSFULLY = "User unblocked successfully"
  CANNOT_BLOCK_YOURSELF = "You cannot block yourself."

  PAYMENT_INTENT_CREATED = "Payment intent created successfully"
  PAYMENT_INTENT_FAILED = "Failed to create payment intent"
  PAYMENT_SUCCEEDED = "Payment completed successfully"
  PAYMENT_PROCESSING = "Payment is being processed"
  PAYMENT_FAILED = "Payment failed"
  PAYMENT_CONFIRMATION_FAILED = "Failed to confirm payment"
  PAYMENT_NOT_FOUND = "Payment not found"
  PAYMENT_WEBHOOK_UPDATE_SUCCESS = "Payment updated from webhook"
  PAYMENT_WEBHOOK_UPDATE_FAILED = "Failed to update payment from webhook"
  PAYMENTS_RETRIEVED = "Payments retrieved successfully"
  PAYMENTS_RETRIEVAL_FAILED = "Failed to retrieve payments"
  PAYMENT_RETRIEVED = "Payment details retrieved"
  PAYMENT_RETRIEVAL_FAILED = "Failed to retrieve payment details"
  PAYMENT_REFUNDED = "Payment refunded successfully"
  PAYMENT_REFUND_FAILED = "Failed to refund payment"
  PAYMENT_CANNOT_REFUND = "Payment cannot be refunded"
  RECIPIENT_NO_PAYMENT_ACCOUNT = "Recipient does not have a payment account or is not verified"
  RECIPIENT_ACCOUNT_INVALID = "Recipient's payment account is not properly configured"
  RECIPIENT_PAYOUTS_DISABLED = "Recipient's account does not have payouts enabled"
  PAYMENT_CUSTOMER_CREATION_FAILED = "Failed to create or retrieve customer for payment"
  INVALID_PAYMENT_METHOD = "Invalid or non-existent payment method"
  PAYMENT_METHOD_ATTACHED_TO_ANOTHER_CUSTOMER = "Payment method is attached to another customer"
  PAYMENT_METHOD_ATTACHMENT_FAILED = "Failed to attach payment method to customer"
  PAYMENT_METHOD_HANDLING_FAILED = "Unexpected error while handling payment method"
  PAYMENT_CARD_ERROR = "Card error while processing payment"
  PAYMENT_RECORD_CREATION_FAILED = "Failed to create payment record"

  INVITATION_SENT = "Invitation sent successfully"
  INVITATION_SEND_FAILURE = "Failed to send invitation"
  INVITATION_RESPONDED = "Invitation response recorded successfully"
  INVITATION_RESPONSE_FAILURE = "Failed to respond to invitation"
  INVITATION_CANCELED = "Invitation canceled successfully"
  INVITATION_CANCEL_FAILURE = "Failed to cancel invitation"
  INVITATION_UNAUTHORIZED_RESPONSE = "You are not authorized to respond to this invitation"
  INVITATION_UNAUTHORIZED_CANCEL = "You are not authorized to cancel this invitation"
  INVITATION_ALREADY_RESPONDED = "This invitation has already been responded to"
  INVITATION_NOT_FOUND = "Invitation not found"
  INVITATION_EXPIRED = "This invitation has expired"
  INVITATION_CANCELLED = "This invitation has been cancelled"
  INVITATION_ALREADY_STARTED = "This invitation cannot be canceled as it has already started"

  STRIPE_CONNECT_ALREADY_EXISTS = "Stripe Connect account already exists"
  STRIPE_CONNECT_CREATE_SUCCESS = "Stripe account created successfully"
  STRIPE_CONNECT_CREATE_FAILURE = "Error while creating stripe account"
  STRIPE_CONNECT_NO_ACCOUNT = "No Stripe Connect account found"
  STRIPE_CONNECT_GENERATE_LINK_SUCCESS = "Stripe connect link generated"
  STRIPE_CONNECT_GENERATE_LINK_FAILURE = "Error while creating connect link"
  STRIPE_CONNECT_ACCOUNT_VERIFY_STATUS = "Stripe connect account verify status"
  STRIPE_CONNECT_ACCOUNT_VERIFY_FAILURE = "Error while verifying stripe connect account"

  DISCOVERY_GET_USERS_SUCCESS = "Successfully retrieved discoverable users"
  DISCOVERY_GET_USERS_FAILURE = "Failed to retrieve discoverable users"
  DISCOVERY_USER_LOCATION_NOT_AVAILABLE = "User location not available for discovery"
  DISCOVERY_USER_INVALID_GENDER = "Invalid user gender for discovery"
  DISCOVERY_NO_USERS_FOUND = "No users found matching current criteria"

  PROFILE_GET_ME_SUCCESS = "Profile data retrieved successfully"

  TOKEN_REFRESH_SUCCESS = "Auth token refreshed successfully"

  WALLET_INFO_SUCCESS = "Wallet info retrieved successfully"
  WALLET_INFO_FAILURE = "Error while retrieving wallet information"

  BANK_INFO_SUCCESS = "Bank info retrieved successfully"
  BANK_INFO_GET_FAILURE = "Error while retrieving bank information"
  BANK_INFO_UPDATE_FAILURE = "Error while updating bank information"

  SUBSCRIPTION_PREPARE_SUCCESS = "Subscription prepared successfully"
  SUBSCRIPTION_PREPARE_FAILURE = "Error while preparing subscription checkout"
  SUBSCRIPTION_CREATE_SUCCESS = "Subscription created successfully"
  SUBSCRIPTION_CREATE_FAILURE = "Error while processing subscription"
  SUBSCRIPTION_CANCEL_SCHEDULE_SUCCESS = (
    "Subscription scheduled for cancellation at the end of billing period"
  )
  SUBSCRIPTION_CANCEL_IMMEDIATE_SUCCESS = "Subscription cancelled immediately"
  SUBSCRIPTION_CANCEL_FAILURE = "Error while canceling subscription"
  SUBSCRIPTION_WEBHOOK_UPDATE_SUCCESS = "Subscription updated from webhook"
  SUBSCRIPTION_WEBHOOK_UPDATE_FAILURE = "Error during subscription update"
  SUBSCRIPTION_GET_ACTIVE_SUCCESS = "Active subscription found"
  SUBSCRIPTION_GET_ACTIVE_FAILURE = "Error while getting active subscription"
  SUBSCRIPTION_ALREADY_ACTIVE = "You have an active subscription in our payment system. Please contact support if you're having issues accessing your subscription"
  SUBSCRIPTION_PLAN_NOT_FOUND = "Subscription plan not found"

  USER_NOT_FOUND = "User not found"

  INTERESTS_UPDATED = "Interests updated successfully"

  PAYMENT_METHOD_GET_FAILURE = "Error while retrieving payment methods"

  STRIPE_CHECKOUT_SESSION_SUCCESS = "Stipe checkout session created successfully"
  STRIPE_CHECKOUT_SESSION_FAILURE = "Stipe checkout session creation failure"
  STRIPE_CONNECT_ONBOARD_SUCCESS = "Stripe connect account created successfully"
  STRIPE_CONNECT_ONBOARD_FAILURE = "Failure while creating stripe connect account"

  SPARK_CANNOT_SENT_TO_YOURSELF = "You cannot send a spark to yourself"
  SPARK_RESPONDED = "You have responded to this spark"
  SPARK_RESPONDED_FAILURE = "Error while responding to spark"
  SPARK_ALREADY_SENT = "You have already sent a spark to this user"
  SPARK_NOT_YOURSELF = "Cannot send spark to yourself"
  SPARK_NO_PENDING = "No pending spark found from this user to respond to"
  SPARK_NOT_ENOUGH_BACK = "You don't have enough sparks to send one back"
  SPARK_NOT_ENOUGH = "Not enough sparks"
  SPARK_INTERNAL_ERROR = "An error occurred while sending the spark"
  SPARK_SENT = "Spark has been sent."
  SPARK_SENT_FAILURE = "Error while sending spark"
  SPARK_STATUS_INVALID = "Status must be either 0 (ignore) or 1 (accept)"

  # User feedback messages
  INVALID_INPUT_DATA = "Invalid input data"
  USER_NOT_FOUND_WITH_NUMBER = ("No user found with the provided mobile number",)
  UPDATE_SUCCESS = "Data updated successfully."
  NOT_UPDATE = "Data could not be updated."
  WRONG_USER = "The provided QR code is invalid."
  MEETING_END = "Your meeting has ended."
  MEETING_STARTED = "Your meeting has started."
  BID_CANCEL_SUCCESSFULLY = "Bid canceled successfully."
  QR_CODE_GENERATED = "QR code has been generated for female users."
  USER_ALREADY_BLOCKED = "This user is already blocked."
  NO_BLOCK_USERS = "There are no blocked users."
  CHAT_DELETE = "Chat deleted successfully."
  CHAT_NOT_DELETE = "Chat could not be deleted."
  NO_USER_TO_CHAT = "No users are available to chat with you."
  ACCOUNT_DELETED = "Account deleted successfully."
  ACCOUNT_NOT_DELETED = "Account could not be deleted."
  EMAIL_EXISTS = "This email address is already registered. Please use a different email."
  VERIFY_YOUR_MOBILE_NUMBER = "Please verify your account with the OTP."
  NOT_VERIFY_ACCOUNT = "Your account has not been verified. Please verify your account first."
  SIGN_UP_SUCCESS = "Sign-up successful! Welcome to our application."
  SIGN_UP_ERROR = "Sign-up failed. Please try again."
  LOGIN_SUCCESSFULLY = "You have successfully logged in."
  IN_VALID_PASSWORD = "The password you entered is incorrect."
  IN_VALID_EMAIL = "Email address not found. Please enter a valid email address."
  PWD_RESET_LINK = "Password reset link sent successfully. Please check your email."
  INVALID_LINK = "The link is invalid."
  ERR = "An internal server error occurred."
  USER_NOT_REGISTER = "This user is not registered."
  # USER_NOT_FOUND = "User not found. Please sign up first."
  PWD_NOT_MATCH = "Password and confirm password do not match."
  PWD_RESET_SUCCESSFULLY = "Your password has been reset successfully."
  INTERNAL_ERROR = "An internal error occurred. Please contact support."
  REGISTER_SUCCESS = "Registration completed successfully."
  REGISTER_ERROR = "Registration failed. Please try again."
  WELCOME = "Welcome to our application!"
  SUCCESS = "Operation completed successfully."
  OTP_VERIFIED = "OTP verified successfully."
  WRONG_OTP = "The OTP you entered is incorrect."
  ALREADY_OTP_VERIFIED = "OTP has already been verified."
  DATA_FOUND = "Data retrieved successfully."
  DATA_NOT_FOUND = "No data found."
  OTP_SEND = "OTP sent successfully."
  OTP_NOT_SEND = "OTP could not be sent."
  LOGGED_IN = "Logged in successfully."
  IN_VALID_PHONE = "The phone number you entered is invalid."
  PHONE_REGISTER_SUCCESS = "Phone number registered successfully."
  PHONE_REGISTER_ERROR = "This phone number is already registered."
  PHONE_NOT_REGISTER = "The phone number is not registered."
  AMOUNT_DEPOSIT_SUCCESS = "Amount deposited successfully."
  AMOUNT_NOT_DEPOSIT = "Amount could not be deposited."
  PASSWORD_GENERATED = "Please generate your password."
  PASSWORD_GENERATED_SUCCESSFULLY = "Password generated successfully."
  PASSWORD_NOT_GENERATED = "Password could not be generated."
  USER_PROFILE_FOUND_SUCCESS = "User profile retrieved successfully."
  USER_PROFILE_NOT_FOUND = "User profile could not be found."
  PASSWORD_RESET_SUCCESSFULLY = "Password has been reset successfully."
  PASSWORD_NOT_RESET = "Password could not be reset."
  SUB_ADMIN_NOT_EXISTS = "Sub-admin does not exist."
  PWD_NOT_CHANGE = "Password could not be changed."
  PWD_CHANGE_SUCCESSFULLY = "Password changed successfully."
  ADMIN_CAN_ACCESS = "Access restricted to admin users only."
  VERIFIED_PHONE_NUMBER = "Phone number verified successfully."
  ALREADY_VERIFIED_NUMBER = "Phone number has already been verified."
  PHONE_NUMBER_NOT_VERIFIED = "Phone number has not been verified."
  USER_REGISTER_BY_SUB_ADMIN = "User successfully created by sub-admin."
  SUB_ADMIN_LIST_FOUND_SUCCESSFULLY = "Sub-admin list retrieved successfully."
  SUB_ADMIN_LIST_NOT_FOUND = "No sub-admins found."
  USER_LIST_FOUND_SUCCESSFULLY = "User list retrieved successfully."
  USER_LIST_NOT_FOUND = "No users found."
  ID_REQUIRE = "Id must be require"

  # Game-related messages
  GAME_CREATED_SUCCESSFULLY = "Game created successfully."
  GAME_NOT_CREATED = "Game could not be created."
  GAME_REQUIRE = "Game information is required."
  GAME_EDITED_SUCCESSFULLY = "Game edited successfully."
  GAME_NOT_FOUND = "Game not found."
  GAME_DELETED_SUCCESSFULLY = "Game deleted successfully."
  GAME_NOT_DELETED = "Game could not be deleted."
  GAME_LIST_FOUND = "Game list retrieved successfully."
  NOT_PERMISSION_TO_CREATE_GAME = "You do not have permission to create games."
  NOT_PERMISSION_TO_EDIT = "You do not have permission to edit games."
  NOT_PERMISSION_TO_DELETE = "You do not have permission to delete games."
  NOT_PERMISSION_TO_VIEW_GAME_LIST = "You do not have permission to view the game list."

  # User management messages
  USER_NOT_CREATED_BY_SUB_ADMIN = "User could not be created by sub-admin."
  PROFILE_UPDATED = "Profile updated successfully."
  PROFILE_NOT_UPDATED = "Profile could not be updated."
  PWD_AND_CNF_PWD_NOT_MATCH = "Password and confirm password do not match."
  USER_NOT_EXISTS = "User does not exist."
  NO_FEMALE_USERS = "No female users found."
  YOU_HAVE_NO_PERMISSION = "Access is restricted to male users only."
  USER_UPDATE_SUCCESSFULLY = "User updated successfully."
  USER_NOT_UPDATE = "User update failed."
  GENDER_MUST_REQUIRE = "Gender is required."
  REQUIRE_FIELDS = "Both user ID and gender are required fields."

  # Image handling messages
  IMAGE_UPLOAD_SUCCESSFULLY = "Image uploaded successfully."
  IMAGE_NOT_UPLOADED = "Image could not be uploaded."
  IMAGE_DELETE_SUCCESSFULLY = "Image deleted successfully."
  IMAGE_NOT_DELETED = "Image could not be deleted."
  IMAGE_UPLOAD_SUCCESS = "Images uploaded successfully."
  IMAGE_UPLOAD_FAILURE = "Images could not be uploaded."

  # Data operation messages
  DATA_INSERT_SUCCESS = "Data inserted successfully."
  DATA_NOT_INSERT = "Data could not be inserted."
  NOT_REQUEST = "This user has no requests."
  STATUS_NOT_UPDATE = "Status could not be updated."

  # Activity messages
  ACTIVITY_CREATE_SUCCESSFULLY = "Activity created successfully."
  ACTIVITY_NOT_CREATE = "Activity could not be created."
  ACTIVITY_UPDATE_SUCCESSFULLY = "Activity updated successfully."
  ACTIVITY_NOT_UPDATE = "Activity could not be updated."
  ACTIVITY_DELETE_SUCCESSFULLY = "Activity deleted successfully."
  ACTIVITY_NOT_DELETED = "Activity could not be deleted."

  # Rating messages
  RATING_ADD = "Rating added successfully."
  RATING_NOT_ADD = "Rating could not be added."
  NO_RATING = "No ratings found for this user."

  # User relations messages
  BLOCKED_ADD = "This user has been blocked successfully."
  UN_BLOCKED = "This user has been unblocked successfully."
  NOT_BLOCKED = "This user is not blocked."
  OLD_USER = "This is an existing user."
  NEW_USER = "This is a new user."
  NO_BANK_INFO = "This user has no bank information."

  # Verification messages
  VERIFICATION_SENT = "Verification sent successfully."
  VERIFICATION_FAILED = "Failed to send verification."
  REQUIRED_DETAILS = "Hourly, dinner, and fullday Price are required."
  USER_ID_REQUIRED = "User ID is required."

  # Notification messages
  NOTIFICATION_FAILED = "Failed to send notification"

  # Bid-related messages
  BID_ID_REQUIRE = "Bid ID is required"
  BID_CANCEL_NOT_ALLOWED = "This user has accepted the invite, so the bid cannot be cancelled."
  DATA_NOT_UPDATE = "Data not update"
  ID_NOT_EXISTS = "This id not exists"

  # Email messages
  EMAIL_SEND_FAILURE = "Failed to send email."
  EMAIL_SEND_SUCCESS = "Email sent successfully."
  EVERYTHING_OK = "Everything is ok."

  # Friend request messages
  REQUEST_PENDING = "Your request has been pending."
  ALREADY_FRIEND = "He is already your friend."
  LIKE_REQUEST_SENT = "Like request has been sent."
  USER_ID_AND_INVITE_ID_REQUIRED = "User ID and Invite ID are required."

  # Subscription messages
  SUBSCRIPTION_NOT_CONFIGURED = "The subscription has not been configured by the admin."
  CREATION_SUCCESS = "Successfully created."
  CREATION_FAILED = "Failed."
  NO_ACTIVE_SUBSCRIPTION = "No active subscription found for this user."
  NO_SUBSCRIPTION = "No subscription found for this user."
  SUBSCRIPTION_EXPIRED = "Your Subscription has expired."
  SUBSCRIPTION_ACTIVE = "Your Subscription is still active."

  # Spark-related messages
  SPARK_SEND_FIELDS_REQUIRED = "spark_send_from and spark_send_to are required."
  NOT_ENOUGH_SPARKS = "You don't have enough sparks."
  SPARK_SENT = "Spark has been sent."
  ADD_BANK_INFO_FIRST = "Please add bank information first."
  ADD_CARD_DETAILS_FIRST = "Please add card details first, then you can proceed with the bid."
  SEND_SPARK_FIRST = "If you want to place a bid, you must first send a spark."
  NOT_FRIEND = "This user is not your friend, so you cannot place a bid."
  INVITE_SUCCESS = "Invite successfully."
  MEMBERSHIP_REQUIRED = "Membership required to continue chatting. Please purchase a membership!"
  TIME_REACH_OUT = "Time Reach Out"
  SPARK_NOT_MATCHED = "Your spark not be matched."
  FCM_TOKEN_UNREGISTERED = "Notification not sent, FCM token is unregistered."

  # Account management messages
  USER_ID_AND_OTP_REQUIRED = "User ID and OTP are required."
  ERROR_DELETING_ACCOUNT = "Error in deleting account"
  INVALID_OTP = "Invalid OTP"

  # Interest messages
  INTEREST_ID_REQUIRED = "Interest id is required."
  INTERESTED_ADDED_SUCCESSFULLY = "Interested Added Successfully"

  # Payment and transfer messages
  NAME_AND_AMOUNT_REQUIRED = "Name and amount must be required."
  TRANSFER_CREATED_SUCCESSFULLY = "Transfer created successfully!"
  TRANSFER_CREATION_FAILED = "Transfer creation failed!"
  FIELDS_REQUIRED = "paid_to, received_from, activity, and amount are required"
  INSUFFICIENT_BALANCE = "You have insufficient balance"
  INSUFFICIENT_FUNDS = "You have insufficient funds"
  ADD_FUNDS_TO_WALLET = "Please Add Fund In Your Wallet"

  # Bank details messages
  BANK_DETAILS_UPDATED_SUCCESSFULLY = "Bank details updated successfully"
  FAILED_TO_UPDATE_BANK_DETAILS = "Failed to update bank details"
  BANK_DETAILS_NOT_FOUND = "Bank details not found"
  USER_ID_AND_AMOUNT_REQUIRED = "user_id and amount must be required"
  TRANSFER_FAILED = "Transfer failed!"
  INCOMPLETE_BANK_INFORMATION = "Your Bank Information is incomplete"
  RECEIVER_ID_REQUIRED = "receiver_id must be required"
  USER_ID_AND_ALL_FIELDS_REQUIRED = "user_id and all fields must be required"
  BANK_DETAIL_ADDED_SUCCESSFULLY = "Bank detail added successfully"
  BANK_DETAIL_NOT_ADDED = "Bank detail not added"
  FIRST_ADD_BANK_INFO = "Firstly Add Bank Information"

  # Card and payment method messages
  SETUP_INTENT_CREATED_SUCCESSFULLY = "Setup intent created successfully"
  CARD_DETAILS_FETCHED_SUCCESSFULLY = "Card details fetched successfully"
  NO_CARD_FOUND = "No card found for this customer."
  AMOUNT_SHOULD_BE_POSITIVE = "Amount should be a positive number."
  NO_SAVED_PAYMENT_METHOD = "No saved payment method found!"
  RECIPIENT_BANK_INFO_MISSING = "Recipient bank information is missing or incomplete."
  PAYMENT_TRANSFER_INITIATED_SUCCESSFULLY = "Payment transfer initiated successfully"
  ERROR_INITIATING_PAYMENT_TRANSFER = "Error initiating payment transfer"
  CARD_ID_REQUIRED = "Card id is required"
  CARD_DELETED_SUCCESSFULLY = "Card deleted successfully"

  # Admin messages
  ADMIN_LOGIN_SUCCESS = "Admin login successful!"
  INVALID_PASSWORD = "Invalid password."
  ACCOUNT_NOT_FOUND = "Account not found. Please check your details."
  INTERNAL_SERVER_ERROR = "An internal server error occurred. Please try again later."
  DATA_FOUNDED = "Data found successfully"
  DATA_NOT_FOUND = "Data Not Found"
  PASSWORD_CHANGED = "Password changed successfully"

  # WebSocket Session Messages
  WS_SESSION_ID_REQUIRED = "Session ID is required"
  WS_SESSION_UNAUTHORIZED = "You are not authorized to access this session"
  WS_SESSION_NOT_FOUND = "Session not found"
  WS_SESSION_CONNECTED = "Successfully connected to this session"
  WS_SESSION_DISCONNECTED = "Disconnected from session"
  WS_USER_DISCONNECTED_FROM_SESSION = "User {user_id} disconnected from session {session_id}"
  WS_INVALID_MESSAGE_FORMAT = "Invalid message format: {error}"
  WS_UNKNOWN_MESSAGE_TYPE = "Unknown message type: {msg_type}"
  WS_ERROR_PROCESSING_MESSAGE = "Error processing message: {error}"
  WS_SESSION_STARTED_BY_USER = "Session {session_id} started by {user_id}"
  WS_SESSION_ACTIVATED = "Session {session_id} activated"
  WS_SESSION_PAUSED_BY_USER = "Session {session_id} paused by {user_id}"
  WS_SESSION_RESUMED_BY_USER = "Session {session_id} resumed by {user_id}"
  WS_SESSION_ENDED_BY_USER = "Session {session_id} ended by {user_id}"
  WS_USER_LEFT_SESSION_EARLY = "User {user_id} left session {session_id} early"
  WS_EMERGENCY_REPORTED_BY_USER = "Emergency in session {session_id} reported by {user_id}"
  WS_SESSION_UPDATED = "Session {session_id} updated"
  WS_SESSION_SETTINGS_UPDATED = "Session settings for {session_id} updated"
  WS_ERROR_IN_EVENT_HANDLER = "Error in {event_type} handler: {error}"

  # WebSocket Auto-Start Handler Messages
  WS_AUTO_START_BOTH_CONNECTED = "Both users connected for session {session_id}. Attempting to start session."
  WS_AUTO_START_SUCCESS = "Session {session_id} started successfully by auto-logic."
  WS_AUTO_START_MSG_FOR_CLIENT = "Session started automatically as both users are connected."
  WS_AUTO_START_FAILURE = "Failed to auto-start session {session_id}. Reason: {reason}"
  WS_AUTO_START_SESSION_NOT_FOUND = "Session {session_id} not found during auto-start logic."
  WS_AUTO_START_ERROR = "Error during auto-start logic for session {session_id}: {error}"
  FAILED_PASSWORD_CHANGED = "Password change failed. Please try again later."
  PASSWORD_DOESNT_MATCH = "Passwords do not match."
  INCORRECT_CURRENT_PASSWORD = "Current password is incorrect."
  NOTIFICATION_FOUNDED = "Notification found successfully."
  NOTIFICATION_NOT_FOUND = "Notification not found."
  ALREADY_NUMBER_REGISTER = "This mobile number is already registered."
  ID_REQUIRED = "id must be require."
  ADMIN_NOT_FOUND = "Admin not found"
  ERROR_DELETE_ACCOUNT = "Error in deleting account"
  EMAIL_REQUIRED = "Email must be required"
  PASSWORD_RESET_LINK_SENT = "Password reset link sent successfully. Please check your email "
  EMAIL_NOT_FOUND_PLEASE_CHECK = "Email address not found. Please enter a valid email "
  PASSWORD_C_PASSWORD_DONT_MATCH = "Password and Confirm Password do not match "

  # Commission and content messages
  COMMITION_ADDED = "Commission added successfully"
  COMMITION_NOT_ADDED = "Commission not added"
  CONTENT_ADDED = "content added successfully"
  CONTENT_NOT_ADDED = "content not added "

  # Session Resume Handler
  WS_RESUME_SESSION_NOT_PAUSED = "Session is not paused. Current state: {state}"
  WS_RESUME_SESSION_NOT_PAUSER = "Only the user who paused the session can resume it."
  WS_RESUME_SESSION_LOG_SUCCESS = "Session {session_id} resumed by user {user_id}"
  WS_RESUME_SESSION_LOG_ERROR = "Error resuming session {session_id} in DB: {error}"
  WS_RESUME_SESSION_UNEXPECTED_ERROR = "An unexpected error occurred while resuming the session."
  WS_RESUME_SESSION_MSG = "Session has been resumed"
  WS_RESUME_SESSION_HANDLER_ERROR = "Error in handle_resume_session for session {session_id}: {error}"
  WS_RESUME_SESSION_ERROR_MSG = "Error resuming session: {error}"

  # Session Emergency Handler
  WS_EMERGENCY_INVALID_STATE = "Cannot report emergency from its current state: {state}"
  WS_EMERGENCY_LOG_SUCCESS = "Emergency reported for session {session_id} by user {user_id}"
  WS_EMERGENCY_LOG_ERROR = "Error reporting emergency for session {session_id} in DB: {error}"
  WS_EMERGENCY_UNEXPECTED_ERROR = "An unexpected error occurred while reporting the emergency."
  WS_EMERGENCY_MSG = "Emergency reported during session"
  WS_EMERGENCY_HANDLER_ERROR = "Error in handle_report_emergency for session {session_id}: {error}"
  WS_EMERGENCY_ERROR_MSG = "Error reporting emergency: {error}"

  # Session Pause Handler
  WS_PAUSE_SESSION_INVALID_STATE = "Session cannot be paused from its current state: {state}"
  WS_PAUSE_SESSION_LOG_SUCCESS = "Session {session_id} paused by user {user_id}"
  WS_PAUSE_SESSION_LOG_ERROR = "Error pausing session {session_id} in DB: {error}"
  WS_PAUSE_SESSION_UNEXPECTED_ERROR = "An unexpected error occurred while pausing the session."
  WS_PAUSE_SESSION_MSG = "Session has been paused"
  WS_PAUSE_SESSION_HANDLER_ERROR = "Error in handle_pause_session for session {session_id}: {error}"
  WS_PAUSE_SESSION_ERROR_MSG = "Error pausing session: {error}"

  # Session Leave Early Handler
  WS_LEAVE_EARLY_INVALID_STATE = "Session cannot be left from its current state: {state}"
  WS_LEAVE_EARLY_LOG_SUCCESS = "Session {session_id} terminated early by user {user_id}"
  WS_LEAVE_EARLY_LOG_ERROR = "Error terminating session {session_id} early in DB: {error}"
  WS_LEAVE_EARLY_UNEXPECTED_ERROR = "An unexpected error occurred while leaving the session."
  WS_LEAVE_EARLY_MSG = "User {user_full_name} has left the session early."
  WS_LEAVE_EARLY_HANDLER_ERROR = "Error in handle_leave_early for session {session_id}: {error}"
  WS_LEAVE_EARLY_ERROR_MSG = "Error leaving session: {error}"

  # Session End Handler
  WS_END_SESSION_NOT_PARTICIPANT = "You are not a participant in this session."
  WS_END_SESSION_INVALID_STATE = "Session cannot be ended from its current state: {state}"
  WS_END_SESSION_LOG_SUCCESS = "Session {session_id} marked as COMPLETED by user {user_id}"
  WS_END_SESSION_LOG_ERROR = "Error ending session {session_id} in DB: {error}"
  WS_END_SESSION_UNEXPECTED_ERROR = "An unexpected error occurred while ending the session."
  WS_END_SESSION_ENDED_MSG = "Session has ended."
  WS_END_SESSION_ERROR_MSG = "Error ending session: {error}"
  WS_END_SESSION_HANDLER_ERROR = "Error in handle_end_session for session {session_id}: {error}"

  # Generic WebSocket Messages
  WS_UNAUTHENTICATED = "User is not authenticated."

  # Wallet messages
  WALLET_ADDED = "Your wallet added successfully"
  CURRENT_BALANCE = "Your current balance is here "
  NOTIFICATION_SENT = "Notification Sent Successfully"

  # Interest validation messages
  INTEREST_REQUIRED = "Interest is required."
  INTEREST_MUST_CONTAIN = (
    "Interest must contain only alphabets and not numbers or special characters."
  )
  INTEREST_NAME_ALREADY = "Interest name already exists. Please try another interest name."
  INTEREST_ADDED = "Interested Added Successfully"
  INTEREST_FETCHED = "Interests fetched successfully."
