"""
Utility functions for handling notifications across the application.
"""

import logging
from typing import <PERSON><PERSON>, Any, Dict

from access.models import User

logger = logging.getLogger(__name__)


class NotificationHelpers:
  """
  Helper class for notification-related operations used across the application.
  """

  @staticmethod
  def determine_notification_recipient(
    action_initiator: User, user1: User, user2: User
  ) -> <PERSON><PERSON>[User, str]:
    """
    Determine who should receive the notification based on who initiated the action.

    Args:
        action_initiator: The user who initiated the action
        user1: First user involved in the interaction
        user2: Second user involved in the interaction

    Returns:
        Tuple of (notification_recipient, notification_sender_name)
    """
    # If the action initiator is user1
    if action_initiator.id == user1.id:
      return user2, action_initiator.full_name
    # If the action initiator is user2
    elif action_initiator.id == user2.id:
      return user1, action_initiator.full_name
    else:
      # This shouldn't happen if authorization is checked properly
      logger.warning(
        f"Action initiator {action_initiator.id} is neither user1 {user1.id} nor user2 {user2.id}"
      )
      # Default to notifying user1
      return user1, action_initiator.full_name

  @staticmethod
  def determine_invitation_participants(
    action_initiator: User, invitation_sender: User, invitation_recipient: User
  ) -> Tuple[User, str]:
    """
    Determine who should receive the notification for invitation-related actions.

    Args:
        action_initiator: The user who initiated the action
        invitation_sender: The user who sent the invitation
        invitation_recipient: The user who received the invitation

    Returns:
        Tuple of (notification_recipient, notification_sender_name)
    """
    return NotificationHelpers.determine_notification_recipient(
      action_initiator, invitation_sender, invitation_recipient
    )

  @staticmethod
  def determine_session_participants(
    action_initiator: User, session_host: User, session_guest: User
  ) -> Tuple[User, str]:
    """
    Determine who should receive the notification for session-related actions.

    Args:
        action_initiator: The user who initiated the action
        session_host: The host of the session
        session_guest: The guest of the session

    Returns:
        Tuple of (notification_recipient, notification_sender_name)
    """
    return NotificationHelpers.determine_notification_recipient(
      action_initiator, session_host, session_guest
    )

  @staticmethod
  def determine_spark_participants(
    action_initiator: User, spark_sender: User, spark_recipient: User
  ) -> Tuple[User, str]:
    """
    Determine who should receive the notification for spark-related actions.

    Args:
        action_initiator: The user who initiated the action
        spark_sender: The user who sent the spark
        spark_recipient: The user who received the spark

    Returns:
        Tuple of (notification_recipient, notification_sender_name)
    """
    return NotificationHelpers.determine_notification_recipient(
      action_initiator, spark_sender, spark_recipient
    )

  @staticmethod
  def determine_review_participants(
    action_initiator: User, reviewer: User, reviewed_user: User
  ) -> Tuple[User, str]:
    """
    Determine who should receive the notification for review-related actions.

    Args:
        action_initiator: The user who initiated the action
        reviewer: The user who wrote the review
        reviewed_user: The user who was reviewed

    Returns:
        Tuple of (notification_recipient, notification_sender_name)
    """
    return NotificationHelpers.determine_notification_recipient(
      action_initiator, reviewer, reviewed_user
    )

  @staticmethod
  def determine_payment_participants(
    action_initiator: User, payer: User, payee: User
  ) -> Tuple[User, str]:
    """
    Determine who should receive the notification for payment-related actions.

    Args:
        action_initiator: The user who initiated the action
        payer: The user who made the payment
        payee: The user who received the payment

    Returns:
        Tuple of (notification_recipient, notification_sender_name)
    """
    return NotificationHelpers.determine_notification_recipient(action_initiator, payer, payee)

  @staticmethod
  def prepare_notification_data(
    base_data: Dict[str, Any], action_initiator: User, notification_type: str, **additional_data
  ) -> Dict[str, Any]:
    """
    Prepare notification data with common fields.

    Args:
        base_data: Base data dictionary
        action_initiator: The user who initiated the action
        notification_type: Type of notification
        additional_data: Any additional data to include

    Returns:
        Complete notification data dictionary
    """
    data = {
      "initiator_id": str(action_initiator.id),
      "initiator_name": action_initiator.full_name,
      "notification_type": notification_type,
      **base_data,
    }

    # Add any additional data
    if additional_data:
      data.update(additional_data)

    return data
