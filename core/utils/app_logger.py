import logging
import traceback
from functools import wraps
from typing import Any, Dict, Optional

# TODO: Add sentry or other error logger service
# import sentry_sdk
# from sentry_sdk.integrations.django import DjangoIntegration
# from sentry_sdk.integrations.logging import LoggingIntegration


class AppLogger:
  """
  AppLogger provides consistent logging across the application with both
  local logging and Sentry integration.
  """

  # Singleton instance
  _instance = None

  def __new__(cls, *args, **kwargs):
    if cls._instance is None:
      cls._instance = super(AppLogger, cls).__new__(cls)
      cls._instance._initialized = False
    return cls._instance

  def __init__(
    self,
    app_name: str = "django-app",
    log_level: int = logging.INFO,
    sentry_dsn: Optional[str] = None,
  ):
    """
    Initialize the AppLogger

    Args:
        app_name: Name of the application for logger naming
        log_level: Default logging level
        sentry_dsn: Sentry DSN for error reporting
    """
    if self._initialized:
      return

    self.app_name = app_name
    self.log_level = log_level

    # Configure basic logging
    logging.basicConfig(
      level=log_level,
      format="%(asctime)s [%(levelname)s] %(name)s - %(message)s",
      datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Root logger
    self.logger = logging.getLogger(app_name)

    # Initialize Sentry if DSN is provided
    # if sentry_dsn:
    #   sentry_logging = LoggingIntegration(
    #     level=logging.INFO,  # Capture info and above as breadcrumbs
    #     event_level=logging.ERROR,  # Send errors as events
    #   )

    #   sentry_sdk.init(
    #     dsn=sentry_dsn,
    #     integrations=[
    #       DjangoIntegration(),
    #       sentry_logging,
    #     ],
    #     traces_sample_rate=0.2,  # Adjust based on your traffic
    #     send_default_pii=False,  # Avoid sending PII by default
    #   )

    self._initialized = True

  def get_logger(self, feature: str) -> logging.Logger:
    """
    Get a logger for a specific feature/module

    Args:
        feature: Feature or module name

    Returns:
        Logger instance for the feature
    """
    return logging.getLogger(f"{self.app_name}.{feature}")

  def log_info(self, feature: str, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """
    Log an info message

    Args:
        feature: Feature, module, or method name
        message: Log message
        extra: Additional data to include in the log
    """
    logger = self.get_logger(feature)

    # Prepare structured log data
    log_data = extra or {}

    # Log locally
    logger.info(message, extra=log_data)

    # Optionally add breadcrumb to Sentry
    # if sentry_sdk.Hub.current.client:
    #   sentry_sdk.add_breadcrumb(category=feature, message=message, level="info", data=log_data)

  def log_error(
    self,
    feature: str,
    message_or_exc: Any,
    exc: Optional[Exception] = None,
    extra: Optional[Dict[str, Any]] = None,
  ) -> None:
    """
    Log an error message and send to Sentry

    Args:
        feature: Feature, module, or method name
        message_or_exc: Error message string or Exception object
        exc: Exception object if available (optional if message_or_exc is Exception)
        extra: Additional data to include in the log
    """
    logger = self.get_logger(feature)

    # Prepare structured log data
    log_data = extra or {}

    # Determine if message_or_exc is an Exception
    if isinstance(message_or_exc, Exception):
      message = f"Exception occurred: {message_or_exc}"
      exc = message_or_exc
    else:
      message = message_or_exc

    if exc:
      log_data["exception"] = str(exc)
      log_data["traceback"] = traceback.format_exc()

    # Log locally
    RED = "\033[91m"
    RESET = "\033[0m"

    logger.error(f"{RED}{message}{RESET}", exc_info=exc is not None, extra=log_data)

    # Send to Sentry if available
    # if sentry_sdk.Hub.current.client:
    #   with sentry_sdk.push_scope() as scope:
    #     # Add extra context
    #     for key, value in log_data.items():
    #       scope.set_extra(key, value)

    #     scope.set_tag("feature", feature)

    #     if exc:
    #       sentry_sdk.capture_exception(exc)
    #     else:
    #       sentry_sdk.capture_message(message, level="error")

  def log_warning(self, feature: str, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """
    Log a warning message

    Args:
        feature: Feature, module, or method name
        message: Warning message
        extra: Additional data to include in the log
    """
    logger = self.get_logger(feature)

    # Prepare structured log data
    log_data = extra or {}

    # Log locally
    logger.warning(message, extra=log_data)

    # Add breadcrumb to Sentry
    # if sentry_sdk.Hub.current.client:
    #   sentry_sdk.add_breadcrumb(category=feature, message=message, level="warning", data=log_data)

  def log_debug(self, feature: str, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """
    Log a debug message

    Args:
        feature: Feature, module, or method name
        message: Debug message
        extra: Additional data to include in the log
    """
    logger = self.get_logger(feature)
    logger.debug(message, extra=extra or {})

  def log_method(self, feature: Optional[str] = None):
    """
    Decorator to log method entry and exit, including timing and exceptions

    Args:
        feature: Override feature name (defaults to class.method)

    Returns:
        Decorated function
    """

    def decorator(func):
      @wraps(func)
      def wrapper(*args, **kwargs):
        # Determine feature name
        if feature:
          feat_name = feature
        else:
          if hasattr(args[0].__class__, "__name__") and func.__name__:
            feat_name = f"{args[0].__class__.__name__}.{func.__name__}"
          else:
            feat_name = func.__name__

        # Log method entry
        self.log_debug(feat_name, "Method started")

        try:
          # Call the original method
          result = func(*args, **kwargs)
          # Log method exit
          self.log_debug(feat_name, "Method completed successfully")
          return result
        except Exception as e:
          # Log the exception
          self.log_error(feat_name, f"Method failed: {str(e)}", exc=e)
          raise

      return wrapper

    return decorator


# Initialize helpers
def init_logger(app_name: str = "django-app", sentry_dsn: Optional[str] = None) -> AppLogger:
  """
  Initialize the application logger

  Args:
      app_name: Name of the application
      sentry_dsn: Sentry DSN for error reporting (optional)

  Returns:
      Configured AppLogger instance
  """
  # Use environment variables as fallback
  # if not sentry_dsn:
  #   sentry_dsn = os.environ.get("SENTRY_DSN")

  return AppLogger(app_name=app_name, sentry_dsn=None)
