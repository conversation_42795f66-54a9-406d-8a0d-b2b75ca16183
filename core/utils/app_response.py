from rest_framework.exceptions import APIException, status

from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination


class BadRequest(APIException):
  status_code = status.HTTP_400_BAD_REQUEST
  default_detail = {"error", "Bad request"}
  default_code = "bad_request"


class Conflict(APIException):
  status_code = status.HTTP_409_CONFLICT
  default_detail = {"error", "Conflict"}
  default_code = "conflict"


class ServerError(APIException):
  status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
  default_detail = {"error", "Server error"}
  default_code = "server_error"


class ServiceUnavailable(APIException):
  status_code = status.HTTP_503_SERVICE_UNAVAILABLE
  default_detail = {"error", "Service unavailable"}
  default_code = "service_unavailable"


class AppPagination(PageNumberPagination):
  """
  Custom pagination class that works with AppResponse
  """

  page_size = 50
  page_size_query_param = "page_size"
  max_page_size = 100

  def get_paginated_response(self, data):
    """
    Override to use AppResponse format
    """
    return AppResponse.paginated_success(
      data=data,
      count=self.page.paginator.count,
      next_page=self.get_next_link(),
      previous_page=self.get_previous_link(),
      message="Results retrieved successfully",
    )


# TODO: For testing only
class AppChatPagination(AppPagination):
  page_size = 10


class AppMapPagination(AppPagination):
  page_size = 500


class AppStatus:
  ERROR = "error"
  SUCCESS = "success"


class AppResponse:
  """Utility class for standardized API responses"""

  @staticmethod
  def success(data=None, message="Operation successful", status=200):
    return Response(
      {
        "status": True,
        "message": message,
        "data": data or {},
        "errors": None,
      },
      status=status,
    )

  @staticmethod
  def paginated_success(
    data=None,
    count=0,
    next_page=None,
    previous_page=None,
    message="Operation successful",
    status=200,
  ):
    """
    Success response that includes pagination metadata
    """
    return Response(
      {
        "count": count,
        "next": next_page,
        "previous": previous_page,
        "status": True,
        "message": message,
        "errors": None,
        "data": data or [],
      },
      status=status,
    )

  @staticmethod
  def error(message="An error occurred", errors=None, status=400):
    return Response(
      {
        "status": False,
        "message": message,
        "data": None,
        "errors": errors or {},
      },
      status=status,
    )
