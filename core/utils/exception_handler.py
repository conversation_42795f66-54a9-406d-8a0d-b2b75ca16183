# utils/exception_handler.py

from rest_framework.views import exception_handler
from rest_framework.exceptions import ValidationError

from core.utils.app_response import AppResponse
from core.utils.app_msg import AppMsg


def custom_exception_handler(exc, context):
  response = exception_handler(exc, context)

  if response is not None:
    # Special handling for ValidationError
    if isinstance(exc, ValidationError):
      # Check if it's a specific validation error we want to handle differently
      detail = exc.detail

      # Handle specific field errors for sparks and invitations
      if isinstance(detail, dict):
        # Check for recipient_id errors in SendSparkSerializer
        if "recipient_id" in detail and isinstance(detail["recipient_id"], list):
          error_msg = detail["recipient_id"][0]
          if error_msg in [
            AppMsg.SPARK_ALREADY_SENT,
            AppMsg.SPARK_NOT_YOURSELF,
            AppMsg.USER_NOT_FOUND,
          ]:
            return AppResponse.error(message=error_msg, status=response.status_code)

        # Check for sender_id errors in RespondSparkSerializer
        if "sender_id" in detail and isinstance(detail["sender_id"], list):
          error_msg = detail["sender_id"][0]
          if error_msg == AppMsg.USER_NOT_FOUND:
            return AppResponse.error(message=error_msg, status=response.status_code)

        # Check for status errors in RespondSparkSerializer
        if "status" in detail and isinstance(detail["status"], list):
          error_msg = detail["status"][0]
          if error_msg == AppMsg.SPARK_STATUS_INVALID:
            return AppResponse.error(message=error_msg, status=response.status_code)

        # Check for non-field errors
        if "non_field_errors" in detail and isinstance(detail["non_field_errors"], list):
          error_msg = detail["non_field_errors"][0]
          if error_msg in [AppMsg.SPARK_NO_PENDING]:
            return AppResponse.error(message=error_msg, status=response.status_code)

        # Handle invitation validation errors
        if "invitation_id" in detail and isinstance(detail["invitation_id"], list):
          error_msg = detail["invitation_id"][0]
          # Check for common invitation error messages
          if any(
            msg in error_msg
            for msg in [
              AppMsg.INVITATION_EXPIRED,
              AppMsg.INVITATION_CANCELLED,
              AppMsg.INVITATION_ALREADY_RESPONDED,
              AppMsg.INVITATION_NOT_FOUND,
            ]
          ):
            return AppResponse.error(message=error_msg, status=response.status_code)

        # Handle session_code validation errors
        if "session_code" in detail and isinstance(detail["session_code"], list):
          error_msg = detail["session_code"][0]
          return AppResponse.error(message=error_msg, status=response.status_code)

      # For profile updates and other form validations, include the validation details in the message
      if isinstance(detail, dict):
        # Extract first error message from the validation errors for the message
        first_field = next(iter(detail), None)
        if first_field and isinstance(detail[first_field], list) and len(detail[first_field]) > 0:
          error_detail = f"{first_field}: {detail[first_field][0]}"
          return AppResponse.error(
            message=error_detail, errors=response.data, status=response.status_code
          )

      # Default validation error handling (fallback)
      return AppResponse.error(
        message="Validation error", errors=response.data, status=response.status_code
      )

    # For other exceptions, use the original detail as message
    message = response.data.get("detail", "An error occurred")
    errors = response.data if "detail" not in response.data else None
    return AppResponse.error(message=message, errors=errors, status=response.status_code)

  # For unhandled exceptions
  return AppResponse.error(message=str(exc), status=500)
