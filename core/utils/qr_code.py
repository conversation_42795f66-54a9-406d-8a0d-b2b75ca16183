import qrcode
import io
import base64


class QRCodeGenerator:
  """Utility class for generating QR codes"""

  @staticmethod
  def generate_qr_code_base64(data: str, size: int = 10) -> str:
    """
    Generate a QR code as a base64 encoded string.

    Args:
        data: The data to encode in the QR code
        size: The size of the QR code in box units (default: 10)

    Returns:
        Base64 encoded string of the QR code image
    """
    qr = qrcode.QRCode(
      version=1,
      error_correction=qrcode.constants.ERROR_CORRECT_L,
      box_size=size,
      border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    # Convert the image to bytes
    buffer = io.BytesIO()
    img.save(buffer, format="PNG")

    # Convert to base64
    img_str = base64.b64encode(buffer.getvalue()).decode("utf-8")

    return f"data:image/png;base64,{img_str}"
