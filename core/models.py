import uuid
from django.utils.translation import gettext_lazy as _
from django.db import models


class AbstractModel(models.Model):
  id = models.UUIDField(
    _("id"),
    default=uuid.uuid4,
    primary_key=True,
    editable=False,
    unique=True,
  )

  created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
  updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

  class Meta:
    abstract = True
    ordering = ["-created_at"]
