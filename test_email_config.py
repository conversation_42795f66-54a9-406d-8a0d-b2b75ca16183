#!/usr/bin/env python
"""
Test script to verify email configuration and admin_emails functionality.
Run this script to diagnose email setup issues.
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rentie.settings')
django.setup()

from django.core.mail import send_mail
from django.contrib.auth import get_user_model
from services.email_service import get_email_service
from admin_emails.models import AdminEmail

User = get_user_model()

def test_django_email_settings():
    """Test Django email configuration"""
    print("=== Django Email Settings ===")
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"EMAIL_USE_TLS: {settings.EMAIL_USE_TLS}")
    print(f"EMAIL_USE_SSL: {settings.EMAIL_USE_SSL}")
    print(f"EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"EMAIL_HOST_PASSWORD: {'***' if settings.EMAIL_HOST_PASSWORD else '(empty)'}")
    print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print()

def test_email_service():
    """Test the email service"""
    print("=== Email Service Test ===")
    try:
        email_service = get_email_service()
        print(f"Email service initialized: {type(email_service).__name__}")
        print(f"Is configured: {email_service.is_configured()}")
        
        # Test simple email
        success, message = email_service.send_email(
            to_emails=['<EMAIL>'],
            subject='Test Email from Rentie',
            html_content='<h1>Test Email</h1><p>This is a test email.</p>'
        )
        print(f"Simple email test - Success: {success}, Message: {message}")
        
        # Test templated email
        success, message = email_service.send_templated_email(
            to_emails=['<EMAIL>'],
            template_name='admin_email.html',
            context={
                'subject': 'Test Admin Email',
                'message': 'This is a test admin email.',
                'template_type': 'custom'
            }
        )
        print(f"Templated email test - Success: {success}, Message: {message}")
        
    except Exception as e:
        print(f"Email service error: {e}")
        import traceback
        traceback.print_exc()
    print()

def test_django_send_mail():
    """Test Django's built-in send_mail"""
    print("=== Django send_mail Test ===")
    try:
        result = send_mail(
            subject='Django Test Email',
            message='This is a test email using Django send_mail.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False
        )
        print(f"Django send_mail result: {result}")
    except Exception as e:
        print(f"Django send_mail error: {e}")
        import traceback
        traceback.print_exc()
    print()

def test_admin_email_model():
    """Test AdminEmail model functionality"""
    print("=== AdminEmail Model Test ===")
    try:
        # Create a test admin email
        admin_email = AdminEmail()
        admin_email.subject = "Test Admin Email"
        admin_email.message = "<h1>Test</h1><p>This is a test admin email from the model.</p>"
        admin_email.template_choice = "custom"
        admin_email.custom_emails = "<EMAIL>"
        admin_email.send_copy_to_admin = False

        print(f"AdminEmail created: {admin_email}")
        print(f"Recipients: {admin_email.get_recipient_emails()}")

        # Test validation
        try:
            admin_email.clean()
            print("Validation passed")
        except Exception as e:
            print(f"Validation error: {e}")

        # Test sending
        success, message = admin_email.send_email()
        print(f"Send email result - Success: {success}, Message: {message}")

    except Exception as e:
        print(f"AdminEmail model error: {e}")
        import traceback
        traceback.print_exc()
    print()

def test_admin_form():
    """Test AdminEmailForm functionality"""
    print("=== AdminEmailForm Test ===")
    try:
        from admin_emails.admin import AdminEmailForm

        # Test form with custom emails
        form_data = {
            'subject': 'Test Form Email',
            'message': '<h1>Test</h1><p>This is a test from the form.</p>',
            'template_choice': 'custom',
            'custom_emails': '<EMAIL>, <EMAIL>',
            'send_copy_to_admin': False
        }

        form = AdminEmailForm(data=form_data)
        print(f"Form is valid: {form.is_valid()}")

        if not form.is_valid():
            print(f"Form errors: {form.errors}")
        else:
            print("Form validation passed")

            # Test creating object from form
            admin_email = form.save(commit=False)
            print(f"Created from form: {admin_email}")
            print(f"Recipients: {admin_email.get_recipient_emails()}")

    except Exception as e:
        print(f"AdminEmailForm error: {e}")
        import traceback
        traceback.print_exc()
    print()

def test_users_with_emails():
    """Check if there are users with email addresses"""
    print("=== Users with Email Addresses ===")
    try:
        users_with_emails = User.objects.filter(
            is_active=True
        ).exclude(email='').exclude(email__isnull=True)
        
        print(f"Total active users with emails: {users_with_emails.count()}")
        
        for user in users_with_emails[:5]:  # Show first 5
            print(f"  - {user.email} (ID: {user.id})")
            
        if users_with_emails.count() > 5:
            print(f"  ... and {users_with_emails.count() - 5} more")
            
    except Exception as e:
        print(f"User query error: {e}")
    print()

def main():
    """Run all tests"""
    print("Email Configuration Diagnostic Tool")
    print("=" * 50)

    test_django_email_settings()
    test_email_service()
    test_django_send_mail()
    test_admin_email_model()
    test_admin_form()
    test_users_with_emails()

    print("=" * 50)
    print("Diagnostic complete!")
    print("\nIf emails are not appearing in Mailhog:")
    print("1. Check if Mailhog is running: docker ps | grep mailhog")
    print("2. Check Mailhog web interface: http://localhost:8025")
    print("3. Check Docker logs: docker logs <mailhog_container_id>")
    print("4. Verify network connectivity between containers")

if __name__ == "__main__":
    main()
