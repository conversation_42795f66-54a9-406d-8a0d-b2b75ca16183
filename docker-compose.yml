services:
  web:
    build: .
    volumes:
      - .:/app
      - static_volume_rentie:/app/staticfiles
      - media_volume_rentie:/app/media
    ports:
      - "${PORT:-8081}:8000"
    environment:
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,web.rentie-backend-new.orb.local,127.0.0.1,0.0.0.0}
      - BASE_DOMAIN=${BASE_DOMAIN:-http://localhost:8081}
    env_file:
      - ./.env
    depends_on:
      - db
      - redis
      - mailhog
    restart: always
    command: ["/bin/bash", "entrypoint.sh"]

  cron:
    build:
      context: .
      dockerfile: cron.Dockerfile
    volumes:
      - .:/app
      - logs_volume_rentie:/var/log/cron
    env_file:
      - ./.env
    depends_on:
      - db
      - redis
      - mailhog
    restart: always
    command: ["/bin/bash", "cron_entrypoint.sh"]

  session_timer:
    build: .
    volumes:
      - .:/app
    env_file:
      - ./.env
    depends_on:
      - db
      - redis
    restart: always
    command: ["python", "manage.py", "send_session_timer_updates"]

  db:
    image: postgis/postgis:15-3.3
    volumes:
      - postgres_data_rentie:/var/lib/postgresql/data
    env_file:
      - ./.env
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_ENCODING=UTF8
    restart: always

  redis:
    image: redis:7-alpine
    restart: always
    volumes:
      - redis_data_rentie:/data

  mailhog:
    image: mailhog/mailhog
    restart: always
    ports:
      - 1025:1025
      - 8025:8025
    logging:
      driver: "none" # Disable logging to prevent spam

volumes:
  postgres_data_rentie:
  static_volume_rentie:
  media_volume_rentie:
  redis_data_rentie:
  logs_volume_rentie:
