#!/bin/bash

# Ensures the script stops immediately if any command fails (fail-fast).
set -e

# Set the Django settings module
export DJANGO_SETTINGS_MODULE=rentie.settings

echo "Waiting for postgres..."
while ! nc -z $DB_HOST $DB_PORT; do
  sleep 0.1
done
echo "PostgreSQL started"

# Enable PostGIS extension if not exists
echo "Enabling PostGIS..."
PGPASSWORD="$DB_PASSWORD" psql -U "$DB_USER" -h "$DB_HOST" -d "$DB_NAME" -c "CREATE EXTENSION IF NOT EXISTS postgis;"

echo "Waiting for redis..."
while ! nc -z $REDIS_HOST $REDIS_PORT; do
  sleep 0.1
done
echo "Redis started"

echo "Running migrations..."
python manage.py migrate

# Collects static files into STATIC_ROOT
echo "Collecting static files..."
python manage.py collectstatic --noinput

echo "Creating superuser..."
python commands/create_superuser.py

echo "Starting Daphne ASGI server..."
# Point Daphne to your ASGI application module and bind to 0.0.0.0:8000
# IMPORTANT: Replace 'rentie' with your actual Django project name if it's different
exec daphne rentie.asgi:application -b 0.0.0.0 -p 8000