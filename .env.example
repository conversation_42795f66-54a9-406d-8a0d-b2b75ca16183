DEBUG=True
SECRET_KEY=your-super-secret-key-change-in-production

# Database settings
DB_ENGINE=django.contrib.gis.db.backends.postgis
DB_NAME=rentie_db
DB_USER=rentie_user
DB_PASSWORD=rentie_password
DB_HOST=db
DB_PORT=5432

# Redis settings
REDIS_HOST=redis
REDIS_PORT=6379

# Stripe keys (replace with your actual keys in production)
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=

# Stripe product and price IDs for subscription plans
STRIPE_12M_PRODUCT_ID=
STRIPE_12M_PRICE_ID=

STRIPE_6M_PRODUCT_ID=
STRIPE_6M_PRICE_ID=

STRIPE_3M_PRODUCT_ID=
STRIPE_3M_PRICE_ID=

STRIPE_1M_PRODUCT_ID=
STRIPE_1M_PRICE_ID=

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=mailhog
EMAIL_PORT=1025
EMAIL_USE_TLS=False
EMAIL_USE_SSL=False
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
DEFAULT_FROM_EMAIL=<EMAIL>

# Mailhog Configuration (for local development)
MAILHOG_HOST=mailhog
MAILHOG_PORT=1025
MAILHOG_WEB_PORT=8025

# Site Settings for Email Templates
SITE_NAME=Rentie
SITE_URL=https://rentie.com
SUPPORT_EMAIL=<EMAIL>

# Twilio Configuration
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_VERIFY_SERVICE_ID=

# OTP Configuration
OTP_EXPIRY_MINUTES=5
OTP_MAX_ATTEMPTS=3
OTP_RESEND_INTERVAL_SECONDS=30
OTP_DAILY_LIMIT=10

# Allowed hosts
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,web.rentie-backend-new.orb.local

# Superuser credentials for initial setup
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_PASSWORD=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
