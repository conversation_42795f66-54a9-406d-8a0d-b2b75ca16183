# WebSocket API Documentation - Ren-Tie Backend

This document describes the WebSocket API for the Ren-Tie backend, covering real-time features like Chat and Online Status tracking.

## Introduction

The WebSocket API provides real-time communication capabilities. Clients connect to dedicated WebSocket endpoints to send and receive live updates and messages.

## Base URL

The base URL for all WebSocket connections is:

`ws://your-domain.com/ws/`

*(Replace `your-domain.com` with your actual domain name. For local development, this might be `ws://localhost:80/ws/` or similar.)*

## Authentication

Authentication is **required** to connect to any WebSocket channel that requires user identity.

1.  **Obtain a JWT Token:** First, authenticate with the REST API (e.g., using your `/api/auth/otp/verify/` endpoint) to obtain a JSON Web Token (JWT).
2.  **Include Token in Handshake Header:** When initiating the WebSocket connection, include the JWT in the `Authorization` header, formatted as `Bearer <your_jwt_token>`.

    **Example using `websocket-client` (applicable to any authenticated channel):**

    ```python
    import websocket
    import json

    auth_token = "your_obtained_jwt_token"
    # This URL will vary depending on the channel you connect to (e.g., chat, status)
    ws_url = "ws://your-domain.com/ws/some/channel/" # Replace with actual channel URL
    headers = {"Authorization": f"Bearer {auth_token}"}

    ws = websocket.WebSocketApp(
        ws_url,
        header=headers,
        on_open=lambda ws: print("Connection opened"),
        on_message=lambda ws, msg: print(f"Received: {msg}"),
        on_error=lambda ws, err: print(f"Error: {err}"),
        on_close=lambda ws, code, msg: print(f"Closed: {code} - {msg}"),
    )
    ws.run_forever()
    ```

    As shown in the consumer code, if authentication fails during the handshake (resulting in an `AnonymousUser`), the connection will be **closed immediately** by the server, often with a specific close code (e.g., 1008 - Policy Violation or a custom code like 4001).

## Channels

WebSocket interactions are organized into "channels," which correspond to specific endpoints and handle different types of real-time data.

### Chat Channel

*   **Endpoint:** `/chat/{other_user_id}/`
*   **Description:** Connects the authenticated user to a chat conversation channel with the user specified by `{other_user_id}`. Messages sent *from* the client to this channel are expected to be new text messages. The server will save these messages to the database and then broadcast them to both users in the conversation via the `chat_message` event. Messages received *by* the client on this channel are those exchanged within this specific conversation.
*   **Parameters:**
    *   `{other_user_id}`: **Required** (Path Parameter). The unique identifier (UUID) of the *other* user involved in the chat conversation with the authenticated user. This ID is used by the server to determine the specific conversation and group clients.

**Example Connection URL:**

`ws://your-domain.com/ws/chat/9c191f88-c1e3-46c2-a5d9-269315fc004f/`

*(This connects the *authenticated* user to a chat with the user whose ID is `9c191f88-c1e3-46c2-a5d9-269315fc004f`)*

### Online Status Channel

*   **Endpoint:** `/status/online/`
*   **Description:** Connects the authenticated user to a channel for tracking and broadcasting user online status. Clients on this channel can receive real-time updates when users come online or go offline, and can query the current status of specific users. A heartbeat mechanism is used on this channel to maintain presence.
*   **Parameters:** None (No path parameters required)

**Example Connection URL:**

`ws://your-domain.com/ws/status/online/`

## Message Formats

All communication over the WebSocket connection is done using JSON formatted messages. Messages typically include a `"type"` field to indicate their purpose.

Messages received from the server may also include `"code"`, `"message"`, and `"details"` fields, especially for `info` or `error` messages.

### Client -> Server Messages

These are messages sent *from* the connected client *to* the server. The consumer's `receive` method expects a specific format based on the message `"type"`.

#### Sending a Text Message (Chat Channel)

*   **Description:** Sends a new text message to the chat conversation associated with the connection.
*   **Target Channel:** `/ws/chat/{user_id}/`
*   **Expected Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): **Required**. Must be `"chat_message"`.
    *   `message` (string): **Required**. The content of the text message. Must be a non-empty string after trimming whitespace.

**Example Payload:**

```json
{
  "type": "chat_message",
  "message": "Hello from the client!"
}
```

#### Sending Online Status Messages (Online Status Channel)

*   **Target Channel:** `/ws/status/online/`

##### Sending a Heartbeat

*   **Description:** Sent periodically by the client to signal that the connection is still active. This helps the server determine if a user is genuinely online. If the server doesn't receive a heartbeat within a configured timeout (`HEARTBEAT_TIMEOUT`, 120 seconds in your code), it may consider the connection stale.
*   **Expected Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): **Required**. Must be `"heartbeat"`.

**Example Payload:**

```json
{
  "type": "heartbeat"
}
```

##### Querying User Online Status

*   **Description:** Requests the current online status of a list of specific users. The server will respond with a `user_online_response` message containing the status of the requested users.
*   **Expected Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): **Required**. Must be `"user_online_query"`.
    *   `user_ids` (list of strings): **Required**. A non-empty list of UUID strings representing the users whose online status you want to query.

**Example Payload:**

```json
{
  "type": "user_online_query",
  "user_ids": [
    "9c191f88-c1e3-46c2-a5d9-269315fc004f",
    "51ca3997-8a3f-4d6a-9faa-d043ab3f707e"
  ]
}
```

### Server -> Client Messages

These are messages sent *from* the server *to* the connected client. Clients should be prepared to receive various message types on any channel, although some types are channel-specific.

#### Chat Message Broadcast (Chat Channel)

*   **Description:** Sent by the server to inform clients in a chat conversation group about a new message. This is sent after a message is successfully saved to the database.
*   **Source:** Broadcast to the room group `/ws/chat/{user_id}/`.
*   **Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): Always `"chat_message"`.
    *   `sender` (string): The UUID of the user who sent the message.
    *   `message` (string): The content of the message.
    *   `message_id` (string): The UUID of the saved message in the database.

**Example Payload:**

```json
{
  "type": "chat_message",
  "sender": "your_user_id_uuid",
  "message": "Hello from the client!",
  "message_id": "some_message_uuid"
}
```

#### Online Status Updates (Online Status Channel)

*   **Description:** Sent by the server to inform clients in the online status group about a user's online status change (coming online or going offline). These are pushed proactively.
*   **Source:** Broadcast to the group `/ws/status/online/`.
*   **Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): Always `"user_online_status"`.
    *   `user_id` (string): The UUID of the user whose status has changed.
    *   `is_online` (boolean): `true` if the user is now online, `false` if offline.

**Example Payload:**

```json
{
  "type": "user_online_status",
  "user_id": "some_user_uuid",
  "is_online": true
}
```

#### Status Query Response (Online Status Channel)

*   **Description:** Sent by the server in response to a client's `user_online_query` message. Contains the online status of the requested users.
*   **Source:** Sent directly back to the client who sent the query.
*   **Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): Always `"user_online_response"`.
    *   `statuses` (list of objects): A list where each object represents the status of a queried user.

**Structure of objects within the `statuses` list:**

*   `user_id` (string): The UUID of the user.
*   `is_online` (boolean): `true` if the user is currently online, `false` otherwise.
*   `last_activity` (string or null): ISO format string indicating the timestamp of the user's last activity (e.g., connecting, sending heartbeat, sending message). `null` if user doesn't exist or no activity recorded.

**Example Payload:**

```json
{
  "type": "user_online_response",
  "statuses": [
    {
      "user_id": "9c191f88-c1e3-46c2-a5d9-269315fc004f",
      "is_online": true,
      "last_activity": "2023-10-27T10:30:00+00:00"
    },
    {
      "user_id": "51ca3997-8a3f-4d6a-9faa-d043ab3f707e",
      "is_online": false,
      "last_activity": "2023-10-27T09:15:00+00:00"
    }
  ]
}
```

#### Heartbeat Acknowledgment (Online Status Channel)

*   **Description:** Sent by the server in response to a client's `heartbeat` message, confirming the heartbeat was received and processed.
*   **Source:** Sent directly back to the client who sent the heartbeat.
*   **Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): Always `"heartbeat_ack"`.

**Example Payload:**

```json
{
  "type": "heartbeat_ack"
}
```

#### General Server Messages (Any Channel)

These messages can be sent by the server on any channel to provide information or report errors.

##### Informational Message

*   **Description:** Provides general information about the connection state or server actions.
*   **Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): Always `"info"`.
    *   `code` (string): A specific code indicating the type of information (e.g., `"CONNECTED"`).
    *   `message` (string): A human-readable description.
    *   `details` (object, optional): Additional contextual information.

**Example Payload (Connection Confirmation):**

```json
{
  "type": "info",
  "code": "CONNECTED",
  "message": "Successfully connected to the chat.",
  "details": {
    "room": "chat_hashedroomid",
    "other_user_id": "other_user_uuid"
  }
}
```

```json
{
  "type": "info",
  "code": "CONNECTED",
  "message": "Successfully connected to online status tracking.",
  "details": {
    "user_id": "your_user_uuid"
  }
}
```

##### Error Message

*   **Description:** Indicates that an error occurred while processing a client message or due to an internal server issue.
*   **Payload Structure:** JSON Object
*   **Fields:**
    *   `type` (string): Always `"error"`.
    *   `code` (string): A specific code indicating the type of error (defined in `WebSocketErrorCodes`).
    *   `message` (string): A human-readable description of the error.
    *   `details` (object, optional): Additional contextual information about the error.

**Common Error Codes (based on `ws_consts.py`):**

*   `AUTH_REQUIRED`: Authentication token is missing or invalid.
*   `USER_NOT_FOUND`: The specified user ID in the URL or payload does not exist.
*   `SERVER_ERROR`: An unexpected internal server error occurred.
*   `INVALID_JSON`: The received message is not valid JSON.
*   `INVALID_MESSAGE_CONTENT`: The received message JSON has missing or invalid fields for its type (e.g., missing `message` for `chat_message`).
*   `INVALID_MESSAGE_TYPE`: The received message has an unrecognized `type` field, or the `type` field is missing when expected.
*   `INVALID_PARAMETERS`: Parameters in the message (e.g., `user_ids` in a query) are invalid.
*   `PROCESSING_ERROR`: An error occurred during message processing (e.g., failed validation).
*   `SAVE_ERROR`: An error occurred while trying to save data (e.g., saving a chat message).

**Example Payload:**

```json
{
  "type": "error",
  "code": "INVALID_MESSAGE_TYPE",
  "message": "Unsupported message type.",
  "details": {
    "received_type": "some_other_type"
  }
}
```

```json
{
  "type": "error",
  "code": "INVALID_MESSAGE_CONTENT",
  "message": "Invalid or empty 'message' field. Expected non-empty string.",
  "details": {
    "received_message": ""
  }
}
```