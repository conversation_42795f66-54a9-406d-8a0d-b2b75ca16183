from django.urls import re_path
from .chat.consumers import ChatConsumer
from .online_status.consumers import OnlineStatusConsumer
from .session.consumers import SessionConsumer

websocket_urlpatterns = [
  re_path(r"ws/chat/(?P<user_id>[0-9a-f-]+)/$", ChatConsumer.as_asgi()),
  re_path(r"ws/status/online/$", OnlineStatusConsumer.as_asgi()),
  re_path(r"ws/session/(?P<session_id>[0-9a-f-]+)/$", SessionConsumer.as_asgi()),
  re_path(r"ws/session/$", SessionConsumer.as_asgi()),
]
