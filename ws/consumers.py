# ws/consumers/base.py

import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser

from core.utils.app_logger import AppLogger
from ws.consts.ws_consts import WebSocketErrorCodes, WebSocketMsgTypes
from ws.consts.ws_exceptions import ObjectLookupError, ProcessingError, InvalidJsonError
from ws.utils.ws_msg_helper import WebSocketMessageHelper

logger = AppLogger()


class BaseWebSocketConsumer(AsyncWebsocketConsumer):
  """
  Base class for WebSocket consumers providing common helpers.
  """

  # --- General Sending Helpers ---

  async def send_connected(self):
    """Helper to send a connected message."""
    await self.send_json(
      {
        "message": "Connected to session",
        "type": WebSocketMsgTypes.CONNECTED,
      }
    )

  async def send_json(self, data: dict):
    """Helper to send JSON data over the WebSocket."""
    try:
      await self.send(text_data=json.dumps(data))
    except Exception as e:
      # Log potential errors during sending (e.g., socket already closed)
      # This happens if the connection is already broken when we try to send.
      error_msg = f"[{self.__class__.__name__}] Error sending message over WebSocket: {e}"
      logger.log_error("BaseWebSocketConsumer - send_json", error_msg)

      # No need to send an error message *about* sending, as it likely won't arrive.

  async def send_error(self, code: str, message: str, details: dict = None):
    """Helper to send a standardized error message using the helper class."""
    # Note: WebSocketMessageHelper expects the code as a string
    error_payload = WebSocketMessageHelper.error(code, message, details)
    await self.send_json(error_payload)
    error_msg = f"[{self.__class__.__name__}] Sent error: Code={code}, Message='{message}'"
    logger.log_error("BaseWebSocketConsumer - send_error", error_msg)

  async def send_info(self, code: str, message: str, details: dict = None):
    """Helper to send a standardized informational message using the helper class."""
    info_payload = WebSocketMessageHelper.info(code, message, details)
    await self.send_json(info_payload)
    info_msg = f"[{self.__class__.__name__}] Sent info: Code={code}, Message='{message}'"
    logger.log_info("BaseWebSocketConsumer - send_info", info_msg)

  # --- Common Connection/Authentication Handlers ---

  async def check_authenticated(self):
    """
    Checks if the connected user is authenticated.
    If not, sends an auth error and closes the connection.
    Returns True if authenticated, False otherwise.
    """
    if isinstance(self.scope.get("user"), AnonymousUser) or not self.scope.get("user"):
      error_msg = f"[{self.__class__.__name__}] Connection rejected: Anonymous user."
      logger.log_error("BaseWebSocketConsumer - check_authenticated", error_msg)
      await self.send_error(
        code=WebSocketErrorCodes.AUTH_REQUIRED,
        message="Authentication failed. Please authenticate and reconnect.",
      )
      await self.close(code=1008)  # Policy violation (authentication required)
      return False
    self.user = self.scope["user"]  # Set user attribute if authenticated
    return True

  # --- Common Message Processing Helpers ---

  async def parse_json(self, text_data: str):
    """
    Parses incoming text data as JSON.
    Raises InvalidJsonError or ProcessingError on failure.
    """
    if not isinstance(text_data, str):
      # Or handle non-string data according to your protocol needs
      raise ProcessingError("Received non-string data.")
    try:
      return json.loads(text_data)
    except json.JSONDecodeError as e:
      raise InvalidJsonError("Invalid JSON format received.") from e
    except Exception as e:
      # Catch any other unexpected errors during loading
      raise ProcessingError(f"Error decoding JSON: {e}") from e

  # --- Common Database Interaction Helpers ---

  @database_sync_to_async
  def get_object_from_pk_async(self, model, pk_str: str, pk_kwarg_name: str = "pk"):
    """
    Fetches a single object from the database by its PK string.
    Handles ObjectDoesNotExist and potential ValueError for PK parsing.
    Raises ObjectLookupError on failure.
    """
    if not pk_str:
      raise ObjectLookupError(f"Missing required '{pk_kwarg_name}' identifier.")
    try:
      # Assume PK is something like UUID or integer that might raise ValueError on incorrect format
      obj = model._default_manager.get(pk=pk_str)
      return obj
    except (model.DoesNotExist, ValueError) as e:
      raise ObjectLookupError(
        f"{model.__name__} with identifier '{pk_str}' not found or invalid format."
      ) from e
    except Exception as e:
      raise ObjectLookupError(
        f"An unexpected error occurred fetching {model.__name__} with identifier '{pk_str}'."
      ) from e

  async def setup_connection_with_lookup(self, model, id_param_name, id_attr_name=None):
    """
    Handles common connection setup pattern with object lookup.
    """
    id_str = self.scope["url_route"]["kwargs"].get(id_param_name)
    attr_name = id_attr_name or f"{id_param_name}_id"

    try:
      obj = await self.get_object_from_pk_async(model, id_str, id_param_name)
      setattr(self, f"{id_param_name}", obj)
      setattr(self, attr_name, obj.id)
      return True
    except ObjectLookupError as e:
      await self.send_error(
        code=WebSocketErrorCodes.USER_NOT_FOUND,
        message=str(e),
        details={id_param_name: id_str},
      )
      await self.close(code=1008)
      return False
    except Exception as e:
      await self.send_error(
        code=WebSocketErrorCodes.SERVER_ERROR,
        message="An internal server error occurred during connection setup.",
        details={"error": str(e)},
      )
      await self.close(code=1011)
      return False

  async def validate_message_content(self, data, required_field="message"):
    """
    Validates that a required field exists and is a non-empty string.
    Returns (is_valid, content) tuple.
    """
    content = data.get(required_field)

    if not content or not isinstance(content, str) or not content.strip():
      await self.send_error(
        code=WebSocketErrorCodes.INVALID_MESSAGE_CONTENT,
        message=f"'{required_field}' field is required and must be a non-empty string.",
      )
      return False, None

    return True, content.strip()

  async def process_message(self, text_data, processor_func):
    """
    Handles common message processing pattern with error handling.
    processor_func should be an async function that processes the parsed data.
    """
    try:
      data = await self.parse_json(text_data)
      return await processor_func(data)
    except (InvalidJsonError, ProcessingError) as e:
      await self.send_error(
        code=WebSocketErrorCodes.INVALID_JSON
        if isinstance(e, InvalidJsonError)
        else WebSocketErrorCodes.PROCESSING_ERROR,
        message=str(e),
        details={"input": text_data[:100] + "..." if len(text_data) > 100 else text_data},
      )
      return False
    except Exception as e:
      await self.send_error(
        code=WebSocketErrorCodes.SERVER_ERROR,
        message="An unexpected error occurred while processing your message.",
        details={"error": str(e)},
      )
      return False
