from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
import asyncio

from core.utils.app_logger import AppLogger
from ws.consts.ws_consts import WebSocketErrorCodes, WebSocketMsgTypes
from ws.consumers import BaseWebSocketConsumer
from ws.utils.ws_msg_helper import WebSocketMessageHelper

User = get_user_model()

logger = AppLogger()


class OnlineStatusConsumer(BaseWebSocketConsumer):
  """
  WebSocket consumer for broadcasting and tracking online status of users.
  This consumer allows users to:
  1. Track when users come online/go offline
  2. Receive real-time updates about other users' online status
  3. Maintain more accurate presence with heartbeat mechanism
  """

  active_connections = {}

  HEARTBEAT_TIMEOUT = 120

  async def connect(self):
    """Handle new WebSocket connection for online status tracking"""
    if not await self.check_authenticated():
      return

    self.user_id = str(self.user.id)

    # Add this connection to user's active connections
    if self.user_id not in self.active_connections:
      self.active_connections[self.user_id] = set()
    self.active_connections[self.user_id].add(self)

    # Set up online status update group
    self.online_status_group_name = "online_status"

    # Join the online status group
    await self.channel_layer.group_add(self.online_status_group_name, self.channel_name)

    # Accept the connection
    await self.accept()

    # Mark user as online in database
    await self.set_user_online(True)

    # Broadcast user online status to all connected clients
    await self.broadcast_online_status_change(self.user_id, True)

    # Start heartbeat task
    self.heartbeat_task = asyncio.create_task(self.heartbeat_checker())

    # Send successful connection confirmation
    await self.send_info(
      code="CONNECTED",
      message="Successfully connected to online status tracking.",
      details={"user_id": self.user_id},
    )

  async def heartbeat_checker(self):
    """
    Coroutine that periodically checks if the client is still sending heartbeats.
    If no heartbeat is received within HEARTBEAT_TIMEOUT, marks the user as offline
    """
    self.last_heartbeat = timezone.now()
    while True:
      await asyncio.sleep(self.HEARTBEAT_TIMEOUT / 2)  # Check half as often as timeout

      # If no heartbeat received recently, break and user will be marked offline on disconnect
      time_since_beat = (timezone.now() - self.last_heartbeat).total_seconds()
      if time_since_beat > self.HEARTBEAT_TIMEOUT:
        logger.log_info(
          "OnlineStatusConsumer - heartbeat_check",
          f"[{self.__class__.__name__}] Heartbeat timeout for {self.user_id}",
        )
        await self.close(code=4000)  # Custom code for heartbeat timeout
        break

  async def disconnect(self, close_code):
    """
    Handle WebSocket disconnection. If this is the last connection for a user,
    mark them as offline in the database and broadcast the status change.
    """
    # Remove the connection from the user's active connections
    if hasattr(self, "user_id") and self.user_id in self.active_connections:
      self.active_connections[self.user_id].discard(self)

      # If this was the last connection for this user, mark them offline
      if not self.active_connections[self.user_id]:
        await self.set_user_online(False)
        await self.broadcast_online_status_change(self.user_id, False)
        # Clean up the empty set
        del self.active_connections[self.user_id]

    # Cancel heartbeat task if it exists
    if hasattr(self, "heartbeat_task"):
      self.heartbeat_task.cancel()

    # Leave the status group
    if hasattr(self, "online_status_group_name"):
      await self.channel_layer.group_discard(
        self.online_status_group_name,
        self.channel_name,
      )

    # Clean up instance attributes
    for attr in ["user", "user_id", "online_status_group_name", "last_heartbeat"]:
      if hasattr(self, attr):
        delattr(self, attr)

  @database_sync_to_async
  def set_user_online(self, is_online):
    """Update the user's online status in the database"""
    try:
      user = User.objects.get(id=self.user_id)
      user.is_online = is_online
      user.last_activity = timezone.now()
      user.save(update_fields=["is_online", "last_activity"])
      return True
    except User.DoesNotExist:
      return False

  async def broadcast_online_status_change(self, user_id, is_online):
    """Broadcast a user's online status change to all clients in the status group"""
    broadcast_payload = {
      "type": WebSocketMsgTypes.USER_ONLINE_STATUS,
      "user_id": user_id,
      "is_online": is_online,
      # TODO: Add timestamp
      # "timestamp": timezone.now().isoformat()
    }

    await self.channel_layer.group_send(self.online_status_group_name, broadcast_payload)

  async def user_online_status(self, event):
    """
    Handler for user_online_status events from the channel layer
    Forwards the online status change to connected WebSocket clients.
    """
    # Extract data from the event
    user_id = event.get("user_id")
    is_online = event.get("is_online")
    # TODO: Add timestamp
    # timestamp = event.get("timestamp")

    client_payload = WebSocketMessageHelper.online_status_update(
      user_id=user_id,
      is_online=is_online,
      # TODO: Add timestamp
      # timestamp=timestamp
    )

    await self.send_json(client_payload)

  async def receive(self, text_data):
    """Process incoming WebSocket messages"""
    await self.process_message(text_data, self._handle_message)

  async def _handle_message(self, data):
    """
    Handle incoming messages from WebSocket clients.
    Currently supports:
    - Heartbeat messages to maintain presence
    - Online status query messages to get status of specific users
    """

    message_type = data.get("type")

    if message_type == WebSocketMsgTypes.HEARTBEAT:
      # Update the last heartbeat time
      self.last_heartbeat = timezone.now()
      # Acknowledge the heartbeat
      await self.send_json({"type": WebSocketMsgTypes.HEARTBEAT_ACK})
      return True

    elif message_type == WebSocketMsgTypes.USER_ONLINE_QUERY:
      # Handle requests for current status of specific users
      user_ids = data.get("user_ids", [])
      if not user_ids or not isinstance(user_ids, list):
        await self.send_error(
          code=WebSocketErrorCodes.INVALID_PARAMETERS,
          message="Invalid or missing user_ids parameter. Expected a list of user IDs.",
        )
        return False

      # Fetch the online status for requested users
      statuses = await self._get_users_status(user_ids)
      await self.send_json(
        {
          "type": WebSocketMsgTypes.USER_ONLINE_RESPONSE,
          "statuses": statuses,
        }
      )
      return True

    else:
      # Unrecognized message type
      await self.send_error(
        code=WebSocketErrorCodes.INVALID_MESSAGE_TYPE,
        message=f"Unrecognized message type: {message_type}",
      )
      return False

  @database_sync_to_async
  def _get_users_status(self, user_ids):
    """Fetch the online status for a list of users from the database"""
    users = User.objects.filter(
      id__in=user_ids,
    ).values("id", "is_online", "last_activity")

    return [
      {
        "user_id": str(user["id"]),
        "is_online": user["is_online"],
        "last_activity": user["last_activity"].isoformat() if user.get("last_activity") else None,
      }
      for user in users
    ]
