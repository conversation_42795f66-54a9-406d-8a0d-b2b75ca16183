# chat/middleware.py
import jwt
from channels.middleware import BaseMiddleware
from django.contrib.auth.models import AnonymousUser
from django.db import close_old_connections
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.settings import api_settings
from rest_framework_simplejwt.backends import TokenBackend

# Database sync decorator for fetching user
from channels.db import database_sync_to_async

from core.utils.app_logger import AppLogger

logger = AppLogger()


class JwtAuthMiddleware(BaseMiddleware):
  """
  Custom middleware to authenticate users from JWT in WebSocket headers.
  Should be placed *before* AuthMiddlewareStack.
  """

  async def __call__(self, scope, receive, send):
    # Close old database connections to prevent usage of stale connections
    close_old_connections()

    # Check if it's a websocket scope
    if scope["type"] == "websocket":
      headers = dict(scope["headers"])
      token = None

      # Look for the Authorization header
      if b"authorization" in headers:
        try:
          # Header is typically "Bearer <token>"
          auth_header = headers[b"authorization"].decode("utf-8").split()
          if auth_header[0].lower() == "bearer" and len(auth_header) == 2:
            token = auth_header[1]
        except (UnicodeDecodeError, IndexError):
          # Handle incorrect header format gracefully
          logger("JwtAuthMiddleware - __call__", "Invalid authorization header format")

          pass

      scope["user"] = AnonymousUser()  # Default the user to anonymous

      if token:
        try:
          # Use Simple JWT's token validation and user lookup logic
          backend = TokenBackend(
            algorithm=api_settings.ALGORITHM, signing_key=api_settings.SIGNING_KEY
          )
          # Decode token; this also verifies its signature and expiration
          validated_token = backend.decode(token, verify=api_settings.VERIFYING_KEY)

          # Get the user ID from the token payload
          user_id = validated_token.get(api_settings.USER_ID_CLAIM)

          if user_id:
            # Fetch the user from the database asynchronously
            user = await self.get_user_from_id(user_id)
            if user:
              scope["user"] = user  # Set the authenticated user on the scope
            else:
              logger.log_error("JwtAuthMiddleware - __call__", f"User with ID {user_id} not found")

        except (InvalidToken, TokenError, jwt.ExpiredSignatureError) as e:
          # Handle token validation errors (e.g., expired, invalid signature)
          logger.log_error("JwtAuthMiddleware - __call__", f"JWT Authentication failed: {e}")
          # User remains AnonymousUser
        except Exception as e:
          # Catch any other unexpected errors during the process
          logger.log_error(
            "JwtAuthMiddleware - __call__", f"Unexpected error during JWT authentication: {e}"
          )
          # User remains AnonymousUser

    # Pass the (potentially modified) scope to the next middleware or consumer
    return await self.inner(scope, receive, send)

  @database_sync_to_async
  def get_user_from_id(self, user_id):
    """Fetches a user from the database given their ID."""
    from django.contrib.auth import get_user_model

    User = get_user_model()
    try:
      # Assuming user_id claim is the primary key
      return User.objects.get(pk=user_id)
    except User.DoesNotExist:
      # Return AnonymousUser if no user matches the ID
      from django.contrib.auth.models import AnonymousUser

      return AnonymousUser()
