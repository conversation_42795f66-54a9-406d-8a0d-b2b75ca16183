from django.contrib.auth import get_user_model
from channels.db import database_sync_to_async

from api.chat.models import Chat<PERSON>oom, Message
from core.utils.app_logger import AppLogger


from ws.chat.utils import get_room_name
from ws.consts.ws_consts import WebSocketErrorCodes, WebSocketMsgTypes
from ws.consumers import BaseWebSocketConsumer
from ws.utils.ws_msg_helper import WebSocketMessageHelper

User = get_user_model()

logger = AppLogger()


class ChatConsumer(BaseWebSocketConsumer):
  """
  WebSocket consumer for handling real-time chat between two users.
  """

  async def connect(self):
    if not await self.check_authenticated():
      return

    if not await self.setup_connection_with_lookup(User, "user_id", "other_user_id"):
      return

    try:
      self.other_user = await database_sync_to_async(User.objects.get)(id=self.other_user_id)
    except Exception as e:
      await self.send_error(
        code=WebSocketErrorCodes.SERVER_ERROR,
        message="An internal server error occurred during connection setup.",
        details={"error": str(e)},
      )
      await self.close(code=1011)
      return

    # Set up the chat room
    self.room_name = get_room_name(self.user.id, self.other_user_id)
    self.room_group_name = f"chat_{self.room_name}"

    # Join the room group
    await self.channel_layer.group_add(self.room_group_name, self.channel_name)
    await self.accept()

    await self.send_info(
      code="CONNECTED",
      message="Successfully connected to the chat.",
      details={"room": self.room_group_name, "other_user_id": str(self.other_user_id)},
    )

  async def disconnect(self, close_code):
    if hasattr(self, "room_group_name"):
      await self.channel_layer.group_discard(
        self.room_group_name,
        self.channel_name,
      )

    for attr in ["user", "other_user", "other_user_id", "room_name", "room_group_name"]:
      if hasattr(self, attr):
        delattr(self, attr)

  async def chat_message(self, event):
    """
    Handles messages received from the channel layer group.
    This is typically a message broadcast by another instance
    of this consumer (or itself) when a message is sent.
    """
    # The 'event' dictionary contains the data passed to group_send
    # It includes the 'type' (which triggered this handler)
    # and any other keys from the broadcast_payload.
    message_type = event.get("type")  # Should be "chat_message"
    sender_id = event.get("sender")
    content = event.get("message")
    message_id = event.get("message_id")  # Assuming you add message_id to broadcast

    # Now send this message *down* to the client's WebSocket connection
    # You can format the payload for the client as needed.
    # Often, it's similar to the broadcast payload structure.
    payload_to_client = {
      "type": message_type,  # Typically send the same type back
      "sender": sender_id,
      "message": content,
    }
    if message_id:
      payload_to_client["message_id"] = message_id

    # Use your helper method to send JSON
    await self.send_json(payload_to_client)

    # Optional: Add logging to confirm the message was received from the group and sent to the client
    logger.log_info(
      "ChatConsumer - chat_message",
      f"[{self.__class__.__name__}] Received group message '{message_type}' and sent to client.",
    )

  async def receive(self, text_data):
    await self.process_message(text_data, self._handle_message)

  async def _handle_message(self, data):
    """
    Handle incoming messages from WebSocket clients.
    Currently supports sending chat messages and heartbeats to the connected user.
    """
    message_type = data.get("type")

    if message_type == WebSocketMsgTypes.HEARTBEAT:
      # Acknowledge heartbeat with a response
      await self.send_json({"type": WebSocketMsgTypes.HEARTBEAT_ACK})

      logger.log_info(
        "ChatConsumer - heartbeat", f"[{self.__class__.__name__}] Received heartbeat from client."
      )
      return True

    if message_type != WebSocketMsgTypes.CHAT_MESSAGE:
      await self.send_error(
        code=WebSocketErrorCodes.INVALID_MESSAGE_TYPE,
        message=f"Unsupported message type: {message_type}",
        details={"received_type": message_type},
      )
      return False

    is_valid, message_content = await self.validate_message_content(data)
    if not is_valid:
      return False

    try:
      saved_message = await self._save_message(
        self.user,
        self.other_user,
        message_content,
      )

      broadcast_payload = {
        "type": WebSocketMsgTypes.CHAT_MESSAGE,
        **WebSocketMessageHelper.chat_message(
          sender_id=str(self.user.id),
          content=saved_message.content,
        ),
      }

      if hasattr(saved_message, "id"):
        broadcast_payload["message_id"] = str(saved_message.id)

      await self.channel_layer.group_send(self.room_group_name, broadcast_payload)
      return True

    except Exception as e:
      logger.log_error(
        "ChatConsumer - _handle_message", f"[{self.__class__.__name__}] Error saving message: {e}"
      )
      await self.send_error(
        code=WebSocketErrorCodes.SAVE_ERROR,
        message="An error occurred while trying to save your message.",
        details={"error": str(e)},
      )
      return False

  @database_sync_to_async
  def _save_message(self, sender_user, receiver_user, content):
    """Saves a message to the database."""
    # Find or create the chat room between these two users
    chat_room = (
      ChatRoom.objects.filter(participants=sender_user).filter(participants=receiver_user).first()
    )

    if not chat_room:
      # Create a new chat room if it doesn't exist
      chat_room = ChatRoom.objects.create()
      chat_room.participants.add(sender_user, receiver_user)
      chat_room.save()

    return Message.objects.create(
      sender=sender_user, receiver=receiver_user, content=content, chat_room=chat_room
    )
