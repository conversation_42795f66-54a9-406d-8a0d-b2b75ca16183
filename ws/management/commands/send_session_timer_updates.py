import asyncio
from django.core.management.base import BaseCommand
from django.utils import timezone
from channels.layers import get_channel_layer
from channels.db import database_sync_to_async
from api.session.models import Session, SessionStatus
from ws.session.utils import get_session_group_name
from ws.consts.ws_consts import WebSocketMsgTypes
from core.utils.app_logger import AppLogger

logger = AppLogger()


@database_sync_to_async
def _get_active_sessions():
  return list(
    Session.objects.filter(status=SessionStatus.ACTIVE).select_related("invitation__activity")
  )


@database_sync_to_async
def _complete_session(session_id):
  try:
    session = Session.objects.get(id=session_id)
    session.status = SessionStatus.COMPLETED
    session.ended_at = timezone.now()
    session.save(update_fields=["status", "ended_at", "updated_at"])
    logger.log_info("SendSessionTimerUpdatesCommand", f"Session {session_id} completed.")
  except Session.DoesNotExist:
    logger.log_warning(
      "SendSessionTimerUpdatesCommand", f"Session {session_id} not found for completion."
    )
  except Exception as e:
    logger.log_error(
      "SendSessionTimerUpdatesCommand", f"Error completing session {session_id}: {e}"
    )


class Command(BaseCommand):
  help = "Sends periodic updates for active sessions with remaining time."

  async def handle_async(self):
    channel_layer = get_channel_layer()
    if not channel_layer:
      logger.log_error(
        "SendSessionTimerUpdatesCommand",
        "Cannot get channel layer. Make sure Channels is configured correctly.",
      )
      return

    while True:
      try:
        active_sessions = await _get_active_sessions()

        for session in active_sessions:
          now = timezone.now()
          if session.started_at:
            planned_end_time = session.started_at + session.invitation.activity.duration
            if session.total_pause_duration:
              planned_end_time += session.total_pause_duration

            remaining_time = planned_end_time - now
            session_group_name = get_session_group_name(session.id)

            if remaining_time.total_seconds() > 0:
              # logger.log_info(
              #   "SendSessionTimerUpdatesCommand",
              #   f"Sending session timer update: {session.id} - {remaining_time.total_seconds()} seconds remaining",
              # )
              # TODO: Move to the session sender
              await channel_layer.group_send(
                session_group_name,
                {
                  "type": WebSocketMsgTypes.SESSION_TIMER_UPDATE,
                  "data": {
                    "remaining_time": remaining_time.total_seconds(),
                  },
                },
              )
            else:
              # Handle session completion
              logger.log_info("SendSessionTimerUpdatesCommand", f"Session {session.id} is ending.")
              await channel_layer.group_send(
                session_group_name,
                {"type": WebSocketMsgTypes.SESSION_ENDED, "data": {"session_id": str(session.id)}},
              )
              await _complete_session(session.id)

      except Exception as e:
        logger.log_error(f"Error in send_session_timer_updates command: {e}")

      await asyncio.sleep(1)

  def handle(self, *args, **options):
    asyncio.run(self.handle_async())
