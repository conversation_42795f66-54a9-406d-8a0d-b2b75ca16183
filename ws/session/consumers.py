# ws/session/consumers.py
from django.contrib.auth import get_user_model
from channels.db import database_sync_to_async

from core.utils.app_logger import AppLogger

from ws.consts.ws_consts import WebSocketErrorCodes, WebSocketMsgTypes
from ws.consumers import BaseWebSocketConsumer
from core.utils.app_msg import AppMsg
from ws.session.sender.message_sender import SessionMessageSender
from ws.session.receiver.auto_start_handler import attempt_auto_start_session

from ws.session.utils import get_session_group_name, get_session
from ws.session.db_helpers import user_in_session
from ws.session.receiver.pause_session import handle_pause_session
from ws.session.receiver.resume_session import handle_resume_session
from ws.session.receiver.leave_early import handle_leave_early

User = get_user_model()

logger = AppLogger()


class SessionConsumer(BaseWebSocketConsumer):
  """
  WebSocket consumer for handling real-time session interactions between users.
  Supports session creation, verification, status updates, and emergency reporting.
  """

  async def connect(self):
    """
    Handle WebSocket connection setup.
    Authenticates the user and sets up session-specific groups if applicable.
    """
    if not await self.check_authenticated():
      return

    # Extract session_id from URL route if provided
    self.session_id = self.scope["url_route"]["kwargs"].get("session_id")

    if not self.session_id:
      await self.send_error(
        code=WebSocketErrorCodes.INVALID_PARAMETERS,
        message=AppMsg.WS_SESSION_ID_REQUIRED,
      )
      await self.close(code=1008)
      return

    # Verify user is part of this session
    is_member = await user_in_session(self.session_id, self.user.id)
    if not is_member:
      await self.send_error(
        code=WebSocketErrorCodes.SESSION_UNAUTHORIZED,
        message=AppMsg.WS_SESSION_UNAUTHORIZED,
      )
      await self.close(code=1008)
      return

    # Join the session-specific group
    self.session_group_name = get_session_group_name(self.session_id)
    await self.channel_layer.group_add(self.session_group_name, self.channel_name)

    session_data_for_client = await database_sync_to_async(get_session)(self.session_id, self.scope)
    if not session_data_for_client:
      await self.send_error(
        code=WebSocketErrorCodes.SESSION_NOT_FOUND,
        message=AppMsg.WS_SESSION_NOT_FOUND,
      )
      await self.close(code=1008)
      return

    # Accept the connection
    await self.accept()

    await self.send_connected()

    details = {
      "user_id": str(self.user.id),
      "session": session_data_for_client,
    }

    await SessionMessageSender.send_session_info(self, details)

    await attempt_auto_start_session(self)

  async def disconnect(self, close_code):
    """
    Handle WebSocket disconnection.
    Cleans up group memberships and instance attributes.
    """
    # Leave the session group if joined
    if hasattr(self, "session_group_name"):
      await self.channel_layer.group_discard(
        self.session_group_name,
        self.channel_name,
      )

    # Leave the user's personal group
    if hasattr(self, "user_group_name"):
      await self.channel_layer.group_discard(
        self.user_group_name,
        self.channel_name,
      )

    # Clean up instance attributes
    for attr in ["user", "session_id", "session_group_name", "user_group_name"]:
      if hasattr(self, attr):
        delattr(self, attr)

  async def receive(self, text_data):
    """
    Handle incoming WebSocket messages.
    Processes different message types and routes to appropriate handlers.
    """
    await self.process_message(text_data, self._handle_message)

  async def _handle_message(self, data):
    """
    Process incoming WebSocket messages based on their type.
    Routes to appropriate action handlers.
    """
    message_type = data.get("type")

    if not message_type:
      await self.send_error(
        code=WebSocketErrorCodes.INVALID_MESSAGE_TYPE,
        message=AppMsg.WS_UNKNOWN_MESSAGE_TYPE.format(msg_type="<not provided>"),
      )
      return False

    # Handle heartbeat messages
    if message_type == WebSocketMsgTypes.HEARTBEAT:
      await self.send_json({"type": WebSocketMsgTypes.HEARTBEAT_ACK})
      return True

    handlers = {
      WebSocketMsgTypes.SESSION_PAUSE: handle_pause_session,
      WebSocketMsgTypes.SESSION_RESUME: handle_resume_session,
      WebSocketMsgTypes.SESSION_LEAVE_EARLY: handle_leave_early,
    }

    handler = handlers.get(message_type)

    if not handler:
      await self.send_error(
        code=WebSocketErrorCodes.INVALID_MESSAGE_TYPE,
        message=AppMsg.WS_UNKNOWN_MESSAGE_TYPE.format(msg_type=message_type),
        details={"received_type": message_type},
      )
      return False

    try:
      # Call the appropriate handler with the message content
      await handler(self, data)
      return True
    except Exception as e:
      logger.log_error(
        "SessionConsumer - _handle_message",
        f"[{self.__class__.__name__}] Error processing {message_type}: {str(e)}",
      )
      await self.send_error(
        code=WebSocketErrorCodes.SERVER_ERROR,
        message=AppMsg.WS_ERROR_PROCESSING_MESSAGE.format(error=e),
        details={"error": str(e)},
      )
      return False

  async def session_paused(self, event):
    await self.send_json(
      {
        "type": WebSocketMsgTypes.SESSION_PAUSED,
        "data": event.get("data"),
        "message": event.get("message"),
      }
    )

  async def session_resumed(self, event):
    await self.send_json(
      {
        "type": WebSocketMsgTypes.SESSION_RESUMED,
        "data": event.get("data"),
        "message": event.get("message"),
      }
    )

  async def session_left_early(self, event):
    await self.send_json(
      {
        "type": WebSocketMsgTypes.SESSION_LEFT_EARLY,
        "data": event.get("data"),
        "message": event.get("message"),
      }
    )

  async def session_timer_update(self, event):
    await self.send_json(
      {
        "type": WebSocketMsgTypes.SESSION_TIMER_UPDATE,
        "data": event.get("data"),
      }
    )
