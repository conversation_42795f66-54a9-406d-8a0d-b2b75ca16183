from api.session.models import Session
from core.utils.app_logger import App<PERSON>ogger
from api.utils import ApiUtils


class ScopeRequest:
  """
  A mock request object created from a Channels scope,
  providing a `build_absolute_uri` method.
  """

  def __init__(self, scope):
    self.scope = scope

  def build_absolute_uri(self, location):
    """
    Constructs an absolute URI from the scope and a given location.
    """
    if not self.scope:
      return location

    scheme = self.scope.get("scheme", "http")
    if scheme == "ws":
      scheme = "http"
    elif scheme == "wss":
      scheme = "https"

    host = ""
    for name, value in self.scope.get("headers", []):
      if name == b"host":
        host = value.decode("utf-8")
        break

    if not host and "server" in self.scope:
      host_ip, port = self.scope["server"]
      if (scheme == "http" and port != 80) or (scheme == "https" and port != 443):
        host = f"{host_ip}:{port}"
      else:
        host = host_ip

    if not host:
      return location

    if location and not location.startswith("/"):
      location = f"/{location}"

    return f"{scheme}://{host}{location}"


logger = AppLogger()


def get_session(session_id, scope=None):
  """
  Retrieves and formats session details for client-side consumption.

  Args:
      session_id: The ID of the session to retrieve.

  Returns:
      A dictionary containing formatted session data if found, otherwise None.
      The dictionary includes activity details, session status, and user information
      for both host and guest (ID, first profile image, location coordinates).
  """
  try:
    session = (
      Session.objects.select_related(
        "invitation",
        "invitation__activity",
        "host",
        "guest",
      )
      .prefetch_related(
        "host__profile_images",
        "guest__profile_images",
      )
      .get(id=session_id)
    )

    invitation = session.invitation
    activity = invitation.activity

    request = ScopeRequest(scope) if scope else None

    def _format_user_for_session(user_obj):
      if not user_obj:
        return None

      location_data = None
      if user_obj.location:
        location_data = {
          "latitude": user_obj.location.y,
          "longitude": user_obj.location.x,
        }

      return {
        "id": str(user_obj.id),
        "first_image": ApiUtils.get_first_profile_image(user_obj, request=request),
        "location": location_data,
      }

    host_data = _format_user_for_session(session.host)
    guest_data = _format_user_for_session(session.guest)

    users_list = []
    if host_data:
      users_list.append(host_data)
    if guest_data:
      users_list.append(guest_data)

    # TODO: make it better?
    try:
      paused_by = str(session.paused_by.id)
    except Exception as _:
      paused_by = ""

    session_data = {
      "activity_name": activity.name,
      "activity_price": str(activity.price),
      "meeting_address": invitation.meeting_address,
      "session_status": session.status,
      "users": users_list,
      "id": str(session.id),
      "invitation_id": str(invitation.id),
      "paused_by": paused_by,
    }
    return session_data

  except Session.DoesNotExist:
    logger.warning(f"Session with id {session_id} not found in get_session.")
    return None
  except Exception as e:
    logger.error(f"Error in get_session for session_id {session_id}: {str(e)}", exc_info=True)
    return None


def get_session_group_name(session_id):
  """
  Generate a consistent group name for a session's WebSocket group.

  Args:
      session_id: The ID of the session

  Returns:
      String group name for the session
  """
  return f"session_{session_id}"


def get_user_session_group_name(user_id):
  """
  Generate a consistent group name for a user's session updates.

  Args:
      user_id: The ID of the user

  Returns:
      String group name for the user's session updates
  """
  return f"user_session_{user_id}"
