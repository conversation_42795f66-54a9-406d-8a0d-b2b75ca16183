"""
Message handlers for session WebSocket events.
"""

from typing import Dict, Optional

from ws.consts.ws_consts import WebSocketMsgTypes
from api.session.models import SessionStatus


class SessionMessageSender:
  """
  Handlers for session-related WebSocket messages.
  These methods are designed to be called by the channel layer.
  All messages conform to a structure: {type: str, data?: Dict, message?: str}.
  """

  @staticmethod
  async def _send_message_base(
    consumer, type: str, data: Optional[Dict] = None, message: Optional[str] = None
  ):
    """
    Base method to send a structured WebSocket message.
    'type' is required. 'data' and 'message' are optional.
    If 'data' or 'message' are effectively absent (None, or empty dict/string),
    they are omitted from the payload.
    """
    payload = {"type": type}
    if data:  # True if data is not None and not an empty dictionary
      payload["data"] = data
    if message:  # True if message is not None and not an empty string
      payload["message"] = message
    await consumer.send_json(payload)

  @staticmethod
  async def _broadcast_message_base(
    consumer, type: str, data: Optional[Dict] = None, message: Optional[str] = None
  ):
    """
    Base method to send a structured WebSocket message.
    'type' is required. 'data' and 'message' are optional.
    If 'data' or 'message' are effectively absent (None, or empty dict/string),
    they are omitted from the payload.
    """
    payload = {"type": type}
    if data:  # True if data is not None and not an empty dictionary
      payload["data"] = data
    if message:  # True if message is not None and not an empty string
      payload["message"] = message

    await consumer.channel_layer.group_send(consumer.session_group_name, payload)

  @staticmethod
  async def send_session_info(consumer, session_data: Dict):
    """
    Handle a session info message.
    """
    # TODO: add whole data about session here
    await SessionMessageSender._send_message_base(
      consumer,
      data=session_data,
      type=WebSocketMsgTypes.SESSION_INFO,
      message="Session info has been updated",
    )

  @staticmethod
  async def send_session_update(consumer, status: SessionStatus):
    """
    Handle a general session update message.
    """
    await SessionMessageSender._send_message_base(
      consumer,
      data={"session_status": status.value},
      type=WebSocketMsgTypes.SESSION_UPDATED,
      message="Session has been updated",
    )

    # await consumer.channel_layer.group_send(
    #   consumer.session_group_name,
    #   {
    #     "type": WebSocketMsgTypes.SESSION_UPDATED,
    #     "data": {"session_status": status.value},
    #     "message": "Session has been updated",
    #   },
    # )

  @staticmethod
  async def send_session_paused(consumer, actor_user_id: str):
    """
    Handle a session paused message.
    """
    data_payload = {
      "actor_user_id": actor_user_id,
      "session_status": SessionStatus.PAUSED.value,
    }

    await SessionMessageSender._broadcast_message_base(
      consumer,
      type="session.paused",
      data=data_payload,
      message="Session has been paused",
    )

  @staticmethod
  async def send_session_resumed(consumer):
    """
    Handle a session resumed message.
    """
    data_payload = {
      "status": SessionStatus.ACTIVE.value,
    }

    await SessionMessageSender._broadcast_message_base(
      consumer,
      data=data_payload,
      type="session.resumed",
      message="Session has been resumed",
    )

  @staticmethod
  async def send_session_ended(consumer, actor_user_id: str, session_id: str, ended_at: str):
    """
    Handle a session ended message.
    """
    sent_by_this_user = False
    if actor_user_id and consumer.user and hasattr(consumer.user, "id"):
      sent_by_this_user = str(consumer.user.id) == str(actor_user_id)

    data_payload = {
      "session_id": session_id,
      "ended_at": ended_at,
      "sent_by_you": sent_by_this_user,
    }

    await SessionMessageSender._send_message_base(
      consumer,
      type=WebSocketMsgTypes.SESSION_ENDED,
      data=data_payload,
      message="Session has been ended",
    )

  @staticmethod
  async def send_session_emergency(consumer, actor_user_id: str, session_id: str, reported_at: str):
    """
    Handle a session emergency message.
    """
    sent_by_this_user = False
    if actor_user_id and consumer.user and hasattr(consumer.user, "id"):
      sent_by_this_user = str(consumer.user.id) == str(actor_user_id)

    data_payload = {
      "session_id": session_id,
      "reported_at": reported_at,
      "sent_by_you": sent_by_this_user,
    }

    await SessionMessageSender._send_message_base(
      consumer,
      type=WebSocketMsgTypes.SESSION_EMERGENCY,
      data=data_payload,
      message="Session has been reported as emergency",
    )

  @staticmethod
  async def send_session_left_early(consumer, actor_user_id: str):
    """
    Handle a session left early message.
    """
    data_payload = {
      "actor_user_id": actor_user_id,
      "session_status": SessionStatus.TERMINATED_EARLY.value,
    }

    await SessionMessageSender._broadcast_message_base(
      consumer,
      type="session.left.early",
      data=data_payload,
      message="Session has been left early",
    )
