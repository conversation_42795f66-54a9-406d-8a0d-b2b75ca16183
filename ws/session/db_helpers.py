"""
Database helper functions for session WebSocket operations.
"""

from channels.db import database_sync_to_async
from django.utils import timezone

from api.session.models import Session, SessionStatus
from core.utils.app_logger import AppLogger

logger = AppLogger()


@database_sync_to_async
def update_session_status(user, session_id, new_status):
  """
  Update the status of a session.

  Args:
      user: The authenticated user (must be part of the session)
      session_id: The ID of the session
      new_status: The new status to set

  Returns:
      Dict with session information or error
  """
  try:
    session = Session.objects.get(id=session_id)

    # Verify user is part of this session
    if session.host_id != user.id and session.guest_id != user.id:
      return {"success": False, "message": "You are not authorized to update this session"}

    # Check for valid status transitions
    if not is_valid_status_transition(session.status, new_status):
      return {
        "success": False,
        "message": f"Invalid status transition from {session.status} to {new_status}",
      }

    # Update session status
    session.status = new_status
    timestamp = timezone.now()

    # Set appropriate timestamp based on status
    if new_status == SessionStatus.ACTIVE and not session.started_at:
      session.started_at = timestamp
    elif new_status == SessionStatus.PAUSED:
      session.paused_at = timestamp
    elif new_status == SessionStatus.COMPLETED:
      session.ended_at = timestamp

    session.save()

    return {
      "success": True,
      "session_id": str(session.id),
      "status": new_status,
      "timestamp": timestamp.isoformat(),
    }

  except Session.DoesNotExist:
    return {"success": False, "message": "Session not found"}
  except Exception as e:
    logger.log_error("SessionDbHelpers - update_session_status", str(e))
    return {"success": False, "message": str(e)}


@database_sync_to_async
def report_session_emergency(user, session_id, details):
  """
  Report an emergency during a session.

  Args:
      user: The authenticated user (must be part of the session)
      session_id: The ID of the session
      details: Details about the emergency

  Returns:
      Dict with session information or error
  """
  try:
    session = Session.objects.get(id=session_id)

    # Verify user is part of this session
    if session.host_id != user.id and session.guest_id != user.id:
      return {
        "success": False,
        "message": "You are not authorized to report an emergency for this session",
      }

    # Record emergency
    session.emergency_reported_at = timezone.now()
    session.emergency_details = details
    session.status = SessionStatus.EMERGENCY
    session.save()

    return {
      "success": True,
      "session_id": str(session.id),
      "timestamp": session.emergency_reported_at.isoformat(),
    }

  except Session.DoesNotExist:
    return {"success": False, "message": "Session not found"}
  except Exception as e:
    logger.log_error("SessionDbHelpers - report_session_emergency", str(e))
    return {"success": False, "message": str(e)}


@database_sync_to_async
def record_early_departure(user, session_id, reason):
  """
  Record a user leaving a session early.

  Args:
      user: The authenticated user (must be part of the session)
      session_id: The ID of the session
      reason: Reason for leaving early

  Returns:
      Dict with session information or error
  """
  try:
    session = Session.objects.get(id=session_id)

    # Verify user is part of this session
    if session.host_id != user.id and session.guest_id != user.id:
      return {"success": False, "message": "You are not authorized to leave this session"}

    timestamp = timezone.now()

    # Record who left early
    if session.host_id == user.id:
      session.host_left_early = True
      session.host_left_at = timestamp
      session.host_left_reason = reason
    else:
      session.guest_left_early = True
      session.guest_left_at = timestamp
      session.guest_left_reason = reason

    # If both users have left, end the session
    if getattr(session, "host_left_early", False) and getattr(session, "guest_left_early", False):
      session.status = SessionStatus.COMPLETED
      session.ended_at = timestamp

    session.save()

    return {
      "success": True,
      "session_id": str(session.id),
      "timestamp": timestamp.isoformat(),
    }

  except Session.DoesNotExist:
    return {"success": False, "message": "Session not found"}
  except Exception as e:
    logger.log_error("SessionDbHelpers - record_early_departure", str(e))
    return {"success": False, "message": str(e)}


@database_sync_to_async
def user_in_session(session_id, user_id):
  """
  Check if a user is part of a session.

  Args:
      session_id: The ID of the session
      user_id: The ID of the user

  Returns:
      Boolean indicating if the user is part of the session
  """
  try:
    session = Session.objects.get(id=session_id)
    return session.host_id == user_id or session.guest_id == user_id
  except Session.DoesNotExist:
    return False
  except Exception as e:
    logger.log_error("SessionDbHelpers - user_in_session", str(e))
    return False


def is_valid_status_transition(current_status, new_status):
  """
  Helper method to validate session status transitions.

  Args:
      current_status: The current status of the session
      new_status: The new status to transition to

  Returns:
      Boolean indicating if the transition is valid
  """
  # Define valid transitions
  valid_transitions = {
    SessionStatus.PENDING: [SessionStatus.ACTIVE],
    SessionStatus.ACTIVE: [
      SessionStatus.PAUSED,
      SessionStatus.COMPLETED,
      SessionStatus.EMERGENCY,
    ],
    SessionStatus.PAUSED: [
      SessionStatus.ACTIVE,
      SessionStatus.COMPLETED,
      SessionStatus.EMERGENCY,
    ],
    SessionStatus.COMPLETED: [],  # Terminal state
    SessionStatus.EMERGENCY: [SessionStatus.COMPLETED],  # Can only complete after emergency
  }

  return new_status in valid_transitions.get(current_status, [])
