"""
Handler for ending a session.
"""

import logging
from django.utils import timezone
from channels.db import database_sync_to_async
from api.session.models import Session, SessionStatus
from ws.consts.ws_consts import WebSocketErrorCodes, WebSocketMsgTypes
from core.utils.app_msg import AppMsg

logger = logging.getLogger(__name__)


@database_sync_to_async
def _end_session_db(user, session_id):
  """
  Updates the session to 'completed'.
  """
  try:
    session = Session.objects.select_related("host", "guest").get(pk=session_id)

    if user.id not in [session.host.id, session.guest.id]:
      return {"success": False, "message": AppMsg.WS_END_SESSION_NOT_PARTICIPANT}

    if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
      return {
        "success": False,
        "message": AppMsg.WS_END_SESSION_INVALID_STATE.format(state=session.status),
      }

    now = timezone.now()
    session.status = SessionStatus.COMPLETED
    session.ended_at = now
    session.save()

    logger.info(AppMsg.WS_END_SESSION_LOG_SUCCESS.format(session_id=session_id, user_id=user.id))

    return {
      "success": True,
      "session_id": str(session.id),
      "ended_at": now.isoformat(),
      "actor_user_id": user.id,
    }
  except Session.DoesNotExist:
    return {"success": False, "message": AppMsg.WS_SESSION_NOT_FOUND}
  except Exception as e:
    logger.error(AppMsg.WS_END_SESSION_LOG_ERROR.format(session_id=session_id, error=e))
    return {"success": False, "message": AppMsg.WS_END_SESSION_UNEXPECTED_ERROR}


async def handle_end_session(consumer, content):
  """Handle ending a session."""
  session_id = content.get("session_id") or getattr(consumer, "session_id", None)
  user = consumer.user

  if not session_id:
    await consumer.send_error(
      code=WebSocketErrorCodes.INVALID_PARAMETERS, message=AppMsg.WS_SESSION_ID_REQUIRED
    )
    return

  try:
    result = await _end_session_db(user, session_id)

    if result["success"]:
      await consumer.channel_layer.group_send(
        f"session_{session_id}",
        {
          "type": WebSocketMsgTypes.SESSION_ENDED,
          "data": {
            "session_id": result["session_id"],
            "ended_at": result["ended_at"],
            "actor_user_id": result["actor_user_id"],
            "message": AppMsg.WS_END_SESSION_ENDED_MSG,
          },
        },
      )
    else:
      await consumer.send_error(
        code=WebSocketErrorCodes.SESSION_INVALID_STATE, message=result["message"]
      )

  except Exception as e:
    logger.error(AppMsg.WS_END_SESSION_HANDLER_ERROR.format(session_id=session_id, error=e))
    await consumer.send_error(
      code=WebSocketErrorCodes.SERVER_ERROR,
      message=AppMsg.WS_END_SESSION_ERROR_MSG.format(error=str(e)),
    )
