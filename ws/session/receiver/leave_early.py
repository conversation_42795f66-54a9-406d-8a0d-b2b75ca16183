"""
Handler for a user leaving a session early.
"""

import logging
from django.utils import timezone
from channels.db import database_sync_to_async
from api.session.models import Session, SessionStatus
from ws.consts.ws_consts import WebSocketErrorCodes
from ws.session.sender.message_sender import SessionMessageSender
from core.utils.app_msg import AppMsg

logger = logging.getLogger(__name__)


@database_sync_to_async
def _leave_early_db(user, session_id, reason):
  """
  Updates the session to 'terminated_early', recording the user and reason.
  """
  try:
    session = Session.objects.select_related("host", "guest").get(pk=session_id)

    if user.id not in [session.host.id, session.guest.id]:
      return {"success": False, "message": AppMsg.WS_END_SESSION_NOT_PARTICIPANT}

    if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
      return {
        "success": False,
        "message": AppMsg.WS_LEAVE_EARLY_INVALID_STATE.format(state=session.status),
      }

    now = timezone.now()
    session.status = SessionStatus.TERMINATED_EARLY
    session.early_terminated_by = user
    session.early_termination_reason = reason
    session.ended_at = now
    session.save()

    logger.info(AppMsg.WS_LEAVE_EARLY_LOG_SUCCESS.format(session_id=session_id, user_id=user.id))

    return {
      "success": True,
      "session_id": str(session.id),
      "left_at": now.isoformat(),
      "actor_user_id": str(user.id),
    }
  except Session.DoesNotExist:
    return {"success": False, "message": AppMsg.WS_SESSION_NOT_FOUND}
  except Exception as e:
    logger.error(AppMsg.WS_LEAVE_EARLY_LOG_ERROR.format(session_id=session_id, error=e))
    return {"success": False, "message": AppMsg.WS_LEAVE_EARLY_UNEXPECTED_ERROR}


async def handle_leave_early(consumer, content):
  """Handle a user leaving a session early."""
  session_id = content.get("session_id") or getattr(consumer, "session_id", None)
  reason = content.get("reason", "")
  user = consumer.user

  if not session_id:
    await consumer.send_error(
      code=WebSocketErrorCodes.INVALID_PARAMETERS, message=AppMsg.WS_SESSION_ID_REQUIRED
    )
    return

  try:
    result = await _leave_early_db(user, session_id, reason)

    if result["success"]:
      await SessionMessageSender.send_session_left_early(consumer, result["actor_user_id"])
    else:
      await consumer.send_error(
        code=WebSocketErrorCodes.SESSION_INVALID_STATE, message=result["message"]
      )

  except Exception as e:
    logger.error(AppMsg.WS_LEAVE_EARLY_HANDLER_ERROR.format(session_id=session_id, error=e))
    await consumer.send_error(
      code=WebSocketErrorCodes.SERVER_ERROR,
      message=AppMsg.WS_LEAVE_EARLY_ERROR_MSG.format(error=str(e)),
    )
