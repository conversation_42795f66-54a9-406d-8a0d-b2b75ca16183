"""
Handler for resuming a paused session.
"""

import logging
from django.utils import timezone
from channels.db import database_sync_to_async
from api.session.models import Session, SessionStatus
from ws.consts.ws_consts import WebSocketErrorCodes
from ws.session.sender.message_sender import SessionMessageSender
from core.utils.app_msg import AppMsg

logger = logging.getLogger(__name__)


@database_sync_to_async
def _resume_session_db(user, session_id):
  """
  Fetches the session, validates its state and pauser, and updates it to 'active'.
  Calculates and updates total pause duration.
  """
  try:
    session = Session.objects.select_related("host", "guest", "paused_by").get(pk=session_id)

    if user.id not in [session.host.id, session.guest.id]:
      return {"success": False, "message": AppMsg.WS_END_SESSION_NOT_PARTICIPANT}

    if session.status != SessionStatus.PAUSED:
      return {
        "success": False,
        "message": AppMsg.WS_RESUME_SESSION_NOT_PAUSED.format(state=session.status),
      }

    if session.paused_by != user:
      return {"success": False, "message": AppMsg.WS_RESUME_SESSION_NOT_PAUSER}

    now = timezone.now()
    if session.paused_at:
      current_pause_duration = now - session.paused_at
      if session.total_pause_duration:
        session.total_pause_duration += current_pause_duration
      else:
        session.total_pause_duration = current_pause_duration

    session.status = SessionStatus.ACTIVE
    session.paused_by = None
    session.paused_at = None
    session.save()

    logger.info(AppMsg.WS_RESUME_SESSION_LOG_SUCCESS.format(session_id=session_id, user_id=user.id))

    return {
      "success": True,
      "session_id": str(session.id),
      "resumed_at": now.isoformat(),
      "actor_user_id": str(user.id),
      "total_pause_duration_seconds": session.total_pause_duration.total_seconds()
      if session.total_pause_duration
      else 0,
    }
  except Session.DoesNotExist:
    return {"success": False, "message": AppMsg.WS_SESSION_NOT_FOUND}
  except Exception as e:
    logger.error(AppMsg.WS_RESUME_SESSION_LOG_ERROR.format(session_id=session_id, error=e))
    return {"success": False, "message": AppMsg.WS_RESUME_SESSION_UNEXPECTED_ERROR}


async def handle_resume_session(consumer, content):
  """Handle resuming a paused session."""
  session_id = content.get("session_id") or getattr(consumer, "session_id", None)
  user = consumer.user

  if not session_id:
    await consumer.send_error(
      code=WebSocketErrorCodes.INVALID_PARAMETERS, message=AppMsg.WS_SESSION_ID_REQUIRED
    )
    return

  try:
    result = await _resume_session_db(user, session_id)

    if result["success"]:
      await SessionMessageSender.send_session_resumed(consumer)
    else:
      await consumer.send_error(
        code=WebSocketErrorCodes.SESSION_INVALID_STATE, message=result["message"]
      )

  except Exception as e:
    logger.error(AppMsg.WS_RESUME_SESSION_HANDLER_ERROR.format(session_id=session_id, error=e))
    await consumer.send_error(
      code=WebSocketErrorCodes.SERVER_ERROR,
      message=AppMsg.WS_RESUME_SESSION_ERROR_MSG.format(error=str(e)),
    )
