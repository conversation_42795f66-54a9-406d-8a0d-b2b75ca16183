"""
Handler for pausing a session.
"""

import logging
from django.utils import timezone
from channels.db import database_sync_to_async
from api.session.models import Session, SessionStatus
from ws.consts.ws_consts import WebSocketErrorCodes
from ws.session.sender.message_sender import Session<PERSON>essageSender
from core.utils.app_msg import AppMsg

logger = logging.getLogger(__name__)


@database_sync_to_async
def _pause_session_db(user, session_id):
  """
  Fetches the session, validates its state, and updates it to 'paused'.
  This function performs the database operations for pausing a session.
  """
  try:
    session = Session.objects.select_related("host", "guest").get(pk=session_id)

    if user.id not in [session.host.id, session.guest.id]:
      return {"success": False, "message": AppMsg.WS_END_SESSION_NOT_PARTICIPANT}

    if session.status != SessionStatus.ACTIVE:
      return {
        "success": False,
        "message": AppMsg.WS_PAUSE_SESSION_INVALID_STATE.format(state=session.status),
      }

    session.status = SessionStatus.PAUSED
    session.paused_by = user
    session.paused_at = timezone.now()
    session.save()

    logger.info(AppMsg.WS_PAUSE_SESSION_LOG_SUCCESS.format(session_id=session_id, user_id=user.id))

    return {
      "success": True,
      "session_id": str(session.id),
      "paused_at": session.paused_at.isoformat(),
      "actor_user_id": str(user.id),
    }
  except Session.DoesNotExist:
    return {"success": False, "message": AppMsg.WS_SESSION_NOT_FOUND}
  except Exception as e:
    logger.error(AppMsg.WS_PAUSE_SESSION_LOG_ERROR.format(session_id=session_id, error=e))
    return {"success": False, "message": AppMsg.WS_PAUSE_SESSION_UNEXPECTED_ERROR}


async def handle_pause_session(consumer, content):
  """Handle pausing a session."""
  session_id = content.get("session_id") or getattr(consumer, "session_id", None)
  user = consumer.user

  if not session_id:
    await consumer.send_error(
      code=WebSocketErrorCodes.INVALID_PARAMETERS, message=AppMsg.WS_SESSION_ID_REQUIRED
    )
    return

  try:
    result = await _pause_session_db(user, session_id)

    if not result["success"]:
      await consumer.send_error(
        code=WebSocketErrorCodes.SESSION_INVALID_STATE, message=result["message"]
      )
      return

    await SessionMessageSender.send_session_paused(
      consumer,
      result["actor_user_id"],
    )

  except Exception as e:
    logger.error(AppMsg.WS_PAUSE_SESSION_HANDLER_ERROR.format(session_id=session_id, error=e))
    await consumer.send_error(
      code=WebSocketErrorCodes.SERVER_ERROR,
      message=AppMsg.WS_PAUSE_SESSION_ERROR_MSG.format(error=str(e)),
    )
