import logging
from django.core.cache import cache
from channels.db import database_sync_to_async
from api.session.models import Session, SessionStatus
from ws.session.db_helpers import update_session_status
from core.utils.app_msg import AppMsg
from ws.session.sender.message_sender import SessionMessageSender

logger = logging.getLogger(__name__)


async def attempt_auto_start_session(consumer_instance):
  """
  Handles the logic for automatically starting a session if both users are connected.
  Uses the consumer_instance to access session_id, user, channel_layer, etc.
  """
  try:
    db_session = await database_sync_to_async(Session.objects.select_related("host", "guest").get)(
      id=consumer_instance.session_id
    )

    host_connected_key = f"session_{consumer_instance.session_id}_host_connected"
    guest_connected_key = f"session_{consumer_instance.session_id}_guest_connected"

    if consumer_instance.user.id == db_session.host_id:
      await database_sync_to_async(cache.set)(host_connected_key, True, timeout=86400)
    elif consumer_instance.user.id == db_session.guest_id:
      await database_sync_to_async(cache.set)(guest_connected_key, True, timeout=86400)

    is_host_connected = await database_sync_to_async(cache.get)(host_connected_key)
    is_guest_connected = await database_sync_to_async(cache.get)(guest_connected_key)

    if is_host_connected and is_guest_connected and db_session.status == SessionStatus.PENDING:
      logger.info(
        AppMsg.WS_AUTO_START_BOTH_CONNECTED.format(session_id=consumer_instance.session_id)
      )
      update_result = await update_session_status(
        consumer_instance.user, consumer_instance.session_id, SessionStatus.ACTIVE
      )
      if update_result and update_result.get("success"):
        logger.info(AppMsg.WS_AUTO_START_SUCCESS.format(session_id=consumer_instance.session_id))

        await SessionMessageSender.send_session_update(
          consumer_instance,
          status=SessionStatus.ACTIVE,
        )
      else:
        logger.error(
          AppMsg.WS_AUTO_START_FAILURE.format(
            session_id=consumer_instance.session_id,
            reason=update_result.get("message") if update_result else "Unknown",
          )
        )
  except Session.DoesNotExist:
    logger.warning(
      AppMsg.WS_AUTO_START_SESSION_NOT_FOUND.format(session_id=consumer_instance.session_id)
    )
  except Exception as e:
    logger.error(
      AppMsg.WS_AUTO_START_ERROR.format(session_id=consumer_instance.session_id, error=str(e))
    )
