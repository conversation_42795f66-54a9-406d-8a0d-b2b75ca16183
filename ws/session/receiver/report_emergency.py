"""
Handler for reporting an emergency during a session.
"""

import logging
from django.utils import timezone
from channels.db import database_sync_to_async
from api.session.models import Session, SessionStatus
from ws.consts.ws_consts import WebSocketErrorCodes, WebSocketMsgTypes
from core.utils.app_msg import AppMsg

logger = logging.getLogger(__name__)


@database_sync_to_async
def _report_emergency_db(user, session_id, details):
  """
  Updates the session to 'emergency', recording the user and details.
  """
  try:
    session = Session.objects.select_related("host", "guest").get(pk=session_id)

    if user.id not in [session.host.id, session.guest.id]:
      return {"success": False, "message": AppMsg.WS_END_SESSION_NOT_PARTICIPANT}

    if session.status in [
      SessionStatus.COMPLETED,
      SessionStatus.TERMINATED_EARLY,
      SessionStatus.EMERGENCY,
    ]:
      return {
        "success": False,
        "message": AppMsg.WS_EMERGENCY_INVALID_STATE.format(state=session.status),
      }

    now = timezone.now()
    session.status = SessionStatus.EMERGENCY
    session.emergency_reported_by = user
    session.emergency_reported_at = now
    session.emergency_details = details
    session.save()

    logger.info(AppMsg.WS_EMERGENCY_LOG_SUCCESS.format(session_id=session_id, user_id=user.id))

    return {
      "success": True,
      "session_id": str(session.id),
      "reported_at": now.isoformat(),
      "actor_user_id": user.id,
    }
  except Session.DoesNotExist:
    return {"success": False, "message": AppMsg.WS_SESSION_NOT_FOUND}
  except Exception as e:
    logger.error(AppMsg.WS_EMERGENCY_LOG_ERROR.format(session_id=session_id, error=e))
    return {"success": False, "message": AppMsg.WS_EMERGENCY_UNEXPECTED_ERROR}


async def handle_report_emergency(consumer, content):
  """Handle reporting an emergency during a session."""
  session_id = content.get("session_id") or getattr(consumer, "session_id", None)
  details = content.get("details", "")
  user = consumer.user

  if not session_id:
    await consumer.send_error(
      code=WebSocketErrorCodes.INVALID_PARAMETERS, message=AppMsg.WS_SESSION_ID_REQUIRED
    )
    return

  try:
    result = await _report_emergency_db(user, session_id, details)

    if result["success"]:
      await consumer.channel_layer.group_send(
        f"session_{session_id}",
        {
          "type": WebSocketMsgTypes.SESSION_EMERGENCY,
          "data": {
            "session_id": result["session_id"],
            "reported_at": result["reported_at"],
            "actor_user_id": result["actor_user_id"],
            "details": details,
            "message": AppMsg.WS_EMERGENCY_MSG,
          },
        },
      )
    else:
      await consumer.send_error(
        code=WebSocketErrorCodes.SESSION_INVALID_STATE, message=result["message"]
      )

  except Exception as e:
    logger.error(AppMsg.WS_EMERGENCY_HANDLER_ERROR.format(session_id=session_id, error=e))
    await consumer.send_error(
      code=WebSocketErrorCodes.SERVER_ERROR,
      message=AppMsg.WS_EMERGENCY_ERROR_MSG.format(error=str(e)),
    )
