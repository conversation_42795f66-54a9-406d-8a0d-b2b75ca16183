from ws.consts.ws_consts import WebSocketMsgTypes


class WebSocketMessageHelper:
  """
  Helper to create standardized message dictionaries for WebSocket communication.
  Uses WebSocketMsgTypes and WebSocketErrorCodes for clarity.
  """

  @staticmethod
  def error(code: str, message: str, details: dict = None) -> dict:
    """Creates an error message payload."""
    # Use the class constants here
    payload = {
      "type": WebSocketMsgTypes.ERROR,
      "code": code,  # Code is passed in, assumed to be from WebSocketErrorCodes
      "message": message,
    }
    if details:
      payload["details"] = details
    return payload

  @staticmethod
  def chat_message(sender_id: str, content: str) -> dict:
    """Creates a chat message payload."""
    # Use the class constants here
    payload = {
      "type": WebSocketMsgTypes.CHAT_MESSAGE,
      "sender": sender_id,
      "message": content,
    }

    return payload

  @staticmethod
  def online_status_update(user_id, is_online, timestamp=None):
    """
    Creates a standardized online status update message.

    Args:
        user_id (str): ID of the user whose online status is changing
        is_online (bool): Whether the user is online or offline
        timestamp (str, optional): ISO format timestamp of when the status changed

    Returns:
        dict: A formatted online status update message
    """
    payload = {
      "type": WebSocketMsgTypes.USER_ONLINE_STATUS,
      "user_id": user_id,
      "is_online": is_online,
    }

    if timestamp:
      payload["timestamp"] = timestamp

    return payload

  @staticmethod
  def info(code: str, message: str, details: dict = None) -> dict:
    """Creates an informational message payload."""
    # Use the class constants here
    payload = {
      "type": WebSocketMsgTypes.INFO,
      "code": code,  # Code is passed in
      "message": message,
    }
    if details:
      payload["details"] = details
    return payload
