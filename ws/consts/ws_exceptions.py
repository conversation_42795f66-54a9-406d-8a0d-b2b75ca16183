# ws/exceptions.py


class WebSocketException(Exception):
  """Base class for custom WebSocket exceptions."""

  pass


class InvalidJsonError(WebSocketException):
  """Raised when incoming text data is not valid JSON."""

  pass


class ProcessingError(WebSocketException):
  """Raised for general errors during message processing."""

  pass


class AuthenticationRequiredError(WebSocketException):
  """Raised when an unauthenticated user attempts an operation requiring auth."""

  pass


class ObjectLookupError(WebSocketException):
  """Raised when a required object (e.g., user by ID) cannot be found."""

  # This can wrap Django's ObjectDoesNotExist or ValueError for PK parsing
  def __init__(self, message, original_exception=None):
    super().__init__(message)
    self.original_exception = original_exception
