class WebSocketMsgTypes:
  """Defines the possible 'type' values for WebSocket messages."""

  ERROR = "error"
  CHAT_MESSAGE = "chat_message"
  USER_ONLINE_STATUS = "user_online_status"
  USER_ONLINE_QUERY = "user_online_query"
  USER_ONLINE_RESPONSE = "user_online_response"
  HEARTBEAT = "heartbeat"
  HEARTBEAT_ACK = "heartbeat_ack"
  INFO = "info"
  CONNECTED = "connected"

  # Session-related message types
  SESSION_START = "session_start"
  SESSION_STARTED = "session_started"
  SESSION_PAUSE = "session_pause"
  SESSION_PAUSED = "session_paused"
  SESSION_RESUME = "session_resume"
  SESSION_RESUMED = "session_resumed"
  SESSION_END = "session_end"
  SESSION_ENDED = "session_ended"
  SESSION_LEAVE_EARLY = "session_leave_early"
  SESSION_LEFT_EARLY = "session_left_early"
  SESSION_EMERGENCY = "session_emergency"
  SESSION_STATUS_UPDATE = "session_status_update"
  SESSION_UPDATED = "session_updated"
  SESSION_INFO = "session_info"
  SESSION_TIMER_UPDATE = "session_timer_update"


class WebSocketErrorCodes:
  """Defines the possible 'code' values for error messages."""

  AUTH_REQUIRED = "AUTH_REQUIRED"
  USER_NOT_FOUND = "USER_NOT_FOUND"
  SERVER_ERROR = "SERVER_ERROR"
  INVALID_JSON = "INVALID_JSON"
  INVALID_MESSAGE_CONTENT = "INVALID_MESSAGE_CONTENT"
  INVALID_MESSAGE_TYPE = "INVALID_MESSAGE_TYPE"
  INVALID_PARAMETERS = "INVALID_PARAMETERS"
  PROCESSING_ERROR = "PROCESSING_ERROR"
  SAVE_ERROR = "SAVE_ERROR"

  # Session-related error codes
  SESSION_NOT_FOUND = "SESSION_NOT_FOUND"
  SESSION_CODE_INVALID = "SESSION_CODE_INVALID"
  SESSION_INVALID_STATE = "SESSION_INVALID_STATE"
  SESSION_UNAUTHORIZED = "SESSION_UNAUTHORIZED"
  SESSION_CODE_GENERATION_FAILED = "SESSION_CODE_GENERATION_FAILED"
  SESSION_VERIFICATION_FAILED = "SESSION_VERIFICATION_FAILED"
