#!/bin/bash

# Ensure environment variables are available to cron
printenv | sed 's/^\(.*\)$/export \1/g' > /app/docker_env.sh
chmod +x /app/docker_env.sh

# Set up cron job
echo "00 03 * * * . /app/docker_env.sh && /usr/local/bin/python /app/commands/run_daily_spark_update.py >> /var/log/cron/cron.log 2>&1" > /etc/cron.d/spark-cron
chmod 0644 /etc/cron.d/spark-cron

# Apply cron job
crontab /etc/cron.d/spark-cron

# Create log file
touch /var/log/cron/cron.log

# Start cron service
service cron start

# Keep container running and tail logs
echo "Cron service started. Tailing logs..."
tail -f /var/log/cron/cron.log
